import discord
from discord.ext import commands
import logging
import os

# Import our custom modules
from database import DatabaseManager
from shard_manager import ShardManager
from shard_api import ShardAPIService

# ========== CONFIGURATION ==========
BOT_TOKEN = os.getenv('BOT_TOKEN')
MONGO_URL = os.getenv('MONGO_URL')
ADMIN_USER_ID = int(os.getenv('ADMIN_USER_ID')) if os.getenv('ADMIN_USER_ID') else None

# Sharding configuration
SHARD_ID = int(os.getenv('SHARD_ID'))
TOTAL_SHARDS = int(os.getenv('TOTAL_SHARDS'))
# ===================================

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Bot setup with necessary intents
intents = discord.Intents.default()
intents.presences = True
intents.members = True
intents.guilds = True
intents.message_content = True
intents.voice_states = True  # Required for voice connection handling

# Create bot instance with improved connection handling
class MyBot(commands.Bot):
    def __init__(self):
        # Configure sharding if multiple shards
        shard_kwargs = {}
        if TOTAL_SHARDS > 1:
            shard_kwargs['shard_id'] = SHARD_ID
            shard_kwargs['shard_count'] = TOTAL_SHARDS

        super().__init__(
            command_prefix='!',
            intents=intents,
            # Add connection parameters to help with timeouts
            heartbeat_timeout=60.0,  # Increase heartbeat timeout
            guild_ready_timeout=10.0,  # Increase guild ready timeout
            # Voice connection settings
            max_messages=None,  # Disable message cache to save memory
            **shard_kwargs
        )
        self.db = DatabaseManager(MONGO_URL)
        self.connection_attempts = 0
        self.max_connection_attempts = 5

        # Initialize shard manager and API service
        self.shard_manager = ShardManager(self, SHARD_ID, TOTAL_SHARDS)
        self.shard_api = ShardAPIService(self, SHARD_ID)

# Initialize the bot
bot = MyBot()

# Make db available at module level for backward compatibility
db = bot.db

# ========== COG LOADING ==========
async def load_cogs():
    """Load all cogs"""
    cogs_to_load = [
        'cogs.core.utilities',
        'cogs.logging.logging_system',
        'cogs.admin.admin_commands',
        'cogs.events.event_handlers',
        'cogs.tasks.background_tasks',
        'cogs.repping.repping',
        'cogs.auto_roling.auto_roling',
        'cogs.vent.vent',
        'cogs.temp_voice.temp_voice',
        'cogs.music.music',
        'cogs.sticky_messages.sticky_messages',
        'cogs.giveaways.giveaways',
        'cogs.dm_support.dm_support',
    ]

    for cog in cogs_to_load:
        try:
            await bot.load_extension(cog)
            logger.info(f"Loaded cog: {cog}")
        except Exception as e:
            logger.error(f"Failed to load cog {cog}: {e}")

# ========== STARTUP FUNCTIONS ==========
async def cleanup_temp_voice_channels():
    """Clean up empty temporary voice channels on startup"""
    try:
        logger.info("Starting temp voice channel cleanup...")
        cleaned_count = 0

        # Get all temp voice channels from database
        temp_channels = bot.db.get_all_active_temp_channels()

        for channel_data in temp_channels:
            try:
                channel = bot.get_channel(channel_data['channel_id'])
                if not channel:
                    # Channel doesn't exist anymore, remove from database
                    bot.db.delete_temp_channel(channel_data['channel_id'])
                    cleaned_count += 1
                    logger.info(f"Removed non-existent temp channel from database: {channel_data['channel_id']}")
                    continue

                # Check if channel is empty
                if len(channel.members) == 0:
                    try:
                        await channel.delete(reason="Empty temporary voice channel cleanup on bot restart")
                        bot.db.delete_temp_channel(channel_data['channel_id'])
                        cleaned_count += 1
                        logger.info(f"Deleted empty temp channel: {channel.name}")
                    except Exception as e:
                        logger.error(f"Error deleting empty temp channel {channel.name}: {e}")

            except Exception as e:
                logger.error(f"Error processing temp channel {channel_data.get('channel_id', 'unknown')}: {e}")

        if cleaned_count > 0:
            logger.info(f"Temp voice cleanup completed: {cleaned_count} channels cleaned up")
        else:
            logger.info("Temp voice cleanup completed: no channels needed cleanup")

    except Exception as e:
        logger.error(f"Error during temp voice cleanup: {e}")

async def check_sticky_messages_on_startup():
    """Check and repost sticky messages after bot restart"""
    try:
        logger.info("Checking sticky messages on startup...")

        for guild in bot.guilds:
            if not bot.db.is_server_licensed(guild.id):
                continue

            # Get all sticky messages for this server
            sticky_messages = bot.db.get_all_sticky_messages(guild.id)

            for sticky_data in sticky_messages:
                try:
                    # Skip if this sticky message is disabled
                    if not sticky_data.get('enabled', True):
                        continue

                    channel = guild.get_channel(sticky_data['channel_id'])
                    if not channel:
                        logger.warning(f"Sticky message channel {sticky_data['channel_id']} not found in guild {guild.name}")
                        continue

                    # Get the sticky messages cog to handle this
                    sticky_cog = bot.get_cog('StickyMessages')
                    if sticky_cog:
                        await sticky_cog.check_and_repost_sticky(channel, sticky_data)

                except Exception as e:
                    logger.error(f"Error checking sticky message in {guild.name}: {e}")

        logger.info("Sticky message startup check completed")

    except Exception as e:
        logger.error(f"Error during sticky message startup check: {e}")

# ========== BOT STARTUP ==========
@bot.event
async def on_ready():
    """Called when the bot is ready"""
    logger.info(f"Shard {SHARD_ID} - Bot is ready! Logged in as {bot.user}")
    logger.info(f"Shard {SHARD_ID} - Connected to {len(bot.guilds)} guilds")

    # Start shard manager heartbeat
    await bot.shard_manager.start_heartbeat()

    # Start shard API service
    bot.shard_api.start_server()
    
    # Connect to database
    try:
        bot.db.connect()
    except Exception as e:
        logger.error(f"Failed to connect to MongoDB: {e}")
        return

    # Load cogs
    await load_cogs()

    # Sync slash commands
    try:
        synced = await bot.tree.sync()
        logger.info(f"Synced {len(synced)} commands")
    except Exception as e:
        logger.error(f"Failed to sync commands: {e}")

    # Cleanup temp voice channels on startup
    await cleanup_temp_voice_channels()

    # Check and repost sticky messages after restart
    await check_sticky_messages_on_startup()

    logger.info("Bot startup complete - web dashboard should be started separately")

@bot.event
async def setup_hook():
    """Called when the bot is first started"""
    # Import views from cogs
    try:
        # Gender verification removed
        from cogs.temp_voice.views import TempVoiceView
        from cogs.giveaways.views import PersistentGiveawayView
        
        # Add persistent views
        bot.add_view(TempVoiceView(bot.db))
        bot.add_view(PersistentGiveawayView(bot.db))
        
        logger.info("Added persistent views")
    except Exception as e:
        logger.error(f"Error adding persistent views: {e}")

if __name__ == "__main__":
    if BOT_TOKEN == "YOUR_BOT_TOKEN_HERE":
        print("⚠️  Please configure your bot token in the script!")
        print("You need to set:")
        print("- BOT_TOKEN: Your Discord bot token")
        print("- MONGO_URL: Your MongoDB connection string")
        print("- ADMIN_USER_ID: Discord user ID for admin commands")
    else:
        try:
            logger.info("Starting ryzuo Bot - Multi-Server License System")
            bot.run(BOT_TOKEN)
        except discord.LoginFailure:
            logger.error("Invalid bot token provided")
        except KeyboardInterrupt:
            logger.info("Bot shutdown requested")
        except Exception as e:
            logger.error(f"Error starting bot: {e}")
        finally:
            # Ensure database connection is closed
            try:
                db.disconnect()
            except:
                pass
