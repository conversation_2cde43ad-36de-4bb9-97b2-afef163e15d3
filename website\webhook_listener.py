import stripe
import logging
from flask import request, jsonify
from database import DatabaseManager
from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv()

# Initialize logging
logger = logging.getLogger(__name__)

# Initialize the database manager
mongo_url = os.getenv("MONGO_URL")

db = DatabaseManager(mongo_url)

# Stripe webhook handler
def handle_stripe_webhook():
    payload = request.get_data(as_text=True)
    sig_header = request.headers.get('Stripe-Signature')

    try:
        event = stripe.Webhook.construct_event(
            payload, sig_header, os.getenv("STRIPE_WEBHOOK_SECRET")
        )
    except ValueError as e:
        # Invalid payload
        logger.error(f"Invalid payload: {e}")
        return jsonify({'success': False, 'error': 'Invalid payload'}), 400
    except stripe.error.SignatureVerificationError as e:
        # Invalid signature
        logger.error(f"Invalid signature: {e}")
        return jsonify({'success': False, 'error': 'Invalid signature'}), 400

    # Handle the event
    if event['type'] == 'customer.subscription.deleted':
        subscription = event['data']['object']
        stripe_subscription_id = subscription['id']

        # Revoke premium access
        db.update_subscription_status(stripe_subscription_id, 'cancelled')
        logger.info(f"Subscription {stripe_subscription_id} cancelled and premium access revoked.")

    return jsonify({'success': True})
