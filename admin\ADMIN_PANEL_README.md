# ryzuo Bot Admin Panel

A comprehensive administrative interface for managing the ryzuo Discord bot system. The admin panel provides full control over users, servers, subscriptions, features, and system processes.

## Features

### 🔐 Security & Authentication
- **Discord OAuth Authentication**: Secure login using Discord OAuth
- **JWT-based Sessions**: Encrypted session management with expiration
- **CSRF Protection**: All forms protected against cross-site request forgery
- **IP Blocking**: Block malicious IP addresses
- **Command Blocking**: Block specific commands for users
- **Admin-only Access**: Restricted to configured admin user ID

### 👥 User Management
- **User Overview**: View all registered users with pagination and search
- **Subscription Management**: Add/remove premium subscriptions manually
- **Account Control**: Enable/disable user accounts
- **Complete User Deletion**: Remove users and all associated data
- **User Filtering**: Filter by subscription status and account status

### 🖥️ Server Management
- **Server Overview**: View all servers using the bot
- **Server Details**: Detailed information including logs and statistics
- **Configuration Reset**: Reset server configurations to defaults
- **Server Deletion**: Complete server data removal
- **License Management**: View server license information

### ⚙️ Process Management
- **Shard Monitoring**: View status of all bot shards
- **Process Restart**: Restart individual shards or the website
- **Console Logs**: View real-time logs from processes
- **Health Monitoring**: System health and uptime tracking

### 🎛️ Feature Toggles
- **Global Feature Control**: Enable/disable features across the platform
- **Premium Feature Management**: Control which features require subscriptions
- **Real-time Updates**: Changes take effect immediately

### 💰 Pricing Management
- **Subscription Pricing**: Configure prices for different tiers
- **Stripe Integration**: Manage Stripe price IDs
- **Pricing History**: Track pricing changes over time

### 📊 Analytics & Monitoring
- **System Statistics**: User counts, server counts, subscription metrics
- **Admin Logs**: Comprehensive audit trail of all admin actions
- **Activity Monitoring**: Track system activity and performance
- **Real-time Updates**: Dashboard updates automatically

### 🔧 Environment Management
- **Configuration Control**: Manage environment variables for bot and website
- **Secure Storage**: Environment variables stored securely in database
- **Change Tracking**: Track all configuration changes

### 📢 Communication
- **Global Notifications**: Send notifications to all users
- **Notification Types**: Support for info, warning, success, and error messages
- **Targeted Messaging**: Send notifications to specific user groups

## Installation & Setup

### Prerequisites
- Python 3.8+
- MongoDB database
- Discord application with OAuth2 configured
- Flask and required Python packages

### Environment Variables
Create a `.env` file with the following variables:

```env
# Database
MONGO_URL=mongodb://localhost:27017/ryzuo

# Security
SECRET_KEY=your-super-secret-key-here
ADMIN_USER_ID=your-discord-user-id

# Discord OAuth
DISCORD_CLIENT_ID=your-discord-client-id
DISCORD_CLIENT_SECRET=your-discord-client-secret

# Admin Panel
ADMIN_PORT=5001
ADMIN_DOMAIN=https://admin.leakin.cc

# Flask Environment
FLASK_ENV=production
```

### Installation Steps

1. **Install Dependencies**
```bash
pip install -r requirements.txt
```

2. **Configure Environment**
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Set Up Database**
```bash
# MongoDB should be running and accessible
# The admin panel will create necessary collections automatically
```

4. **Configure Discord OAuth**
- Go to Discord Developer Portal
- Create a new application or use existing
- Add redirect URI: `https://admin.leakin.cc/oauth/callback`
- Copy Client ID and Client Secret to `.env`

5. **Start the Admin Panel**
```bash
python admin_panel.py
```

The admin panel will be available at `http://localhost:5001` (or your configured domain).

## Usage

### First Login
1. Navigate to the admin panel URL
2. Click "Login with Discord"
3. Authorize the application
4. You'll be redirected to the dashboard

### Dashboard Overview
The dashboard provides:
- System statistics (users, servers, subscriptions)
- Quick action buttons
- Feature toggle overview
- Recent admin actions
- System health status

### User Management
- **View Users**: Browse all users with search and filtering
- **Add Subscription**: Manually add premium subscriptions
- **Disable Account**: Temporarily disable user accounts
- **Delete User**: Permanently remove user and all data

### Server Management
- **View Servers**: Browse all servers with search
- **Server Details**: View detailed server information
- **Reset Config**: Reset server to default configuration
- **Delete Server**: Remove server and all associated data

### Process Management
- **Monitor Shards**: View status of all bot shards
- **Restart Processes**: Restart individual shards or website
- **View Logs**: Access real-time console logs
- **Health Check**: Monitor system health and uptime

### Feature Toggles
- **Global Control**: Enable/disable features globally
- **Premium Features**: Control which features require subscriptions
- **Real-time Updates**: Changes are applied immediately

### Security Features
- **IP Blocking**: Block malicious IP addresses
- **Command Blocking**: Block specific commands for users
- **Audit Logging**: All admin actions are logged

## API Endpoints

### Authentication
- `GET /` - Redirect to login or dashboard
- `GET /login` - Login page
- `GET /oauth/callback` - Discord OAuth callback
- `GET /logout` - Logout and clear session

### User Management
- `GET /users` - User management page
- `POST /api/user/<user_id>/subscription` - Manage user subscription
- `POST /api/user/<user_id>/disable` - Disable user account
- `POST /api/user/<user_id>/enable` - Enable user account
- `DELETE /api/user/<user_id>/delete` - Delete user completely

### Server Management
- `GET /servers` - Server management page
- `GET /api/admin/server/<server_id>/details` - Get server details
- `POST /api/admin/server/<server_id>/reset` - Reset server config
- `DELETE /api/admin/server/<server_id>/delete` - Delete server

### Process Management
- `GET /processes` - Process management page
- `POST /api/admin/process/<process_name>/restart` - Restart process
- `GET /api/admin/process/<process_name>/logs` - Get process logs

### Feature & Configuration
- `GET|POST /api/admin/feature-toggles` - Manage feature toggles
- `GET /environment` - Environment management page
- `POST /api/admin/environment/<config_type>` - Update environment config
- `GET /pricing` - Pricing management page
- `POST /api/admin/pricing` - Update pricing configuration

### Security
- `GET /security` - Security management page
- `POST /api/admin/block-ip` - Block IP address
- `POST /api/admin/unblock-ip` - Unblock IP address
- `POST /api/admin/block-commands` - Block user commands
- `POST /api/admin/unblock-commands` - Unblock user commands

### Monitoring
- `GET /logs` - Admin logs page
- `GET /api/admin/statistics` - Get system statistics
- `POST /api/admin/send-notification` - Send global notification

## Security Considerations

### Authentication
- Admin access is restricted to a single Discord user ID
- JWT tokens expire after 8 hours
- Sessions are invalidated on logout
- CSRF protection on all forms

### Data Protection
- All admin actions are logged with timestamps
- IP addresses are tracked for security
- Environment variables are encrypted in database
- Sensitive operations require confirmation

### Network Security
- Admin panel runs on separate port from main website
- Designed to run behind Cloudflare tunnels
- HTTPS strongly recommended for production

## Deployment

### Docker Deployment
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 5001

CMD ["python", "admin_panel.py"]
```

### Systemd Service
```ini
[Unit]
Description=ryzuo Bot Admin Panel
After=network.target

[Service]
Type=simple
User=ryzuo
WorkingDirectory=/path/to/ryzuo/website
ExecStart=/usr/bin/python3 admin_panel.py
Restart=always
RestartSec=10
Environment=FLASK_ENV=production

[Install]
WantedBy=multi-user.target
```

### Nginx Configuration
```nginx
server {
    listen 80;
    server_name admin.leakin.cc;
    
    location / {
        proxy_pass http://127.0.0.1:5001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## Troubleshooting

### Common Issues

1. **Login Fails**
   - Check Discord OAuth configuration
   - Verify redirect URI matches exactly
   - Ensure ADMIN_USER_ID is correct

2. **Database Connection Issues**
   - Verify MongoDB is running
   - Check MONGO_URL in environment
   - Ensure database permissions are correct

3. **Feature Toggles Not Working**
   - Check database connection
   - Verify feature toggle collection exists
   - Check admin logs for errors

4. **Process Management Issues**
   - Ensure process status collection exists
   - Check shard communication setup
   - Verify process names are correct

### Logs and Debugging
- Admin actions are logged to `ryzuo-admin-logs` collection
- Flask logs are available in console output
- Enable debug mode for development: `FLASK_ENV=development`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This admin panel is part of the ryzuo Bot project and follows the same licensing terms.