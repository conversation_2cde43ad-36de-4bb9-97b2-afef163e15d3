"""
Music System for Discord Bot
Handles voice connections, YouTube integration, and playlist management
"""

import os
import asyncio
import logging
import discord
import aiohttp
from typing import Optional, Dict, List, Any
from urllib.parse import quote
import re
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class LoopMode(Enum):
    OFF = "off"
    SONG = "song"
    PLAYLIST = "playlist"

@dataclass
class Song:
    title: str
    url: str
    duration: str
    thumbnail: str
    requested_by: discord.Member
    youtube_id: str

class MusicManager:
    def __init__(self, bot):
        self.bot = bot
        self.youtube_api_key = os.getenv('YOUTUBE_API_KEY')
        self.voice_clients: Dict[int, discord.VoiceClient] = {}
        self.playlists: Dict[int, List[Song]] = {}
        self.current_songs: Dict[int, Song] = {}
        self.loop_modes: Dict[int, LoopMode] = {}
        self.is_playing: Dict[int, bool] = {}
        self.is_paused: Dict[int, bool] = {}
        self.disconnect_tasks: Dict[int, asyncio.Task] = {}
        
        if not self.youtube_api_key:
            logger.error("YouTube API key not found! Music system will not work.")
        else:
            logger.info("Music system initialized with YouTube API")

    async def join_voice_channel(self, channel: discord.VoiceChannel, guild_id: int) -> bool:
        """Join a voice channel and auto-deafen"""
        try:
            # Check if already connected to this channel
            if guild_id in self.voice_clients:
                voice_client = self.voice_clients[guild_id]
                if voice_client.channel == channel and voice_client.is_connected():
                    return True

                # If playing music, don't allow channel switching
                if self.is_playing.get(guild_id, False):
                    return False

                # Clean up old connection if it exists but is not connected
                if not voice_client.is_connected():
                    try:
                        await voice_client.disconnect(force=True)
                    except:
                        pass
                    del self.voice_clients[guild_id]
                else:
                    # Move to new channel
                    try:
                        await voice_client.move_to(channel)
                    except Exception as e:
                        logger.error(f"Failed to move to channel: {e}")
                        return False

            # Connect to channel if not already connected
            if guild_id not in self.voice_clients:
                # Try connecting with retry logic
                max_retries = 3
                retry_delay = 2

                for attempt in range(max_retries):
                    try:
                        logger.info(f"Attempting to connect to {channel.name} (attempt {attempt + 1}/{max_retries})")

                        # Use shorter timeout and no reconnect to avoid hanging
                        voice_client = await asyncio.wait_for(
                            channel.connect(timeout=15.0, reconnect=False),
                            timeout=20.0
                        )

                        # Verify connection was successful
                        if voice_client and voice_client.is_connected():
                            self.voice_clients[guild_id] = voice_client
                            logger.info(f"Successfully connected to {channel.name}")

                            # Initialize guild data
                            if guild_id not in self.playlists:
                                self.playlists[guild_id] = []
                            if guild_id not in self.loop_modes:
                                self.loop_modes[guild_id] = LoopMode.OFF
                            if guild_id not in self.is_playing:
                                self.is_playing[guild_id] = False
                            if guild_id not in self.is_paused:
                                self.is_paused[guild_id] = False
                            break
                        else:
                            logger.warning(f"Voice client created but not connected properly")
                            if voice_client:
                                try:
                                    await voice_client.disconnect(force=True)
                                except:
                                    pass
                            raise Exception("Voice client not properly connected")

                    except asyncio.TimeoutError:
                        logger.warning(f"Timeout connecting to voice channel {channel.name} (attempt {attempt + 1})")
                        if attempt < max_retries - 1:
                            await asyncio.sleep(retry_delay)
                            retry_delay *= 1.5  # Exponential backoff
                        else:
                            logger.error(f"Failed to connect to {channel.name} after {max_retries} attempts")
                            return False
                    except Exception as e:
                        logger.warning(f"Failed to connect to voice channel (attempt {attempt + 1}): {e}")
                        if attempt < max_retries - 1:
                            await asyncio.sleep(retry_delay)
                            retry_delay *= 1.5  # Exponential backoff
                        else:
                            logger.error(f"Failed to connect to {channel.name} after {max_retries} attempts: {e}")
                            return False

            # Auto-deafen the bot
            try:
                voice_client = self.voice_clients[guild_id]
                await voice_client.guild.change_voice_state(
                    channel=channel,
                    self_mute=False,
                    self_deaf=True
                )
                logger.info(f"Joined voice channel {channel.name} in {channel.guild.name} and auto-deafened")
            except Exception as e:
                logger.warning(f"Failed to auto-deafen in {channel.name}: {e}")
                # Continue anyway, deafening is not critical

            # Cancel any pending disconnect task
            if guild_id in self.disconnect_tasks:
                self.disconnect_tasks[guild_id].cancel()
                del self.disconnect_tasks[guild_id]

            return True

        except Exception as e:
            logger.error(f"Error joining voice channel: {e}")
            # Clean up any partial connection
            if guild_id in self.voice_clients:
                try:
                    await self.voice_clients[guild_id].disconnect(force=True)
                except:
                    pass
                del self.voice_clients[guild_id]
            return False

    async def leave_voice_channel(self, guild_id: int, reason: str = "Manual disconnect"):
        """Leave voice channel and cleanup"""
        try:
            if guild_id in self.voice_clients:
                voice_client = self.voice_clients[guild_id]

                # Stop playing if currently playing
                try:
                    if voice_client.is_playing() or voice_client.is_paused():
                        voice_client.stop()
                except:
                    pass  # Ignore errors when stopping

                # Disconnect with force to handle stuck connections
                try:
                    await voice_client.disconnect(force=True)
                except:
                    pass  # Ignore disconnect errors

                del self.voice_clients[guild_id]

                # Clear guild data
                self.playlists[guild_id] = []
                self.current_songs.pop(guild_id, None)
                self.is_playing[guild_id] = False
                self.is_paused[guild_id] = False

                # Cancel disconnect task if exists
                if guild_id in self.disconnect_tasks:
                    self.disconnect_tasks[guild_id].cancel()
                    del self.disconnect_tasks[guild_id]

                logger.info(f"Left voice channel in guild {guild_id}: {reason}")

        except Exception as e:
            logger.error(f"Error leaving voice channel: {e}")
            # Force cleanup even if disconnect failed
            if guild_id in self.voice_clients:
                del self.voice_clients[guild_id]

    async def search_youtube(self, query: str) -> Optional[Song]:
        """Search YouTube for a song"""
        if not self.youtube_api_key:
            return None
            
        try:
            search_url = "https://www.googleapis.com/youtube/v3/search"
            params = {
                'part': 'snippet',
                'q': query,
                'type': 'video',
                'maxResults': 1,
                'key': self.youtube_api_key,
                'videoCategoryId': '10'  # Music category
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(search_url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        items = data.get('items', [])
                        
                        if items:
                            video = items[0]
                            video_id = video['id']['videoId']
                            snippet = video['snippet']
                            
                            # Get video details for duration
                            duration = await self._get_video_duration(video_id)
                            
                            return Song(
                                title=snippet['title'],
                                url=f"https://www.youtube.com/watch?v={video_id}",
                                duration=duration,
                                thumbnail=snippet['thumbnails']['medium']['url'],
                                requested_by=None,  # Will be set when adding to playlist
                                youtube_id=video_id
                            )
                    else:
                        logger.error(f"YouTube API search failed with status {response.status}")
                        
        except Exception as e:
            logger.error(f"Error searching YouTube: {e}")
            
        return None

    async def _get_video_duration(self, video_id: str) -> str:
        """Get video duration from YouTube API"""
        try:
            details_url = "https://www.googleapis.com/youtube/v3/videos"
            params = {
                'part': 'contentDetails',
                'id': video_id,
                'key': self.youtube_api_key
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(details_url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        items = data.get('items', [])
                        
                        if items:
                            duration_iso = items[0]['contentDetails']['duration']
                            return self._parse_duration(duration_iso)
                            
        except Exception as e:
            logger.error(f"Error getting video duration: {e}")
            
        return "Unknown"

    def _parse_duration(self, duration_iso: str) -> str:
        """Parse ISO 8601 duration to readable format"""
        try:
            # Remove PT prefix
            duration = duration_iso[2:]
            
            hours = 0
            minutes = 0
            seconds = 0
            
            # Extract hours
            if 'H' in duration:
                hours = int(duration.split('H')[0])
                duration = duration.split('H')[1]
                
            # Extract minutes
            if 'M' in duration:
                minutes = int(duration.split('M')[0])
                duration = duration.split('M')[1]
                
            # Extract seconds
            if 'S' in duration:
                seconds = int(duration.split('S')[0])
                
            # Format duration
            if hours > 0:
                return f"{hours}:{minutes:02d}:{seconds:02d}"
            else:
                return f"{minutes}:{seconds:02d}"
                
        except Exception as e:
            logger.error(f"Error parsing duration {duration_iso}: {e}")
            return "Unknown"

    async def add_to_playlist(self, guild_id: int, song: Song, position: str = "end") -> bool:
        """Add song to playlist"""
        try:
            if guild_id not in self.playlists:
                self.playlists[guild_id] = []
                
            if position == "top":
                self.playlists[guild_id].insert(0, song)
            else:
                self.playlists[guild_id].append(song)
                
            return True
            
        except Exception as e:
            logger.error(f"Error adding song to playlist: {e}")
            return False

    def get_playlist(self, guild_id: int) -> List[Song]:
        """Get current playlist"""
        return self.playlists.get(guild_id, [])

    def shuffle_playlist(self, guild_id: int) -> bool:
        """Shuffle the playlist"""
        try:
            if guild_id in self.playlists and self.playlists[guild_id]:
                import random
                random.shuffle(self.playlists[guild_id])
                return True
        except Exception as e:
            logger.error(f"Error shuffling playlist: {e}")
        return False

    def set_loop_mode(self, guild_id: int, mode: LoopMode):
        """Set loop mode for guild"""
        self.loop_modes[guild_id] = mode

    def get_loop_mode(self, guild_id: int) -> LoopMode:
        """Get current loop mode"""
        return self.loop_modes.get(guild_id, LoopMode.OFF)

    async def start_disconnect_timer(self, guild_id: int):
        """Start 30-second disconnect timer"""
        try:
            # Cancel existing timer
            if guild_id in self.disconnect_tasks:
                self.disconnect_tasks[guild_id].cancel()
                
            # Create new timer
            async def disconnect_after_delay():
                await asyncio.sleep(30)
                
                # Check if channel is still empty
                if guild_id in self.voice_clients:
                    voice_client = self.voice_clients[guild_id]
                    if voice_client.channel:
                        # Count non-bot members
                        human_members = [m for m in voice_client.channel.members if not m.bot]
                        if len(human_members) == 0:
                            await self.leave_voice_channel(guild_id, "No users in voice channel")
                            
            self.disconnect_tasks[guild_id] = asyncio.create_task(disconnect_after_delay())
            
        except Exception as e:
            logger.error(f"Error starting disconnect timer: {e}")

    def is_connected(self, guild_id: int) -> bool:
        """Check if bot is connected to voice channel"""
        if guild_id not in self.voice_clients:
            return False

        voice_client = self.voice_clients[guild_id]

        # Check if voice client is still valid and connected
        try:
            return voice_client.is_connected() and voice_client.channel is not None
        except:
            # If there's any error checking connection, assume disconnected
            return False

    def get_current_song(self, guild_id: int) -> Optional[Song]:
        """Get currently playing song"""
        return self.current_songs.get(guild_id)

    def is_guild_playing(self, guild_id: int) -> bool:
        """Check if guild is currently playing music"""
        return self.is_playing.get(guild_id, False)

    def is_guild_paused(self, guild_id: int) -> bool:
        """Check if guild music is paused"""
        return self.is_paused.get(guild_id, False)

    async def cleanup_disconnected_clients(self):
        """Clean up disconnected voice clients"""
        try:
            disconnected_guilds = []

            for guild_id, voice_client in self.voice_clients.items():
                if not voice_client.is_connected():
                    disconnected_guilds.append(guild_id)

            for guild_id in disconnected_guilds:
                logger.info(f"Cleaning up disconnected voice client for guild {guild_id}")
                await self.leave_voice_channel(guild_id, "Connection lost")

        except Exception as e:
            logger.error(f"Error cleaning up disconnected clients: {e}")

    def get_voice_client_status(self, guild_id: int) -> str:
        """Get voice client status for debugging"""
        if guild_id not in self.voice_clients:
            return "Not connected"

        voice_client = self.voice_clients[guild_id]

        if not voice_client.is_connected():
            return "Disconnected"
        elif voice_client.is_playing():
            return "Playing"
        elif voice_client.is_paused():
            return "Paused"
        else:
            return "Connected (idle)"

    async def play_next(self, guild_id: int):
        """Play next song in playlist"""
        try:
            if guild_id not in self.voice_clients:
                return

            voice_client = self.voice_clients[guild_id]

            # Handle loop modes
            loop_mode = self.get_loop_mode(guild_id)
            current_song = self.current_songs.get(guild_id)

            if loop_mode == LoopMode.SONG and current_song:
                # Repeat current song
                next_song = current_song
            elif loop_mode == LoopMode.PLAYLIST and current_song:
                # Add current song back to end of playlist
                self.playlists[guild_id].append(current_song)
                # Get next song from playlist
                if self.playlists[guild_id]:
                    next_song = self.playlists[guild_id].pop(0)
                else:
                    next_song = None
            else:
                # Normal mode - get next song
                if self.playlists[guild_id]:
                    next_song = self.playlists[guild_id].pop(0)
                else:
                    next_song = None

            if next_song:
                await self._play_song(guild_id, next_song)
            else:
                # No more songs
                self.is_playing[guild_id] = False
                self.current_songs.pop(guild_id, None)

                # Start disconnect timer
                await self.start_disconnect_timer(guild_id)

        except Exception as e:
            logger.error(f"Error playing next song: {e}")

    async def _play_song(self, guild_id: int, song: Song):
        """Play a specific song"""
        try:
            if guild_id not in self.voice_clients:
                return False

            voice_client = self.voice_clients[guild_id]

            # Check if voice client is still connected
            if not voice_client.is_connected():
                logger.warning(f"Voice client disconnected for guild {guild_id}")
                await self.leave_voice_channel(guild_id, "Voice client disconnected")
                return False

            # Create audio source using yt-dlp
            try:
                import yt_dlp

                ydl_opts = {
                    'format': 'bestaudio/best',
                    'quiet': True,
                    'no_warnings': True,
                    'extractaudio': True,
                    'audioformat': 'mp3',
                    'outtmpl': '%(extractor)s-%(id)s-%(title)s.%(ext)s',
                    'restrictfilenames': True,
                    'noplaylist': True,
                    'nocheckcertificate': True,
                    'ignoreerrors': False,
                    'logtostderr': False,
                    'age_limit': None,
                    'default_search': 'auto',
                    'socket_timeout': 30,
                }

                # Extract info with timeout
                loop = asyncio.get_event_loop()
                with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                    info = await loop.run_in_executor(
                        None,
                        lambda: ydl.extract_info(song.url, download=False)
                    )

                if not info or 'url' not in info:
                    logger.error(f"No audio URL found for {song.title}")
                    return False

                audio_url = info['url']

            except Exception as e:
                logger.error(f"Error extracting audio URL for {song.title}: {e}")
                return False

            # Create audio source
            try:
                ffmpeg_options = {
                    'before_options': '-reconnect 1 -reconnect_streamed 1 -reconnect_delay_max 5',
                    'options': '-vn -bufsize 512k'
                }

                audio_source = discord.FFmpegPCMAudio(audio_url, **ffmpeg_options)

            except Exception as e:
                logger.error(f"Error creating audio source for {song.title}: {e}")
                return False

            # Set current song
            self.current_songs[guild_id] = song
            self.is_playing[guild_id] = True
            self.is_paused[guild_id] = False

            # Play the song
            def after_playing(error):
                if error:
                    logger.error(f"Player error for {song.title}: {error}")
                else:
                    logger.info(f"Finished playing: {song.title}")

                # Schedule next song
                try:
                    asyncio.run_coroutine_threadsafe(
                        self.play_next(guild_id),
                        self.bot.loop
                    )
                except Exception as e:
                    logger.error(f"Error scheduling next song: {e}")

            try:
                voice_client.play(audio_source, after=after_playing)
                logger.info(f"Started playing: {song.title}")
                return True
            except Exception as e:
                logger.error(f"Error starting playback for {song.title}: {e}")
                return False

        except Exception as e:
            logger.error(f"Error playing song {song.title}: {e}")
            # Reset state on error
            self.is_playing[guild_id] = False
            self.current_songs.pop(guild_id, None)
            return False

    async def play_song(self, guild_id: int, song: Song) -> bool:
        """Add song to playlist and play if nothing is playing"""
        try:
            # Add to playlist
            await self.add_to_playlist(guild_id, song)

            # If nothing is playing, start playing
            if not self.is_playing.get(guild_id, False):
                # Get the song we just added
                if self.playlists[guild_id]:
                    next_song = self.playlists[guild_id].pop(0)
                    return await self._play_song(guild_id, next_song)

            return True

        except Exception as e:
            logger.error(f"Error in play_song: {e}")
            return False

    async def skip_song(self, guild_id: int) -> bool:
        """Skip current song"""
        try:
            if guild_id in self.voice_clients:
                voice_client = self.voice_clients[guild_id]
                if voice_client.is_playing():
                    voice_client.stop()  # This will trigger the after callback
                    return True
        except Exception as e:
            logger.error(f"Error skipping song: {e}")
        return False

    async def pause_song(self, guild_id: int) -> bool:
        """Pause current song"""
        try:
            if guild_id in self.voice_clients:
                voice_client = self.voice_clients[guild_id]
                if voice_client.is_playing():
                    voice_client.pause()
                    self.is_paused[guild_id] = True
                    return True
        except Exception as e:
            logger.error(f"Error pausing song: {e}")
        return False

    async def unpause_song(self, guild_id: int) -> bool:
        """Resume current song"""
        try:
            if guild_id in self.voice_clients:
                voice_client = self.voice_clients[guild_id]
                if voice_client.is_paused():
                    voice_client.resume()
                    self.is_paused[guild_id] = False
                    return True
        except Exception as e:
            logger.error(f"Error resuming song: {e}")
        return False

    async def handle_voice_state_update(self, member, before, after):
        """Handle voice state updates for auto-disconnect"""
        try:
            # Only handle if bot is connected to voice
            guild_id = member.guild.id
            if guild_id not in self.voice_clients:
                return

            voice_client = self.voice_clients[guild_id]
            bot_channel = voice_client.channel

            if not bot_channel:
                return

            # Check if someone left the bot's channel
            if before.channel == bot_channel and after.channel != bot_channel:
                # Count remaining non-bot members
                human_members = [m for m in bot_channel.members if not m.bot]

                if len(human_members) == 0:
                    # Start disconnect timer
                    await self.start_disconnect_timer(guild_id)

            # Check if someone joined the bot's channel
            elif after.channel == bot_channel and before.channel != bot_channel:
                # Cancel disconnect timer if someone joined
                if guild_id in self.disconnect_tasks:
                    self.disconnect_tasks[guild_id].cancel()
                    del self.disconnect_tasks[guild_id]

        except Exception as e:
            logger.error(f"Error handling voice state update: {e}")
