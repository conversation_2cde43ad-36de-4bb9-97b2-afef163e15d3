<!-- Unified Modern Header -->
<header class="header" id="header">
    <nav class="nav">
        {% set is_server_page = request.endpoint in ['dashboard', 'configure_repping', 'configure_auto_roling', 'configure_vent', 'configure_tempvoice', 'configure_sticky_messages', 'configure_dm_support', 'configure_gender_verification', 'configure_logs', 'settings', 'site_logs', 'stats', 'giveaways'] %}
        {% set is_user_settings = request.endpoint == 'user_settings' %}
        {% set logo_url = url_for('select_server') if (is_server_page or is_user_settings) else url_for('index') %}

        <a href="{{ logo_url }}" class="logo">
            <img src="{{ url_for('static', filename='logo.png') }}" alt="ryzuo Bot" class="logo-img">
        </a>

        <ul class="nav-links">
            <li><a href="{{ url_for('shop') }}">Purchase</a></li>
            <li><a href="{{ url_for('index') }}#features">Commands</a></li>
            <li><a href="{{ url_for('status') }}">Status</a></li>
            <li><a href="{{ url_for('docs') }}">Docs</a></li>
        </ul>

        <!-- Right side content - conditional based on login status -->
        {% set is_dashboard_page = request.endpoint in ['dashboard', 'configure_repping', 'configure_auto_roling', 'configure_vent', 'configure_tempvoice', 'configure_sticky_messages', 'configure_dm_support', 'configure_gender_verification', 'configure_logs', 'settings', 'site_logs', 'stats', 'giveaways', 'user_settings'] %}
        {% if 'discord_user' in session %}
        <!-- User is signed in - show user dropdown on ALL pages -->
        <div class="user-nav">
            <!-- Show notifications when user is logged in -->
            <div class="nav-item notification-link">
                <a href="{{ url_for('notifications') }}" class="notification-bell nav-link">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge" id="notificationBadge" style="display: none;"></span>
                </a>
            </div>

            <!-- User dropdown - always show when signed in -->
            <div class="user-dropdown">
                <button class="user-dropdown-toggle" id="userDropdownToggle">
                    {% if session.discord_user.avatar %}
                    <img src="https://cdn.discordapp.com/avatars/{{ session.discord_user.id }}/{{ session.discord_user.avatar }}.png?size=64"
                         alt="User Avatar" class="user-avatar">
                    {% else %}
                    <div class="user-avatar user-avatar-default">
                        <i class="fas fa-user"></i>
                    </div>
                    {% endif %}
                    <span class="user-name">{{ session.discord_user.username }}</span>
                    <i class="fas fa-chevron-down user-dropdown-arrow"></i>
                </button>
                <div class="user-dropdown-menu" id="userDropdownMenu">
                    <a href="{{ url_for('select_server') }}" class="dropdown-item">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a href="{{ url_for('user_settings') }}" class="dropdown-item">
                        <i class="fas fa-cog"></i>Settings
                    </a>
                    <div class="dropdown-divider"></div>
                    <a href="{{ url_for('logout') }}" class="dropdown-item text-danger">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </div>
            </div>
        </div>
        {% else %}
        <!-- User not signed in - show login button -->
        <a href="{{ url_for('login') }}" class="nav-cta">Sign In</a>
        {% endif %}

        <button class="mobile-menu-toggle" id="mobileMenuToggle" onclick="toggleMobileMenu()">
            <i class="fas fa-bars"></i>
        </button>
    </nav>
</header>

<!-- Mobile Menu -->
<div class="mobile-menu" id="mobileMenu">
    <button class="mobile-menu-close" id="mobileMenuClose" onclick="closeMobileMenuInline()">
        <i class="fas fa-times"></i>
    </button>
    <div class="mobile-menu-links">
        <a href="{{ url_for('shop') }}">Purchase</a>
        <a href="{{ url_for('index') }}#features">Commands</a>
        <a href="{{ url_for('status') }}">Status</a>
        <a href="{{ url_for('docs') }}">Docs</a>

        <!-- User menu items removed - they appear in the main navbar -->
    </div>

    {% if not 'discord_user' in session %}
    <a href="{{ url_for('login') }}" class="mobile-menu-cta">Sign In</a>
    {% endif %}
</div>

<style>
    /* Header */
    .header {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 9999;
        background: rgba(10, 10, 15, 0.9);
        backdrop-filter: blur(20px);
        border-bottom: 1px solid rgba(88, 101, 242, 0.3);
        box-shadow: 0 0 20px rgba(88, 101, 242, 0.1);
        padding: 15px 0;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .header.scrolled {
        background: rgba(10, 10, 15, 0.98);
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3), 0 0 30px rgba(88, 101, 242, 0.2);
        border-bottom: 1px solid rgba(88, 101, 242, 0.4);
    }

    .nav {
        display: flex;
        justify-content: space-between;
        align-items: center;
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
        position: relative;
        height: 70px; /* Fixed height for consistent alignment */
    }

    .logo {
        display: flex;
        align-items: center;
        text-decoration: none;
        transition: all 0.3s ease;
        height: 100%;
    }

    .logo-img {
        height: 60px;
        width: auto;
        transition: all 0.3s ease;
    }

    .logo:hover .logo-img {
        transform: scale(1.05);
        filter: drop-shadow(0 0 10px var(--glow-primary));
    }

    .nav-links {
        display: flex;
        gap: 30px;
        list-style: none;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        align-items: center;
        height: 100%;
    }

    .nav-links a {
        color: var(--text-secondary);
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
        position: relative;
        display: flex;
        align-items: center;
        height: 100%;
        padding: 0 5px;
    }

    .nav-links a::after {
        content: '';
        position: absolute;
        bottom: -5px;
        left: 0;
        width: 0;
        height: 2px;
        transition: width 0.3s ease;
    }

    .nav-links a:hover::after {
        width: 100%;
    }

    .nav-links a:hover {
        color: var(--text-primary);
    }

    .nav-cta {
        padding: 12px 24px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: var(--text-primary);
        text-decoration: none;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 4px 15px rgba(88, 101, 242, 0.3);
        display: flex;
        align-items: center;
        height: fit-content;
    }

    .nav-cta:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(88, 101, 242, 0.5);
    }

    /* User Dropdown */
    .user-dropdown {
        position: relative;
    }

    .user-dropdown-toggle {
        display: flex;
        align-items: center;
        gap: 8px;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 25px;
        padding: 8px 16px;
        color: var(--text-primary);
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        backdrop-filter: blur(10px);
    }

    .user-dropdown-toggle:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 255, 255, 0.1);
    }

    .user-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        border: 2px solid var(--border-color);
        transition: all 0.3s ease;
    }

    .user-avatar-default {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 0.9rem;
    }

    .user-name {
        font-weight: 600;
        font-size: 0.9rem;
    }

    .user-dropdown-arrow {
        font-size: 0.8rem;
        transition: transform 0.3s ease;
    }

    .user-dropdown.active .user-dropdown-arrow {
        transform: rotate(180deg);
    }

    .user-dropdown-menu {
        position: absolute;
        top: calc(100% + 10px);
        right: 0;
        background: rgba(16, 18, 24, 0.95);
        backdrop-filter: blur(20px);
        border: 1px solid var(--border-color);
        border-radius: 15px;
        padding: 8px 0;
        min-width: 200px;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
        opacity: 0;
        visibility: hidden;
        transform: translateY(-10px);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        z-index: 10001;
    }

    .user-dropdown.active .user-dropdown-menu {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }

    .dropdown-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px 20px;
        color: var(--text-secondary);
        text-decoration: none;
        transition: all 0.3s ease;
        font-weight: 500;
    }

    .dropdown-item:hover {
        background: rgba(88, 101, 242, 0.1);
        color: var(--text-primary);
    }

    .dropdown-item.text-danger {
        color: var(--danger-color);
    }

    .dropdown-item.text-danger:hover {
        background: rgba(239, 68, 68, 0.1);
        color: var(--danger-color);
    }

    .dropdown-divider {
        height: 1px;
        background: var(--border-color);
        margin: 8px 0;
    }

    /* User Navigation */
    .user-nav {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    /* Dashboard Navigation (legacy support) */
    .dashboard-nav {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    /* Notifications Dropdown */
    .notifications-dropdown {
        position: relative;
    }

    .notifications-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        color: var(--text-secondary);
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
    }

    .notifications-toggle:hover {
        background: rgba(255, 255, 255, 0.1);
        color: var(--text-primary);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 255, 255, 0.1);
    }

    .notification-badge {
        position: absolute;
        top: -5px;
        right: -5px;
        background: linear-gradient(135deg, var(--danger-color), #dc2626);
        color: white;
        font-size: 0.7rem;
        font-weight: 700;
        padding: 2px 6px;
        border-radius: 10px;
        min-width: 18px;
        height: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 8px rgba(239, 68, 68, 0.4);
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }

    .notifications-menu {
        position: absolute;
        top: calc(100% + 10px);
        right: 0;
        background: rgba(16, 18, 24, 0.95);
        backdrop-filter: blur(20px);
        border: 1px solid var(--border-color);
        border-radius: 15px;
        width: 380px;
        max-height: 500px;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
        opacity: 0;
        visibility: hidden;
        transform: translateY(-10px);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        z-index: 10001;
        overflow: hidden;
    }

    /* Mobile responsive positioning */
    @media (max-width: 768px) {
        .notifications-menu {
            position: fixed;
            top: 70px;
            left: 50%;
            right: auto;
            transform: translateX(-50%) translateY(-10px);
            width: calc(100vw - 2rem);
            max-width: 380px;
        }

        .notifications-dropdown.active .notifications-menu {
            transform: translateX(-50%) translateY(0);
        }
    }

    .notifications-dropdown.active .notifications-menu {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }

    .notifications-header {
        padding: 1rem 1.5rem;
        border-bottom: 1px solid var(--border-color);
        display: flex;
        justify-content: between;
        align-items: center;
        background: rgba(255, 255, 255, 0.02);
    }

    .notifications-header h6 {
        color: var(--text-primary);
        font-weight: 600;
        margin: 0;
        flex: 1;
    }

    .notifications-list {
        max-height: 350px;
        overflow-y: auto;
        padding: 0.5rem 0;
    }

    .notification-item {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        padding: 1rem 1.5rem;
        transition: all 0.3s ease;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        position: relative;
    }

    .notification-item:hover {
        background: rgba(255, 255, 255, 0.03);
    }

    .notification-item:last-child {
        border-bottom: none;
    }

    .notification-icon {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 0.9rem;
        flex-shrink: 0;
    }

    .notification-content {
        flex: 1;
        min-width: 0;
    }

    .notification-title {
        font-weight: 600;
        color: var(--text-primary);
        font-size: 0.9rem;
        margin-bottom: 4px;
    }

    .notification-text {
        color: var(--text-secondary);
        font-size: 0.8rem;
        line-height: 1.4;
        margin-bottom: 4px;
    }

    .notification-time {
        color: var(--text-muted);
        font-size: 0.75rem;
    }

    .notification-dismiss {
        background: none;
        border: none;
        color: var(--text-muted);
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        transition: all 0.3s ease;
        flex-shrink: 0;
    }

    .notification-dismiss:hover {
        background: rgba(255, 255, 255, 0.1);
        color: var(--text-primary);
    }

    .notifications-footer {
        padding: 0.75rem 1.5rem;
        border-top: 1px solid var(--border-color);
        background: rgba(255, 255, 255, 0.02);
    }

    .notifications-footer a {
        color: var(--primary-color);
        text-decoration: none;
        font-size: 0.9rem;
        font-weight: 500;
        transition: color 0.3s ease;
    }

    .notifications-footer a:hover {
        color: var(--accent-color);
    }

    /* Custom scrollbar for notifications */
    .notifications-list::-webkit-scrollbar {
        width: 6px;
    }

    .notifications-list::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 3px;
    }

    .notifications-list::-webkit-scrollbar-thumb {
        background: rgba(88, 101, 242, 0.5);
        border-radius: 3px;
    }

    .notifications-list::-webkit-scrollbar-thumb:hover {
        background: rgba(88, 101, 242, 0.7);
    }

    /* Mobile Menu */
    .mobile-menu-toggle {
        display: none;
        background: rgba(88, 101, 242, 0.2);
        border: 1px solid rgba(88, 101, 242, 0.5);
        color: var(--text-primary);
        font-size: 1.5rem;
        cursor: pointer;
        padding: 12px;
        border-radius: 8px;
        transition: all 0.3s ease;
        z-index: 10001;
        position: relative;
        min-width: 44px;
        min-height: 44px;
        align-items: center;
        justify-content: center;
        /* Improve touch handling */
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
        user-select: none;
    }

    .mobile-menu-toggle:hover {
        background: rgba(88, 101, 242, 0.4);
        border-color: rgba(88, 101, 242, 0.8);
        transform: scale(1.05);
    }

    .mobile-menu-toggle:active {
        transform: scale(0.95);
        background: rgba(88, 101, 242, 0.5);
    }

    .mobile-menu {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100vh;
        background: rgba(10, 10, 15, 0.98);
        backdrop-filter: blur(20px);
        z-index: 10000;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 30px;
        transform: translateX(-100%);
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        visibility: hidden;
        opacity: 0;
    }

    .mobile-menu.active {
        transform: translateX(0);
        visibility: visible;
        opacity: 1;
    }

    .mobile-menu-close {
        position: absolute;
        top: 30px;
        right: 30px;
        background: none;
        border: none;
        color: var(--text-primary);
        font-size: 2rem;
        cursor: pointer;
        padding: 8px;
        border-radius: 8px;
        transition: all 0.3s ease;
        /* Improve touch handling */
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
        user-select: none;
        min-width: 44px;
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .mobile-menu-close:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: rotate(90deg);
    }

    .mobile-menu-links {
        display: flex;
        flex-direction: column;
        gap: 25px;
        text-align: center;
    }

    .mobile-menu-links a {
        color: var(--text-secondary);
        text-decoration: none;
        font-size: 1.5rem;
        font-weight: 500;
        transition: all 0.3s ease;
        padding: 15px 30px;
        border-radius: 15px;
    }

    .mobile-menu-links a:hover {
        color: var(--text-primary);
        background: rgba(88, 101, 242, 0.1);
        transform: translateY(-2px);
    }

    .mobile-subscription-link {
        background: linear-gradient(135deg, var(--primary-color), var(--accent-color)) !important;
        color: var(--text-primary) !important;
        font-weight: 600 !important;
        border: 2px solid rgba(88, 101, 242, 0.3) !important;
        box-shadow: 0 4px 15px rgba(88, 101, 242, 0.2) !important;
    }

    .mobile-subscription-link:hover {
        background: linear-gradient(135deg, var(--accent-color), var(--primary-color)) !important;
        transform: translateY(-3px) !important;
        box-shadow: 0 6px 20px rgba(88, 101, 242, 0.3) !important;
    }

    /* Mobile User Section - Removed (user items now in main navbar) */

    .mobile-menu-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 1rem;
        color: var(--text-primary);
        text-decoration: none;
        border-radius: var(--border-radius);
        transition: all 0.3s ease;
        margin-bottom: 0.5rem;
    }

    .mobile-menu-item:hover {
        background: var(--bg-secondary);
        color: var(--text-primary);
        text-decoration: none;
        transform: translateX(5px);
    }

    .mobile-menu-item.text-danger {
        color: var(--danger-color) !important;
    }

    .mobile-menu-item.text-danger:hover {
        background: rgba(220, 53, 69, 0.1);
        color: var(--danger-color) !important;
    }

    .mobile-menu-cta {
        margin-top: 20px;
        padding: 15px 30px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: var(--text-primary);
        text-decoration: none;
        border-radius: 25px;
        font-weight: 600;
        font-size: 1.2rem;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 4px 15px rgba(88, 101, 242, 0.3);
    }

    .mobile-menu-cta:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(88, 101, 242, 0.5);
    }

    /* Mobile User Menu */
    .mobile-user-menu {
        padding: 20px;
        border-top: 1px solid var(--border-color);
        margin-top: 20px;
    }

    .mobile-user-info {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 20px;
        padding: 15px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 15px;
        border: 1px solid var(--border-color);
    }

    .mobile-user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        border: 2px solid var(--border-color);
    }

    .mobile-user-avatar-default {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1rem;
    }

    .mobile-user-name {
        font-weight: 600;
        color: var(--text-primary);
        font-size: 1rem;
    }

    .mobile-menu-logout {
        background: linear-gradient(135deg, var(--danger-color), #dc2626) !important;
        margin-top: 10px;
    }

    .mobile-menu-logout:hover {
        background: linear-gradient(135deg, #dc2626, #b91c1c) !important;
    }

    .mobile-notification-badge {
        background: linear-gradient(135deg, var(--danger-color), #dc2626);
        color: white;
        font-size: 0.7rem;
        font-weight: 700;
        padding: 2px 6px;
        border-radius: 10px;
        min-width: 18px;
        height: 18px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-left: auto;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .nav-links {
            display: none !important;
        }

        .nav-cta {
            display: none !important;
        }

        .mobile-menu-toggle {
            display: flex !important;
            background: rgba(88, 101, 242, 0.8) !important;
            border: 3px solid rgba(88, 101, 242, 1) !important;
            color: white !important;
            box-shadow: 0 4px 12px rgba(88, 101, 242, 0.5) !important;
            z-index: 99999 !important;
            position: relative !important;
            min-width: 50px !important;
            min-height: 50px !important;
            align-items: center !important;
            justify-content: center !important;
            font-size: 1.2rem !important;
            cursor: pointer !important;
        }

        .mobile-menu-toggle:hover {
            background: rgba(88, 101, 242, 1) !important;
            transform: scale(1.1) !important;
        }

        .nav {
            padding: 0 15px;
        }

        .logo-img {
            height: 50px;
        }
    }
</style>

<script>
// Header scroll effect
window.addEventListener('scroll', () => {
    const header = document.getElementById('header');
    if (header && window.scrollY > 100) {
        header.classList.add('scrolled');
    } else if (header) {
        header.classList.remove('scrolled');
    }
});

// Mobile menu functionality
function initMobileMenu() {
    const mobileMenuToggle = document.getElementById('mobileMenuToggle');
    const mobileMenu = document.getElementById('mobileMenu');
    const mobileMenuClose = document.getElementById('mobileMenuClose');
    const mobileMenuLinks = document.querySelectorAll('.mobile-menu-links a, .mobile-menu-cta');

    if (!mobileMenuToggle) {
        return false;
    }

    function openMobileMenu() {
        if (mobileMenu) {
            mobileMenu.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
    }

    function closeMobileMenu() {
        if (mobileMenu) {
            mobileMenu.classList.remove('active');
            document.body.style.overflow = '';
        }
    }

    if (mobileMenuToggle) {
        mobileMenuToggle.addEventListener('click', function(e) {
            e.stopPropagation();
            openMobileMenu();
        });

        // Use touchend instead of touchstart to avoid scroll conflicts
        mobileMenuToggle.addEventListener('touchend', function(e) {
            if (e.cancelable) {
                e.preventDefault();
            }
            e.stopPropagation();
            openMobileMenu();
        }, { passive: false });
    }

    if (mobileMenuClose) {
        mobileMenuClose.addEventListener('click', function(e) {
            e.stopPropagation();
            closeMobileMenu();
        });

        mobileMenuClose.addEventListener('touchend', function(e) {
            if (e.cancelable) {
                e.preventDefault();
            }
            e.stopPropagation();
            closeMobileMenu();
        }, { passive: false });
    }

    // Close menu when clicking on links
    mobileMenuLinks.forEach(link => {
        link.addEventListener('click', closeMobileMenu);
    });

    // Close menu when clicking outside
    if (mobileMenu) {
        mobileMenu.addEventListener('click', function(e) {
            if (e.target === mobileMenu) {
                closeMobileMenu();
            }
        });
    }

    // Close menu on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && mobileMenu && mobileMenu.classList.contains('active')) {
            closeMobileMenu();
        }
    });

    return true;
}

// Try multiple initialization methods
document.addEventListener('DOMContentLoaded', initMobileMenu);
window.addEventListener('load', initMobileMenu);
// Also try immediate execution
setTimeout(initMobileMenu, 100);

// Fallback inline functions for direct onclick
function toggleMobileMenu() {
    const mobileMenu = document.getElementById('mobileMenu');
    if (mobileMenu) {
        if (mobileMenu.classList.contains('active')) {
            closeMobileMenuInline();
        } else {
            openMobileMenuInline();
        }
    }
}

function openMobileMenuInline() {
    const mobileMenu = document.getElementById('mobileMenu');
    if (mobileMenu) {
        mobileMenu.classList.add('active');
        document.body.style.overflow = 'hidden';
    }
}

function closeMobileMenuInline() {
    const mobileMenu = document.getElementById('mobileMenu');
    if (mobileMenu) {
        mobileMenu.classList.remove('active');
        document.body.style.overflow = '';
    }
}

// Simple, bulletproof dropdown functionality that works on ALL pages
(function() {
    'use strict';

    // Only run once, ever
    if (window.navbarDropdownReady) {
        return;
    }
    window.navbarDropdownReady = true;

    function setupDropdown() {
        const userDropdownToggle = document.getElementById('userDropdownToggle');
        if (!userDropdownToggle) {
            return;
        }

        // Remove ALL existing event listeners by cloning the element
        const newToggle = userDropdownToggle.cloneNode(true);
        userDropdownToggle.parentNode.replaceChild(newToggle, userDropdownToggle);

        // Add single, simple click handler
        newToggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const dropdown = document.querySelector('.user-dropdown');
            if (dropdown) {
                dropdown.classList.toggle('active');

                // Close notifications if open
                const notificationsDropdown = document.querySelector('.notifications-dropdown');
                if (notificationsDropdown) {
                    notificationsDropdown.classList.remove('active');
                }
            }
        });
    }

    // Try setup immediately
    setupDropdown();

    // Try again on DOM ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', setupDropdown);
    }

    // Try again after short delay
    setTimeout(setupDropdown, 100);

    // Close dropdown when clicking outside
    document.addEventListener('click', function(e) {
        const dropdown = document.querySelector('.user-dropdown');
        if (dropdown && !dropdown.contains(e.target)) {
            dropdown.classList.remove('active');
        }
    });

    // Close dropdown on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const dropdown = document.querySelector('.user-dropdown');
            if (dropdown) {
                dropdown.classList.remove('active');
            }
        }
    });
})();

// Add click handler directly to document for event delegation
document.addEventListener('click', function(e) {
    if (e.target.id === 'userDropdownToggle' || e.target.closest('#userDropdownToggle')) {
        e.preventDefault();
        e.stopPropagation();
        const dropdown = document.querySelector('.user-dropdown');
        if (dropdown) {
            dropdown.classList.toggle('active');
        }
    }
});

// Notification functions
function loadNotifications() {
    fetch('/api/notifications')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateNotificationsList(data.notifications);
                updateNotificationBadge(data.unread_count);
            }
        })
        .catch(error => {
            console.error('Error loading notifications:', error);
        });
}

function updateNotificationsList(notifications) {
    const notificationsList = document.getElementById('notificationsList');
    if (!notificationsList) return;

    if (notifications.length === 0) {
        notificationsList.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-bell-slash fa-2x text-muted mb-2"></i>
                <p class="text-muted mb-0">No notifications</p>
            </div>
        `;
        return;
    }

    notificationsList.innerHTML = notifications.map(notification => `
        <div class="notification-item" data-id="${notification.id}">
            <div class="notification-icon bg-${getNotificationColor(notification.type)}">
                <i class="fas fa-${getNotificationIcon(notification.type)}"></i>
            </div>
            <div class="notification-content">
                <div class="notification-title">${notification.title}</div>
                <div class="notification-text">${notification.message}</div>
                <div class="notification-time">${formatTime(notification.created_at)}</div>
            </div>
            <button class="notification-dismiss" onclick="dismissNotification(this, '${notification.id}')">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `).join('');
}

function updateNotificationBadge(count = null) {
    const badge = document.getElementById('notificationBadge');
    if (!badge) return;

    if (count === null) {
        // Fetch current count
        fetch('/api/notifications/count')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateNotificationBadge(data.count);
                }
            })
            .catch(error => {
                console.error('Error fetching notification count:', error);
            });
        return;
    }

    if (count > 0) {
        badge.textContent = count > 10 ? '10+' : count.toString();
        badge.style.display = 'flex';
    } else {
        badge.style.display = 'none';
    }
}

function dismissNotification(button, notificationId) {
    const notificationItem = button.closest('.notification-item');

    fetch('/api/notifications/dismiss', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        },
        body: JSON.stringify({ notification_id: notificationId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            notificationItem.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => {
                notificationItem.remove();
                updateNotificationBadge();

                // Check if no notifications left
                const notificationsList = document.getElementById('notificationsList');
                if (notificationsList && notificationsList.children.length === 0) {
                    loadNotifications();
                }
            }, 300);
        }
    })
    .catch(error => {
        console.error('Error dismissing notification:', error);
    });
}

function clearAllNotifications() {
    fetch('/api/notifications/clear-all', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadNotifications();
            updateNotificationBadge(0);
        }
    })
    .catch(error => {
        console.error('Error clearing notifications:', error);
    });
}

function getNotificationColor(type) {
    const colors = {
        'license_activated': 'success',
        'license_disabled': 'warning',
        'license_transferred': 'info',
        'settings_updated': 'info',
        'error': 'danger',
        'warning': 'warning',
        'info': 'info',
        'success': 'success'
    };
    return colors[type] || 'info';
}

function getNotificationIcon(type) {
    const icons = {
        'license_activated': 'key',
        'license_disabled': 'exclamation-triangle',
        'license_transferred': 'exchange-alt',
        'settings_updated': 'cog',
        'error': 'exclamation-circle',
        'warning': 'exclamation-triangle',
        'info': 'info-circle',
        'success': 'check-circle'
    };
    return icons[type] || 'bell';
}

function formatTime(timestamp) {
    const now = new Date();
    const time = new Date(timestamp);
    const diff = now - time;

    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    if (hours < 24) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    if (days < 7) return `${days} day${days > 1 ? 's' : ''} ago`;

    return time.toLocaleDateString();
}

function getCsrfToken() {
    return document.querySelector('meta[name=csrf-token]')?.getAttribute('content') || '';
}

function showMobileNotifications() {
    // Close mobile menu first
    closeMobileMenuInline();

    // Show notifications in a modal or redirect to notifications page
    alert('Mobile notifications feature - this would show a full-screen notification view');
    // TODO: Implement mobile notifications view
}

// Add slide out animation (only if not already added)
if (!window.navbarSlideStyleAdded) {
    const navbarSlideStyle = document.createElement('style');
    navbarSlideStyle.textContent = `
        @keyframes slideOut {
            from {
                opacity: 1;
                transform: translateX(0);
            }
            to {
                opacity: 0;
                transform: translateX(100%);
            }
        }
    `;
    document.head.appendChild(navbarSlideStyle);
    window.navbarSlideStyleAdded = true;
}
</script>
