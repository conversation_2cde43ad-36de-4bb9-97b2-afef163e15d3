﻿{% extends "base.html" %}

{% block title %}r<PERSON><PERSON><PERSON> - Freemium Discord Bot{% endblock %}

{% block styles %}
<style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
    
    :root {
        --primary-color: #5865f2;
        --secondary-color: #7289da;
        --accent-color: #00d4ff;
        --bg-primary: #0a0a0f;
        --bg-secondary: #101218;
        --bg-tertiary: #1a1d29;
        --text-primary: #ffffff;
        --text-secondary: #b9bbbe;
        --text-muted: #72767d;
        --border-color: rgba(255, 255, 255, 0.1);
        --glow-primary: rgba(88, 101, 242, 0.5);
        --glow-accent: rgba(0, 212, 255, 0.3);
    }

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        background: var(--bg-primary);
        color: var(--text-primary);
        overflow-x: hidden;
        line-height: 1.6;
        /* Mobile optimizations */
        -webkit-text-size-adjust: 100%;
        -webkit-tap-highlight-color: transparent;
        touch-action: manipulation;
    }

    /* Animated Background */
    .animated-bg {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        background: linear-gradient(45deg, #0a0a0f 25%, transparent 25%),
                    linear-gradient(-45deg, #0a0a0f 25%, transparent 25%),
                    linear-gradient(45deg, transparent 75%, #0a0a0f 75%),
                    linear-gradient(-45deg, transparent 75%, #0a0a0f 75%);
        background-size: 60px 60px;
        background-position: 0 0, 0 30px, 30px -30px, -30px 0px;
        animation: backgroundMove 20s linear infinite;
        opacity: 0.03;
    }

    @keyframes backgroundMove {
        0% { background-position: 0 0, 0 30px, 30px -30px, -30px 0px; }
        100% { background-position: 60px 60px, 60px 90px, 90px 30px, 30px 60px; }
    }

    /* Floating Particles */
    .particles {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        overflow: hidden;
    }

    .particle {
        position: absolute;
        width: 4px;
        height: 4px;
        background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
        border-radius: 50%;
        animation: float 6s ease-in-out infinite;
        opacity: 0.6;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.6; }
        50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
    }



    /* Hero Section */
    .hero {
        min-height: auto; /* Changed from 100vh to auto for compact layout */
        display: flex;
        align-items: center;
        text-align: center;
        padding: 180px 20px 80px; /* Balanced padding for proper spacing from navbar */
        position: relative;
        overflow: hidden;
        z-index: 1;
        /* Improve text rendering on mobile */
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    .hero::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 600px;
        height: 600px;
        background: radial-gradient(circle, rgba(88, 101, 242, 0.1) 0%, transparent 70%);
        transform: translate(-50%, -50%);
        animation: pulse 4s ease-in-out infinite;
    }

    @keyframes pulse {
        0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.5; }
        50% { transform: translate(-50%, -50%) scale(1.1); opacity: 0.8; }
    }

    .hero-content {
        max-width: 800px;
        margin: 0 auto;
        position: relative;
        z-index: 1;
        transform: translateZ(0); /* Force hardware acceleration */
    }

    .hero-badge {
        display: inline-block;
        background: rgba(88, 101, 242, 0.1);
        border: 1px solid rgba(88, 101, 242, 0.3);
        padding: 8px 20px;
        border-radius: 25px;
        font-size: 0.9rem;
        color: var(--primary-color);
        margin-bottom: 20px;
        animation: slideInUp 0.6s ease-out;
    }

    .hero-title {
        font-size: clamp(1.5rem, 4vw, 3.5rem);
        font-weight: 900;
        margin-bottom: 20px;
        color: var(--text-primary);
        animation: slideInUp 0.8s ease-out 0.2s both;
        line-height: 1.1;
    }

    .static-text {
        background: linear-gradient(135deg, var(--text-primary) 0%, var(--text-secondary) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .typing-text {
        position: relative;
        display: inline;
        color: var(--primary-color);
        font-weight: 900;
    }

    .typing-text::after {
        content: '|';
        color: var(--primary-color);
        animation: blink 1s infinite;
        margin-left: 2px;
    }

    @keyframes blink {
        0%, 50% { opacity: 1; }
        51%, 100% { opacity: 0; }
    }

    .hero-description {
        font-size: 1.3rem;
        color: var(--text-secondary);
        margin-bottom: 30px;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
        animation: slideInUp 1s ease-out 0.4s both;
    }

    .hero-features {
        display: flex;
        justify-content: center;
        margin-bottom: 40px;
        height: 60px; /* Increased height to prevent clipping */
        overflow: visible; /* Changed from hidden to visible */
        animation: slideInUp 1.2s ease-out 0.6s both;
    }

    .feature-tags-container {
        display: flex;
        gap: 15px;
        align-items: center;
        position: relative;
        white-space: nowrap;
        min-height: 60px; /* Increased to match parent */
        justify-content: center;
        overflow: visible; /* Changed from hidden to visible */
        width: 100%;
        max-width: 600px;
        margin: 0 auto;
        padding: 10px 0; /* Added padding for better spacing */
    }

    .feature-tag {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 0.9rem;
        color: var(--text-secondary);
        backdrop-filter: blur(10px);
        transition: all 0.4s ease-out;
        opacity: 1;
        transform: translateX(0) scale(1);
        will-change: transform, opacity;
        flex-shrink: 0;
    }

    .feature-tag:hover {
        background: rgba(88, 101, 242, 0.1);
        border-color: rgba(88, 101, 242, 0.3);
        color: var(--primary-color);
        transform: translateY(-2px) scale(1.05);
    }

    .feature-tag.fade-out {
        opacity: 0;
        transform: translateX(-30px) scale(0.9);
        pointer-events: none;
    }

    .feature-tag.fade-in {
        opacity: 0;
        transform: translateX(30px) scale(0.9);
        pointer-events: none;
    }

    .hero-buttons {
        display: flex;
        gap: 20px;
        justify-content: center;
        flex-wrap: wrap;
        animation: slideInUp 1.4s ease-out 0.8s both;
    }

    .btn {
        padding: 16px 32px;
        border-radius: 25px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 10px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border: none;
        cursor: pointer;
        font-size: 1rem;
        /* Mobile touch optimizations */
        min-height: 44px; /* iOS recommended touch target size */
        -webkit-tap-highlight-color: transparent;
        user-select: none;
    }

    .btn-primary {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: var(--text-primary);
        box-shadow: 0 6px 20px rgba(88, 101, 242, 0.4);
    }

    .btn-primary:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 30px rgba(88, 101, 242, 0.6);
    }

    .btn-secondary {
        background: rgba(255, 255, 255, 0.05);
        color: var(--text-primary);
        border: 1px solid rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
    }

    .btn-secondary:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: translateY(-3px);
        box-shadow: 0 10px 30px rgba(255, 255, 255, 0.1);
    }

    /* Stats Section */
    .stats {
        padding: 80px 20px;
        background: rgba(255, 255, 255, 0.02);
        backdrop-filter: blur(20px);
        border-top: 1px solid var(--border-color);
        border-bottom: 1px solid var(--border-color);
        position: relative;
        z-index: 1;
    }

    .stats-container {
        max-width: 1200px;
        margin: 0 auto;
        text-align: center;
    }

    .stats-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 60px;
        color: var(--text-secondary);
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 40px;
        margin-bottom: 80px;
    }

    /* Server Showcase */
    .server-showcase {
        padding: 80px 20px;
        text-align: center;
        position: relative;
        z-index: 1;
    }

    .server-showcase-card {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid var(--border-color);
        border-radius: 20px;
        padding: 40px;
        backdrop-filter: blur(20px);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        max-width: 1200px;
        margin: 0 auto;
    }

    .server-showcase-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .server-showcase-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
        border-color: rgba(255, 255, 255, 0.2);
        background: rgba(255, 255, 255, 0.08);
    }

    .server-showcase-card:hover::before {
        opacity: 1;
    }

    .showcase-content {
        max-width: 1200px;
        margin: 0 auto;
    }

    .showcase-title {
        font-size: 1.2rem;
        color: var(--text-secondary);
        margin-bottom: 40px;
    }

    .showcase-title span {
        color: var(--primary-color);
        font-weight: 700;
    }

    .server-grid {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 30px;
        flex-wrap: wrap;
    }

    .server-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;
        transition: transform 0.3s ease;
    }

    .server-item:hover {
        transform: translateY(-5px);
    }

    .server-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        border: 2px solid var(--border-color);
        transition: border-color 0.3s ease;
    }

    .server-item:hover .server-icon {
        border-color: var(--primary-color);
    }

    .server-name {
        font-size: 0.9rem;
        font-weight: 600;
        color: var(--text-primary);
    }

    .server-members {
        font-size: 0.8rem;
        color: var(--text-muted);
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.03);
        border: 1px solid var(--border-color);
        border-radius: 20px;
        padding: 30px 20px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        backdrop-filter: blur(20px);
    }

    .stat-card:hover {
        transform: translateY(-5px);
        background: rgba(255, 255, 255, 0.05);
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 900;
        color: var(--text-primary);
        margin-bottom: 10px;
        background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .stat-label {
        color: var(--text-secondary);
        font-size: 1rem;
        font-weight: 500;
    }

    /* Server Showcase */
    .server-showcase {
        padding: 80px 0;
        overflow: hidden;
    }

    .showcase-title {
        text-align: center;
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 60px;
        color: var(--text-secondary);
    }

    .server-slider {
        position: relative;
        height: 120px;
        overflow: hidden;
        mask-image: linear-gradient(to right, transparent 0%, black 10%, black 90%, transparent 100%);
    }

    .server-track {
        display: flex;
        gap: 30px;
        animation: scroll 40s linear infinite;
    }

    .server-card {
        flex: 0 0 auto;
        display: flex;
        align-items: center;
        gap: 15px;
        background: rgba(255, 255, 255, 0.03);
        border: 1px solid var(--border-color);
        border-radius: 15px;
        padding: 20px;
        min-width: 280px;
        backdrop-filter: blur(20px);
        transition: all 0.3s ease;
    }

    .server-card:hover {
        transform: translateY(-3px);
        background: rgba(255, 255, 255, 0.05);
    }

    .server-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        box-shadow: 0 4px 15px rgba(88, 101, 242, 0.3);
    }

    .server-info h4 {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 5px;
    }

    .server-info p {
        color: var(--text-secondary);
        font-size: 0.9rem;
    }

    @keyframes scroll {
        0% { transform: translateX(0); }
        100% { transform: translateX(-50%); }
    }

    /* Features Section */
    .features {
        padding: 80px 20px;
        position: relative;
        z-index: 1;
    }

    .features-main-card {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid var(--border-color);
        border-radius: 20px;
        padding: 40px;
        backdrop-filter: blur(20px);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        max-width: 1200px;
        margin: 0 auto;
    }

    .features-main-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .features-main-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
        border-color: rgba(255, 255, 255, 0.2);
        background: rgba(255, 255, 255, 0.08);
    }

    .features-main-card:hover::before {
        opacity: 1;
    }

    .features-container {
        max-width: 1200px;
        margin: 0 auto;
    }

    .features-header {
        text-align: center;
        margin-bottom: 80px;
    }

    .features-title {
        font-size: 3rem;
        font-weight: 900;
        margin-bottom: 20px;
        background: linear-gradient(135deg, var(--text-primary), var(--text-secondary));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .features-subtitle {
        font-size: 1.2rem;
        color: var(--text-secondary);
        max-width: 600px;
        margin: 0 auto;
    }

    .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 30px;
    }

    .feature-card {
        background: rgba(255, 255, 255, 0.03);
        border: 1px solid var(--border-color);
        border-radius: 20px;
        padding: 40px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        backdrop-filter: blur(20px);
        position: relative;
        overflow: hidden;
    }

    .feature-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
        transform: translateX(-100%);
        transition: transform 0.6s ease;
    }

    .feature-card:hover::before {
        transform: translateX(0);
    }

    .feature-card:hover {
        transform: translateY(-5px);
        background: rgba(255, 255, 255, 0.05);
        box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
    }

    .feature-icon {
        font-size: 3rem;
        margin-bottom: 20px;
        display: block;
    }

    .feature-title {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 15px;
        color: var(--text-primary);
    }

    .feature-description {
        color: var(--text-secondary);
        line-height: 1.7;
        font-size: 1rem;
    }

    /* CTA Section */
    .cta {
        padding: 100px 20px;
        text-align: center;
        position: relative;
        overflow: hidden;
        z-index: 1;
    }

    .cta-main-card {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid var(--border-color);
        border-radius: 20px;
        padding: 60px 40px;
        backdrop-filter: blur(20px);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        max-width: 1200px;
        margin: 0 auto;
    }

    .cta-main-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .cta-main-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
        border-color: rgba(255, 255, 255, 0.2);
        background: rgba(255, 255, 255, 0.08);
    }

    .cta-main-card:hover::before {
        opacity: 1;
    }

    .cta::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url("data:image/svg+xml,%3Csvg viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
        animation: backgroundMove 20s linear infinite;
    }

    .cta-content {
        max-width: 800px;
        margin: 0 auto;
        position: relative;
        z-index: 2;
    }

    .cta-title {
        font-size: 3rem;
        font-weight: 900;
        margin-bottom: 20px;
        background: linear-gradient(135deg, var(--text-primary), var(--primary-color));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .cta-description {
        font-size: 1.2rem;
        color: var(--text-secondary);
        margin-bottom: 40px;
    }

    /* Animations */
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
        }
        to {
            opacity: 1;
        }
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
        .hero {
            padding: 160px 20px 60px;
        }

        .features-grid {
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        }

        .server-card {
            min-width: 250px;
        }
    }

    @media (max-width: 992px) {
        .hero {
            padding: 140px 15px 50px;
        }

        .hero-title {
            font-size: clamp(2rem, 6vw, 3rem);
        }

        .hero-description {
            font-size: 1.1rem;
        }

        .stats {
            padding: 60px 15px;
        }

        .stats-title {
            font-size: 2rem;
        }

        .features {
            padding: 60px 15px;
        }

        .features-title {
            font-size: 2.5rem;
        }

        .cta {
            padding: 80px 15px;
        }

        .cta-title {
            font-size: 2.5rem;
        }
    }

    @media (max-width: 768px) {
        .hero {
            padding: 120px 15px 40px;
            text-align: center;
        }

        .hero-title {
            font-size: clamp(1.8rem, 8vw, 2.5rem);
            line-height: 1.2;
            margin-bottom: 15px;
        }

        .hero-description {
            font-size: 1rem;
            margin-bottom: 25px;
            padding: 0 10px;
        }

        .hero-features {
            height: auto;
            min-height: 50px;
            margin-bottom: 50px;
            padding-bottom: 20px;
        }

        .feature-tags-container {
            min-height: 50px;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
        }

        .feature-tag {
            font-size: 0.8rem;
            padding: 6px 12px;
        }

        .hero-buttons {
            flex-direction: column;
            align-items: center;
            gap: 15px;
        }

        .btn {
            padding: 14px 28px;
            font-size: 0.95rem;
            width: 100%;
            max-width: 280px;
            justify-content: center;
        }

        .stats {
            padding: 50px 15px;
        }

        .stats-title {
            font-size: 1.8rem;
            margin-bottom: 40px;
        }

        .stats-grid {
            grid-template-columns: 1fr;
            gap: 20px;
            max-width: 300px;
            margin: 0 auto 60px;
        }

        .stat-card {
            padding: 25px 20px;
        }

        .stat-number {
            font-size: 2rem;
        }

        .server-showcase {
            padding: 50px 15px;
        }

        .server-showcase-card {
            padding: 30px 20px;
            margin: 0 10px;
        }

        .showcase-title {
            font-size: 1rem;
            margin-bottom: 30px;
            padding: 0 10px;
        }

        .server-grid {
            gap: 20px;
            justify-content: center;
        }

        .server-item {
            gap: 8px;
        }

        .server-icon {
            width: 50px;
            height: 50px;
        }

        .server-name {
            font-size: 0.85rem;
        }

        .server-members {
            font-size: 0.75rem;
        }

        .features {
            padding: 50px 15px;
        }

        .features-main-card {
            padding: 30px 20px;
            margin: 0 10px;
        }

        .features-title {
            font-size: 2rem;
            margin-bottom: 15px;
        }

        .features-subtitle {
            font-size: 1rem;
            margin-bottom: 40px;
            padding: 0 10px;
        }

        .features-grid {
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .feature-card {
            padding: 25px 20px;
        }

        .feature-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }

        .feature-title {
            font-size: 1.3rem;
            margin-bottom: 12px;
        }

        .feature-description {
            font-size: 0.95rem;
            line-height: 1.6;
        }

        .cta {
            padding: 60px 15px;
        }

        .cta-main-card {
            padding: 40px 20px;
            margin: 0 10px;
        }

        .cta-title {
            font-size: 2rem;
            margin-bottom: 15px;
        }

        .cta-description {
            font-size: 1rem;
            margin-bottom: 30px;
            padding: 0 10px;
        }
    }

    @media (max-width: 480px) {
        .hero {
            padding: 100px 10px 30px;
        }

        .hero-title {
            font-size: clamp(1.5rem, 10vw, 2rem);
        }

        .hero-description {
            font-size: 0.9rem;
            margin-bottom: 30px;
        }

        .hero-features {
            margin-bottom: 60px;
            padding-bottom: 25px;
        }

        .feature-tags-container {
            margin-bottom: 25px;
            padding-bottom: 20px;
        }

        .btn {
            padding: 12px 24px;
            font-size: 0.9rem;
        }

        .stats {
            padding: 40px 10px;
        }

        .stats-title {
            font-size: 1.5rem;
        }

        .stat-card {
            padding: 20px 15px;
        }

        .stat-number {
            font-size: 1.8rem;
        }

        .stat-label {
            font-size: 0.9rem;
        }

        .server-showcase {
            padding: 40px 10px;
        }

        .server-showcase-card {
            padding: 25px 15px;
            margin: 0 5px;
        }

        .showcase-title {
            font-size: 0.9rem;
        }

        .server-icon {
            width: 45px;
            height: 45px;
        }

        .features {
            padding: 40px 10px;
        }

        .features-main-card {
            padding: 25px 15px;
            margin: 0 5px;
        }

        .features-title {
            font-size: 1.8rem;
        }

        .features-subtitle {
            font-size: 0.9rem;
        }

        .feature-card {
            padding: 20px 15px;
        }

        .feature-icon {
            font-size: 2rem;
        }

        .feature-title {
            font-size: 1.2rem;
        }

        .feature-description {
            font-size: 0.9rem;
        }

        .cta {
            padding: 50px 10px;
        }

        .cta-main-card {
            padding: 30px 15px;
            margin: 0 5px;
        }

        .cta-title {
            font-size: 1.8rem;
        }

        .cta-description {
            font-size: 0.9rem;
        }
    }

    /* Touch device optimizations */
    @media (hover: none) and (pointer: coarse) {
        .feature-card:hover,
        .stat-card:hover,
        .server-item:hover {
            transform: none;
        }

        .btn:hover {
            transform: none;
        }

        .feature-tag:hover {
            transform: none;
        }

        /* Reduce animations on touch devices for better performance */
        .particles {
            display: none;
        }

        .animated-bg {
            animation: none;
        }

        /* Improve touch interactions */
        .btn,
        .feature-card,
        .stat-card {
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        /* Add active states for touch feedback */
        .btn:active {
            transform: scale(0.98);
            opacity: 0.8;
        }

        .feature-card:active,
        .stat-card:active {
            transform: scale(0.98);
        }
    }

    /* Reduce motion for users who prefer it */
    @media (prefers-reduced-motion: reduce) {
        *,
        *::before,
        *::after {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }

        .particles {
            display: none;
        }

        .animated-bg {
            animation: none;
        }

        .typing-text::after {
            animation: none;
        }
    }

    /* Scroll reveal animation */
    .reveal {
        opacity: 0;
        transform: translateY(50px);
        transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .reveal.active {
        opacity: 1;
        transform: translateY(0);
    }
</style>
{% endblock %}

{% block content %}
<!-- Animated Background -->
<div class="animated-bg"></div>

<!-- Particles -->
<div class="particles" id="particles"></div>

<!-- Include Navbar -->
{% include 'navbar.html' %}

<!-- Hero Section -->
<section class="hero">
    <div class="hero-content">
        <div class="hero-badge">🚀 Freemium Discord Bot</div>
        <h1 class="hero-title"><span class="static-text">Discord's </span><span id="rotatingText" class="typing-text"></span></h1>
        <p class="hero-description">
            Built to elevate your community's experience, streamline server management, and provide you access to premium resources for every necessity.
        </p>
        
        <div class="hero-features">
            <div class="feature-tags-container" id="featureTagsContainer">
                <!-- Feature tags will be dynamically populated -->
            </div>
        </div>

        <div class="hero-buttons">
            {% if 'discord_user' in session %}
            <a href="{{ url_for('select_server') }}" class="btn btn-primary">
                <i class="fas fa-tachometer-alt"></i>
                Access Dashboard
            </a>
            {% else %}
            <a href="{{ url_for('login') }}" class="btn btn-primary">
                <i class="fas fa-tachometer-alt"></i>
                Access Dashboard
            </a>
            {% endif %}
            <a href="https://discord.com/oauth2/authorize?client_id=1389466386396483714" target="_blank" class="btn btn-secondary">
                <i class="fab fa-discord"></i>
                Invite Bot
            </a>
        </div>
    </div>
</section>

<!-- Server Showcase -->
<section class="server-showcase" id="servers">
    <div class="server-showcase-card">
        <div class="showcase-content">
            <div class="showcase-title">Powering <span id="statsUsers">-</span> users across <span id="statsCommunities">-</span> communities</div>
            <div class="server-grid" id="serverGrid">
                <!-- Servers will be populated here -->
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="features" id="features">
    <div class="features-main-card">
        <div class="features-container">
            <div class="features-header">
                <h2 class="features-title">We provide you with the best tools</h2>
                <p class="features-subtitle">to keep your community clean, secure, and engaged</p>
            </div>

            <div class="features-grid">
                <div class="feature-card reveal">
                    <div class="feature-icon">🛡️</div>
                    <h3 class="feature-title">Advanced Security</h3>
                    <p class="feature-description">
                        Keep your server safe with our comprehensive anti-nuke, anti-raid, and filtering systems. Protect against malicious attacks with customizable thresholds and automated responses.
                    </p>
                </div>

                <div class="feature-card reveal">
                    <div class="feature-icon">🎵</div>
                    <h3 class="feature-title">Premium Music</h3>
                    <p class="feature-description">
                        Premium audio quality with Spotify integration, custom filters, queue management, and preset audio effects. Transform any music into an immersive listening experience.
                    </p>
                </div>

                <div class="feature-card reveal">
                    <div class="feature-icon">🔊</div>
                    <h3 class="feature-title">Voice Channels</h3>
                    <p class="feature-description">
                        Temporary personalized voice channels with powerful control interfaces. Easy setup, custom roles, and intuitive management for seamless voice communication.
                    </p>
                </div>

                <div class="feature-card reveal">
                    <div class="feature-icon">📱</div>
                    <h3 class="feature-title">Social Media</h3>
                    <p class="feature-description">
                        Track feeds from Instagram, Twitter, TikTok, and more. Embed live feeds, notify about new posts, and keep your community connected across platforms.
                    </p>
                </div>

                <div class="feature-card reveal">
                    <div class="feature-icon">🎁</div>
                    <h3 class="feature-title">Engagement Tools</h3>
                    <p class="feature-description">
                        Giveaways, leveling systems, games, auto-responders, and reaction triggers to keep your community active, engaged, and entertained.
                    </p>
                </div>

                <div class="feature-card reveal">
                    <div class="feature-icon">⚡</div>
                    <h3 class="feature-title">Utility Features</h3>
                    <p class="feature-description">
                        Image manipulation, translation, text-to-speech, counters, bump reminders, and more. Everything you need in one powerful, comprehensive bot.
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="cta">
    <div class="cta-main-card">
        <div class="cta-content">
            <h2 class="cta-title">Ready to transform your server?</h2>
            <p class="cta-description">Join thousands of communities already using ryzuo Bot for premium Discord management</p>
            <div class="hero-buttons">
                <a href="https://discord.com/oauth2/authorize?client_id=1389466386396483714" target="_blank" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    Add to Server
                </a>
                <a href="javascript:void(Tawk_API.toggle())" class="btn btn-secondary">
                    <i class="fas fa-question-circle"></i>
                    Get Support
                </a>
            </div>
            <div style="margin-top: 20px;">
                <small style="color: var(--text-muted);">
                    Need a license key? <a href="https://ryzuo.com/shop" target="_blank" style="color: var(--primary-color);">Get one here</a>
                </small>
            </div>
        </div>
    </div>
</section>

<script>
// Create floating particles (optimized for mobile)
function createParticles() {
    const particles = document.getElementById('particles');
    if (!particles) return;

    // Reduce particle count on mobile devices
    const isMobile = window.innerWidth <= 768;
    const particleCount = isMobile ? 20 : 50;

    // Don't create particles if user prefers reduced motion
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
        return;
    }

    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.top = Math.random() * 100 + '%';
        particle.style.animationDelay = Math.random() * 6 + 's';
        particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
        particles.appendChild(particle);
    }
}

// Typing effect animation
const rotatingTexts = [
    'premium solution',
    'moderation tool',
    'community manager',
    'engagement platform',
    'all-in-one app'
];

let currentTextIndex = 0;
let currentCharIndex = 0;
let isDeleting = false;
let isTyping = false;
const rotatingElement = document.getElementById('rotatingText');

function typeText() {
    if (!rotatingElement || isTyping) return;

    isTyping = true;
    const currentText = rotatingTexts[currentTextIndex];

    if (!isDeleting) {
        // Typing
        currentCharIndex++;
        rotatingElement.textContent = currentText.substring(0, currentCharIndex);

        if (currentCharIndex === currentText.length) {
            // Finished typing, wait then start deleting
            setTimeout(() => {
                isDeleting = true;
                isTyping = false;
                typeText();
            }, 2000);
            return;
        }

        setTimeout(() => {
            isTyping = false;
            typeText();
        }, 100);
    } else {
        // Deleting
        currentCharIndex--;
        rotatingElement.textContent = currentText.substring(0, currentCharIndex);

        if (currentCharIndex === 0) {
            // Finished deleting, move to next text
            isDeleting = false;
            currentTextIndex = (currentTextIndex + 1) % rotatingTexts.length;
            setTimeout(() => {
                isTyping = false;
                typeText();
            }, 500);
            return;
        }

        setTimeout(() => {
            isTyping = false;
            typeText();
        }, 50);
    }
}

// Feature tags sliding animation
const featureTags = [
    'repping perks',
    'auto-roling',
    'venting',
    'voice channels',
    'sticky messages',
    'giveaways',
    'dm support',
    'gender verification',
    'logging',
    'dashboard',
    'on-site logs',
    'license management',
    'on-site statistics'
];

let currentTagIndex = 0;
const featureTagsContainer = document.getElementById('featureTagsContainer');

function initializeFeatureTags() {
    if (!featureTagsContainer) return;

    // Show fewer tags on mobile
    const isMobile = window.innerWidth <= 768;
    const tagCount = isMobile ? 2 : 4;

    for (let i = 0; i < tagCount; i++) {
        const tag = document.createElement('div');
        tag.className = 'feature-tag';
        tag.textContent = featureTags[i];
        featureTagsContainer.appendChild(tag);
    }
    currentTagIndex = tagCount;
}

function slideFeatureTags() {
    if (!featureTagsContainer) return;

    const tags = featureTagsContainer.querySelectorAll('.feature-tag:not(.fade-out)');
    if (tags.length === 0) return;

    // Fade out the first tag
    const firstTag = tags[0];
    firstTag.classList.add('fade-out');

    // Create new tag
    const newTag = document.createElement('div');
    newTag.className = 'feature-tag fade-in';
    newTag.textContent = featureTags[currentTagIndex % featureTags.length];

    // Add to container
    featureTagsContainer.appendChild(newTag);

    // Animate in the new tag
    requestAnimationFrame(() => {
        requestAnimationFrame(() => {
            newTag.classList.remove('fade-in');
        });
    });

    // Remove old tag after animation
    setTimeout(() => {
        if (firstTag && firstTag.parentNode) {
            firstTag.remove();
        }
    }, 400);

    currentTagIndex++;
}

// Start animations with better timing (slower on mobile)
// Note: isMobile detection moved to DOMContentLoaded to avoid variable conflicts
// setInterval will be called from there

// Scroll reveal animation
function revealOnScroll() {
    const reveals = document.querySelectorAll('.reveal');

    for (let i = 0; i < reveals.length; i++) {
        const windowHeight = window.innerHeight;
        const elementTop = reveals[i].getBoundingClientRect().top;
        const elementVisible = 150;

        if (elementTop < windowHeight - elementVisible) {
            reveals[i].classList.add('active');
        }
    }
}

window.addEventListener('scroll', revealOnScroll);

// Load homepage statistics
async function loadStats() {
    try {
        const response = await fetch('/api/homepage-stats');
        const data = await response.json();

        if (data.success) {
            // Only show real data - no fallback
            animateNumber('totalServers', data.total_servers || 0);
            animateNumber('totalMembers', data.total_members || 0);
            animateNumber('totalChannels', data.total_channels || 0);
            animateNumber('totalCommands', data.total_commands || 0);

            // Remove loading animation
            document.querySelectorAll('.loading-pulse').forEach(el => {
                el.classList.remove('loading-pulse');
            });
        } else {
            showErrorStats();
        }
    } catch (error) {
        console.error('Error loading stats:', error);
        showErrorStats();
    }
}

// Show error state instead of fallback statistics
function showErrorStats() {
    document.getElementById('totalServers').textContent = '0';
    document.getElementById('totalMembers').textContent = '0';
    document.getElementById('totalChannels').textContent = '0';
    document.getElementById('totalCommands').textContent = '0';

    document.querySelectorAll('.loading-pulse').forEach(el => {
        el.classList.remove('loading-pulse');
        el.style.color = 'var(--text-muted)';
        el.title = 'Unable to load statistics';
    });
}

// Load featured servers
async function loadServers() {
    try {
        const response = await fetch('/api/homepage-servers');
        const data = await response.json();

        if (data.success && data.servers && data.servers.length > 0) {
            const serverGrid = document.getElementById('serverGrid');

            // Sort servers by member count (largest first) and show top 4
            const sortedServers = data.servers.sort((a, b) => b.member_count - a.member_count);
            const topServers = sortedServers.slice(0, 4);

            serverGrid.innerHTML = topServers.map(server => `
                <div class="server-item">
                    <img src="${server.icon_url || 'https://cdn.discordapp.com/embed/avatars/0.png'}"
                         alt="${server.name}" class="server-icon"
                         onerror="this.src='https://cdn.discordapp.com/embed/avatars/0.png'">
                    <div class="server-name">${server.name}</div>
                    <div class="server-members">${server.member_count.toLocaleString()} members</div>
                </div>
            `).join('');

            // Update the stats text with real numbers from all servers
            const totalMembers = data.servers.reduce((sum, server) => sum + server.member_count, 0);
            const totalServers = data.servers.length;

            document.getElementById('statsUsers').textContent = totalMembers.toLocaleString();
            document.getElementById('statsCommunities').textContent = totalServers.toLocaleString();
        } else {
            console.error('No servers data available');
            // Don't show fallback servers - leave empty if no real data
            document.getElementById('serverGrid').innerHTML = '<p style="color: var(--text-muted);">No servers available</p>';
            document.getElementById('statsUsers').textContent = '0';
            document.getElementById('statsCommunities').textContent = '0';
        }
    } catch (error) {
        console.error('Error loading servers:', error);
        // Don't show fallback servers - leave empty if API fails
        document.getElementById('serverGrid').innerHTML = '<p style="color: var(--text-muted);">Unable to load servers</p>';
        document.getElementById('statsUsers').textContent = '0';
        document.getElementById('statsCommunities').textContent = '0';
    }
}



// Animate numbers with easing
function animateNumber(elementId, targetValue) {
    const element = document.getElementById(elementId);
    if (!element) return;

    const duration = 2000;
    const startValue = 0;
    const startTime = performance.now();

    function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // Easing function for smooth animation
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const currentValue = Math.floor(startValue + (targetValue - startValue) * easeOutQuart);

        // Format numbers appropriately
        if (targetValue >= 1000000) {
            element.textContent = (currentValue / 1000000).toFixed(1) + 'M';
        } else if (targetValue >= 1000) {
            element.textContent = (currentValue / 1000).toFixed(1) + 'K';
        } else {
            element.textContent = currentValue.toLocaleString();
        }

        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        }
    }

    requestAnimationFrame(updateNumber);
}

// Initialize everything when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Mobile detection
    const isMobile = window.innerWidth <= 768;
    const isTouch = 'ontouchstart' in window;

    // Create particles (skip on low-end devices)
    if (!isMobile || !isTouch) {
        createParticles();
    }

    // Initialize feature tags
    initializeFeatureTags();

    // Load data
    loadStats();
    loadServers();

    // Initial reveal check
    revealOnScroll();

    // Start animations with staggered timing (delayed on mobile)
    const typeDelay = isMobile ? 1000 : 2000;
    const slideDelay = isMobile ? 2000 : 3000;
    const slideInterval = isMobile ? 3500 : 2500;

    setTimeout(() => typeText(), typeDelay);
    setTimeout(() => slideFeatureTags(), slideDelay);

    // Start the sliding animation interval
    setInterval(slideFeatureTags, slideInterval);

    // Add touch event listeners for mobile interactions
    if (isTouch) {
        addTouchInteractions();
    }

    // Handle orientation changes
    window.addEventListener('orientationchange', function() {
        setTimeout(() => {
            // Recalculate layouts after orientation change
            revealOnScroll();
        }, 100);
    });

    // Optimize scroll performance on mobile
    let ticking = false;
    function optimizedScrollHandler() {
        if (!ticking) {
            requestAnimationFrame(() => {
                revealOnScroll();
                ticking = false;
            });
            ticking = true;
        }
    }

    // Use optimized scroll handler on mobile
    if (isMobile) {
        window.addEventListener('scroll', optimizedScrollHandler, { passive: true });
    } else {
        window.addEventListener('scroll', revealOnScroll);
    }
});

// Add touch interactions for mobile devices
function addTouchInteractions() {
    const cards = document.querySelectorAll('.feature-card, .stat-card');

    cards.forEach(card => {
        card.addEventListener('touchstart', function() {
            this.style.transform = 'translateY(-2px)';
        }, { passive: true });

        card.addEventListener('touchend', function() {
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        }, { passive: true });
    });
}
</script>

{% endblock %}