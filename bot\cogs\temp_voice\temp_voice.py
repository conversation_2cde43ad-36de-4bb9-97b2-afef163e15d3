"""
Temporary Voice Channels System Cog for Discord Bot
Handles temporary voice channel creation, management, and cleanup
"""

import discord
from discord.ext import commands
from discord import app_commands
import logging
from datetime import datetime, timezone
# Utils functionality moved to core utilities cog

logger = logging.getLogger(__name__)

class TempVoiceSystem(commands.Cog):
    """Cog for handling the temporary voice channels system functionality"""
    
    def __init__(self, bot):
        self.bot = bot
        self.db = bot.db
        
    async def cog_load(self):
        """Called when the cog is loaded"""
        logger.info("Loading Temp Voice System cog...")
        
        # Add persistent views
        from .views import TempVoiceView
        self.bot.add_view(TempVoiceView(self.db))
        
        # Cleanup temp voice channels on startup
        await self.cleanup_temp_voice_channels()
        
        logger.info("Temp Voice System cog loaded successfully")
        
    async def cog_unload(self):
        """Called when the cog is unloaded"""
        logger.info("Unloading Temp Voice System cog...")
        logger.info("Temp Voice System cog unloaded")

    @commands.Cog.listener()
    async def on_voice_state_update(self, member, before, after):
        """Handle voice state updates for TempVoice management"""
        try:
            # Skip TempVoice functionality if server owner doesn't have subscription
            if member.guild.owner_id and not self.db.is_server_premium_for_user(member.guild.id, member.guild.owner_id):
                logger.debug(f"Skipping TempVoice for {member.guild.name} - no premium subscription")
                return

            # Skip TempVoice functionality if tempvoice system is disabled
            tempvoice_enabled = self.db.is_system_enabled(member.guild.id, "tempvoice")
            logger.info(f"TempVoice enabled for {member.guild.name}: {tempvoice_enabled}")
            if not tempvoice_enabled:
                logger.warning(f"Skipping TempVoice for {member.guild.name} - tempvoice disabled")
                return

            # Handle user leaving a voice channel
            if before.channel and not after.channel:
                logger.debug(f"{member.display_name} left voice channel {before.channel.name}")
                await self.handle_user_left_voice(member, before.channel)

            # Handle user joining a voice channel
            elif not before.channel and after.channel:
                logger.info(f"{member.display_name} joined voice channel {after.channel.name} (ID: {after.channel.id}) in {member.guild.name}")
                await self.handle_user_joined_voice(member, after.channel)

            # Handle user moving between channels
            elif before.channel and after.channel and before.channel != after.channel:
                logger.debug(f"{member.display_name} moved from {before.channel.name} to {after.channel.name}")
                await self.handle_user_left_voice(member, before.channel)
                await self.handle_user_joined_voice(member, after.channel)

        except Exception as e:
            logger.error(f"Error in temp voice voice state update handler: {e}", exc_info=True)

    @commands.Cog.listener()
    async def on_channel_delete(self, channel):
        """Handle channel deletion for temp voice cleanup"""
        try:
            # Handle temporary voice channel deletion
            if isinstance(channel, discord.VoiceChannel):
                temp_channel_data = self.db.get_temp_channel(channel.id)
                if temp_channel_data:
                    self.db.delete_temp_channel(channel.id)
                    logger.info(f"Cleaned up database entry for deleted temp voice channel: {channel.name}")
        except Exception as e:
            logger.error(f"Error handling temp voice channel delete: {e}")

    async def handle_user_left_voice(self, member, channel):
        """Handle when a user leaves a voice channel"""
        try:
            # Check if this is a temporary voice channel
            temp_channel_data = self.db.get_temp_channel(channel.id)
            if not temp_channel_data:
                return

            # If channel is now empty, delete it
            if len(channel.members) == 0:
                try:
                    await channel.delete(reason="Temporary voice channel is empty")
                    self.db.delete_temp_channel(channel.id)
                except Exception as e:
                    logger.error(f"Error deleting empty temp channel: {e}")

            # If the owner left but others remain, they can claim ownership
            elif temp_channel_data['owner_id'] == member.id and len(channel.members) > 0:
                # The channel remains open for others to claim ownership
                logger.info(f"Owner {member.display_name} left temp channel {channel.name}, others can claim ownership")

        except Exception as e:
            logger.error(f"Error handling user left voice: {e}")

    async def handle_user_joined_voice(self, member, channel):
        """Handle when a user joins a voice channel"""
        try:
            # Check if this is the Creator channel - auto-create temp channel
            settings = self.db.get_tempvoice_settings(member.guild.id)
            logger.debug(f"TempVoice settings for {member.guild.name}: {settings}")

            if settings and str(channel.id) == str(settings['creator_channel_id']):
                logger.info(f"{member.display_name} joined creator channel {channel.name} - creating temp channel")
                await self.auto_create_temp_channel(member, channel)
                return
            elif settings:
                logger.info(f"Channel {channel.name} (ID: {channel.id}) is not the creator channel (ID: {settings.get('creator_channel_id')}) - types: {type(channel.id)} vs {type(settings.get('creator_channel_id'))}")
            else:
                logger.warning(f"No TempVoice settings found for guild {member.guild.name} (ID: {member.guild.id})")

            # Check if this is a temporary voice channel
            temp_channel_data = self.db.get_temp_channel(channel.id)
            if not temp_channel_data:
                return

            # Check if user is blocked from this channel
            blocked_users = temp_channel_data.get('blocked_users', [])
            if member.id in blocked_users:
                try:
                    # Move user out of the channel
                    await member.move_to(None)
                    # Send them a DM if possible
                    try:
                        await member.send(f"❌ You are blocked from accessing the temporary voice channel **{channel.name}**.")
                    except:
                        pass  # User might have DMs disabled
                except Exception as e:
                    logger.error(f"Error moving blocked user {member.display_name}: {e}")

        except Exception as e:
            logger.error(f"Error handling user joined voice: {e}")

    async def auto_create_temp_channel(self, member, creator_channel):
        """Auto-create a temporary voice channel when user joins the Creator channel"""
        try:
            logger.info(f"Starting auto_create_temp_channel for {member.display_name} in {member.guild.name}")

            # Check if user already has a temp channel
            existing_channel = self.db.get_user_temp_channel(member.guild.id, member.id)
            logger.info(f"Existing channel check for {member.display_name}: {existing_channel}")
            if existing_channel:
                channel = member.guild.get_channel(existing_channel['channel_id'])
                if channel:
                    # Move user to their existing channel
                    await member.move_to(channel)
                    return
                else:
                    # Channel was deleted but not cleaned up in database
                    self.db.delete_temp_channel(existing_channel['channel_id'])

            # Get TempVoice settings for default user limit
            settings = self.db.get_tempvoice_settings(member.guild.id)
            default_limit = settings.get('default_user_limit') if settings else None

            # Create the temporary voice channel
            # Copy permissions from creator channel
            overwrites = creator_channel.overwrites.copy()

            # Ensure @everyone permissions are copied from creator channel
            if member.guild.default_role in creator_channel.overwrites:
                overwrites[member.guild.default_role] = creator_channel.overwrites[member.guild.default_role]

            channel_name = f"{member.display_name}'s Channel"
            logger.info(f"Creating temp channel: {channel_name[:100]} in category {creator_channel.category} with limit {default_limit}")

            temp_channel = await member.guild.create_voice_channel(
                name=channel_name[:100],  # Ensure channel name is within Discord's limit
                category=creator_channel.category,
                overwrites=overwrites,
                user_limit=default_limit  # Apply default user limit
            )

            # Save to database
            success = self.db.create_temp_channel(member.guild.id, member.id, temp_channel.id)
            if not success:
                await temp_channel.delete(reason="Failed to save to database")
                logger.error(f"Failed to save temp channel to database for {member.display_name}")
                return

            # Move user to their new channel
            await member.move_to(temp_channel)
            logger.info(f"Auto-created temp channel for {member.display_name}: {temp_channel.name}")

            # Log to dashboard
            self.db.log_bot_activity(
                member.guild.id,
                member.id,
                f"{member.name}#{member.discriminator}",
                "Temporary voice channel created",
                f"Channel: {temp_channel.name}, Category: {creator_channel.category.name if creator_channel.category else 'None'}",
                "tempvoice",
                temp_channel.id
            )

        except Exception as e:
            logger.error(f"Error auto-creating temp channel: {e}")

    async def cleanup_temp_voice_channels(self):
        """Cleanup temp voice channels on bot startup - remove empty channels and handle deleted channels"""
        try:
            logger.info("Starting temp voice channel cleanup...")

            # Get all active temp channels from database
            active_channels = self.db.get_all_active_temp_channels()
            cleaned_count = 0

            for channel_data in active_channels:
                try:
                    guild = self.bot.get_guild(channel_data['server_id'])
                    if not guild:
                        # Guild not found, mark channel as deleted
                        self.db.delete_temp_channel(channel_data['channel_id'])
                        cleaned_count += 1
                        continue

                    channel = guild.get_channel(channel_data['channel_id'])
                    if not channel:
                        # Channel was deleted manually, clean up database
                        self.db.delete_temp_channel(channel_data['channel_id'])
                        cleaned_count += 1
                        continue

                    # Check if channel is empty
                    if len(channel.members) == 0:
                        try:
                            await channel.delete(reason="Empty temporary voice channel cleanup on bot restart")
                            self.db.delete_temp_channel(channel_data['channel_id'])
                            cleaned_count += 1
                            logger.info(f"Deleted empty temp channel: {channel.name}")
                        except Exception as e:
                            logger.error(f"Error deleting empty temp channel {channel.name}: {e}")

                except Exception as e:
                    logger.error(f"Error processing temp channel {channel_data.get('channel_id', 'unknown')}: {e}")

            if cleaned_count > 0:
                logger.info(f"Temp voice cleanup completed: {cleaned_count} channels cleaned up")
            else:
                logger.info("Temp voice cleanup completed: no channels needed cleanup")

        except Exception as e:
            logger.error(f"Error during temp voice channel cleanup: {e}", exc_info=True)

    @app_commands.command(name="test-tempvoice", description="Test tempvoice channel creation (admin only)")
    async def test_tempvoice_command(self, interaction: discord.Interaction):
        """Test tempvoice channel creation"""
        # Check if server owner has subscription
        if interaction.guild.owner_id and not self.db.is_server_premium_for_user(interaction.guild.id, interaction.guild.owner_id):
            await interaction.response.send_message("❌ This server requires a premium subscription. Only the server owner can subscribe.", ephemeral=True)
            return

        # Check permissions
        if not interaction.user.guild_permissions.administrator:
            await interaction.response.send_message("❌ You need administrator permissions to use this command.", ephemeral=True)
            return

        # Check if user is in a voice channel
        if not interaction.user.voice or not interaction.user.voice.channel:
            await interaction.response.send_message("❌ You need to be in a voice channel to test this.", ephemeral=True)
            return

        await interaction.response.defer(ephemeral=True)

        try:
            # Get the creator channel to copy permissions from
            settings = self.db.get_tempvoice_settings(interaction.guild.id)
            if not settings:
                await interaction.followup.send("❌ TempVoice system is not configured for this server.", ephemeral=True)
                return

            creator_channel = interaction.guild.get_channel(settings['creator_channel_id'])
            if not creator_channel or not isinstance(creator_channel, discord.VoiceChannel):
                await interaction.followup.send("❌ Creator voice channel not found or invalid.", ephemeral=True)
                return

            await interaction.followup.send(f"💡 **Tip:** Join {creator_channel.mention} to automatically create your temporary voice channel!\n\nAlternatively, I can create one for you now if you prefer.", ephemeral=True)

        except Exception as e:
            logger.error(f"Error in test tempvoice command: {e}")
            await interaction.followup.send("❌ An error occurred while testing the tempvoice system.", ephemeral=True)


async def setup(bot):
    """Setup function for the cog"""
    await bot.add_cog(TempVoiceSystem(bot))
