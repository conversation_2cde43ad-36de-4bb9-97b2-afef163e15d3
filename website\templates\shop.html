{% extends "base.html" %}

{% block title %}Shop - <PERSON><PERSON><PERSON><PERSON>{% endblock %}

{% block styles %}
<style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
    
    :root {
        --primary-color: #5865f2;
        --secondary-color: #7289da;
        --accent-color: #00d4ff;
        --bg-primary: #0a0a0f;
        --bg-secondary: #101218;
        --bg-tertiary: #1a1d29;
        --text-primary: #ffffff;
        --text-secondary: #b9bbbe;
        --text-muted: #72767d;
        --border-color: rgba(255, 255, 255, 0.1);
        --glow-primary: rgba(88, 101, 242, 0.5);
        --glow-accent: rgba(0, 212, 255, 0.3);
    }

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        background: var(--bg-primary);
        color: var(--text-primary);
        overflow-x: hidden;
        line-height: 1.6;
    }

    /* Animated Background */
    .animated-bg {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        background: linear-gradient(45deg, #0a0a0f 25%, transparent 25%),
                    linear-gradient(-45deg, #0a0a0f 25%, transparent 25%),
                    linear-gradient(45deg, transparent 75%, #0a0a0f 75%),
                    linear-gradient(-45deg, transparent 75%, #0a0a0f 75%);
        background-size: 60px 60px;
        background-position: 0 0, 0 30px, 30px -30px, -30px 0px;
        animation: backgroundMove 20s linear infinite;
        opacity: 0.03;
    }

    @keyframes backgroundMove {
        0% { background-position: 0 0, 0 30px, 30px -30px, -30px 0px; }
        100% { background-position: 60px 60px, 60px 90px, 90px 30px, 30px 60px; }
    }

    /* Shop Header */
    .shop-header {
        padding: 120px 20px 60px;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .shop-header::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 600px;
        height: 600px;
        background: radial-gradient(circle, rgba(88, 101, 242, 0.1) 0%, transparent 70%);
        transform: translate(-50%, -50%);
        animation: pulse 4s ease-in-out infinite;
    }

    @keyframes pulse {
        0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.5; }
        50% { transform: translate(-50%, -50%) scale(1.1); opacity: 0.8; }
    }

    .shop-title {
        font-size: clamp(2rem, 5vw, 4rem);
        font-weight: 900;
        margin-bottom: 20px;
        background: linear-gradient(135deg, var(--text-primary) 0%, var(--primary-color) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        position: relative;
        z-index: 2;
    }

    .shop-subtitle {
        font-size: 1.3rem;
        color: var(--text-secondary);
        margin-bottom: 40px;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
        position: relative;
        z-index: 2;
    }

    /* Products Grid */
    .products-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px 80px;
    }

    /* Donate Card */
    .donate-card {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.03) 0%, rgba(88, 101, 242, 0.05) 100%);
        border: 1px solid rgba(88, 101, 242, 0.2);
        border-radius: 20px;
        padding: 40px;
        margin-bottom: 40px;
        text-align: center;
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(20px);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .donate-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, #ff6b6b, #ffd93d, #6bcf7f, #4ecdc4, #45b7d1, #96ceb4, #ffeaa7);
        background-size: 300% 100%;
        animation: rainbowGlow 3s ease-in-out infinite;
    }

    @keyframes rainbowGlow {
        0%, 100% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
    }

    .donate-card:hover {
        transform: translateY(-5px);
        border-color: rgba(88, 101, 242, 0.4);
        box-shadow: 0 20px 40px rgba(88, 101, 242, 0.15);
    }

    .donate-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 30px;
        max-width: 1000px;
        margin: 0 auto;
    }

    .donate-info {
        flex: 1;
        text-align: left;
    }

    .donate-icon {
        font-size: 2.5rem;
        margin-bottom: 15px;
        background: linear-gradient(135deg, #ff6b6b, #ffd93d);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        display: inline-block;
    }

    .donate-title {
        font-size: 2rem;
        font-weight: 800;
        margin-bottom: 10px;
        background: linear-gradient(135deg, var(--text-primary) 0%, var(--accent-color) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .donate-description {
        color: var(--text-secondary);
        font-size: 1.1rem;
        line-height: 1.6;
        margin-bottom: 0;
    }

    .donate-button-container {
        flex-shrink: 0;
    }

    .donate-button {
        padding: 18px 40px;
        background: linear-gradient(135deg, #ff6b6b, #ffd93d);
        color: #ffffff;
        border: none;
        border-radius: 15px;
        font-weight: 700;
        font-size: 1.1rem;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .donate-button:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(255, 107, 107, 0.4);
        background: linear-gradient(135deg, #ff5252, #ffcc02);
    }

    .products-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 30px;
        margin-top: 40px;
    }

    .product-card {
        background: rgba(255, 255, 255, 0.02);
        border: 1px solid var(--border-color);
        border-radius: 20px;
        padding: 30px;
        text-align: center;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        backdrop-filter: blur(20px);
        position: relative;
        overflow: hidden;
    }

    .product-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .product-card:hover {
        transform: translateY(-10px);
        border-color: rgba(88, 101, 242, 0.3);
        box-shadow: 0 20px 40px rgba(88, 101, 242, 0.2);
    }

    .product-card:hover::before {
        opacity: 1;
    }

    .product-icon {
        font-size: 3rem;
        margin-bottom: 20px;
        background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .product-title {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 10px;
        color: var(--text-primary);
    }

    .product-description {
        color: var(--text-secondary);
        margin-bottom: 20px;
        line-height: 1.6;
    }

    .product-delivery {
        font-size: 0.9rem;
        color: var(--accent-color);
        margin-bottom: 25px;
        font-weight: 500;
    }

    /* Buy Button Styling */
    .buy-button {
        width: 100%;
        padding: 15px 30px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: var(--text-primary);
        border: none;
        border-radius: 12px;
        font-weight: 600;
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 6px 20px rgba(88, 101, 242, 0.3);
    }

    .buy-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 30px rgba(88, 101, 242, 0.5);
    }

    /* Subscription Styles */
    .auth-notice {
        background: linear-gradient(135deg, rgba(88, 101, 242, 0.1) 0%, rgba(0, 212, 255, 0.1) 100%);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        padding: 3rem;
        text-align: center;
        margin-bottom: 2rem;
    }

    .auth-content .auth-icon {
        font-size: 3rem;
        color: var(--primary-color);
        margin-bottom: 1rem;
    }

    .subscription-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }

    .subscription-header {
        grid-column: 1 / -1;
        text-align: center;
        margin-bottom: 2rem;
    }

    .subscription-main-title {
        font-size: 3rem;
        font-weight: 700;
        background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 0.5rem;
    }

    .subscription-subtitle {
        font-size: 1.2rem;
        color: var(--text-secondary);
        margin: 0;
    }

    .subscription-card {
        background: var(--bg-secondary);
        border: 2px solid var(--border-color);
        border-radius: var(--border-radius);
        padding: 2rem;
        text-align: center;
        position: relative;
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .subscription-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        border-color: var(--primary-color);
    }

    .subscription-card.featured {
        border-color: var(--primary-color);
        background: linear-gradient(135deg, rgba(88, 101, 242, 0.05) 0%, rgba(0, 212, 255, 0.05) 100%);
        transform: scale(1.05);
    }

    .subscription-badge {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: var(--primary-color);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .subscription-icon {
        font-size: 3rem;
        color: var(--primary-color);
        margin-bottom: 1rem;
    }

    .subscription-title {
        font-size: 1.8rem;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 1rem;
    }

    .subscription-price {
        margin-bottom: 1rem;
    }

    .price-amount {
        font-size: 3rem;
        font-weight: 700;
        color: var(--primary-color);
    }

    .price-period {
        font-size: 1.2rem;
        color: var(--text-secondary);
    }

    .savings-badge {
        background: var(--success-color);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.9rem;
        font-weight: 600;
        display: inline-block;
        margin-bottom: 1rem;
    }

    .subscription-features {
        list-style: none;
        padding: 0;
        margin: 2rem 0;
        text-align: left;
    }

    .subscription-features li {
        padding: 0.5rem 0;
        color: var(--text-primary);
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .subscription-features i {
        color: var(--success-color);
        font-size: 1rem;
        width: 1rem;
    }

    .subscription-button {
        background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: var(--border-radius);
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        width: 100%;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .subscription-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(88, 101, 242, 0.3);
    }

    .features-overview {
        background: var(--bg-secondary);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        padding: 3rem;
        margin: 3rem 0;
        text-align: center;
    }

    .features-overview h3 {
        font-size: 2rem;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 2rem;
    }

    .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
        margin-top: 2rem;
    }

    .feature-item {
        text-align: center;
        padding: 1.5rem;
        background: var(--bg-tertiary);
        border-radius: var(--border-radius);
        border: 1px solid var(--border-color);
        transition: all 0.3s ease;
    }

    .feature-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    }

    .feature-item i {
        font-size: 2.5rem;
        color: var(--primary-color);
        margin-bottom: 1rem;
    }

    .feature-item h4 {
        font-size: 1.2rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
    }

    .feature-item p {
        color: var(--text-secondary);
        margin: 0;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .products-grid {
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .product-card {
            padding: 25px;
        }

        .shop-header {
            padding: 100px 20px 40px;
        }

        .donate-card {
            padding: 30px 20px;
            margin-bottom: 30px;
        }

        .donate-content {
            flex-direction: column;
            text-align: center;
            gap: 25px;
        }

        .donate-info {
            text-align: center;
        }

        .donate-title {
            font-size: 1.6rem;
        }

        .donate-description {
            font-size: 1rem;
        }

        .donate-button {
            padding: 15px 35px;
            font-size: 1rem;
            width: 100%;
            max-width: 280px;
        }
    }

    /* Animation for cards */
    .product-card {
        animation: slideInUp 0.6s ease-out;
    }

    .product-card:nth-child(1) { animation-delay: 0.1s; }
    .product-card:nth-child(2) { animation-delay: 0.2s; }
    .product-card:nth-child(3) { animation-delay: 0.3s; }
    .product-card:nth-child(4) { animation-delay: 0.4s; }
    .product-card:nth-child(5) { animation-delay: 0.5s; }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="animated-bg"></div>

<div class="shop-header">
    <h1 class="shop-title">Ryzuo Premium</h1>
    <p class="shop-subtitle">Unlock premium features for UNLIMITED* servers</p>
</div>

<div class="products-container">
    <!-- Authentication Check -->
    <div id="auth-required" class="auth-notice" style="display: none;">
        <div class="auth-content">
            <div class="auth-icon">
                <i class="fas fa-sign-in-alt"></i>
            </div>
            <h3>Sign In Required</h3>
            <p>Please sign in with Discord to purchase a subscription</p>
            <a href="/login" class="btn btn-primary">
                <i class="fab fa-discord me-2"></i>Sign In with Discord
            </a>
        </div>
    </div>

    <!-- Subscription Plans -->
    <div id="subscription-plans" class="subscription-grid">

        <!-- Weekly Plan -->
        <div class="subscription-card">
            <div class="subscription-badge">Most Flexible</div>
            <div class="subscription-icon">
                <i class="fas fa-calendar-week"></i>
            </div>
            <h3 class="subscription-title">Weekly</h3>
            <div class="subscription-price">
                <span class="price-amount">$2.99</span>
                <span class="price-period">/week</span>
            </div>
            <ul class="subscription-features">
                <li><i class="fas fa-check"></i> All premium features</li>
                <li><i class="fas fa-check"></i> Unlimited* servers</li>
                <li><i class="fas fa-check"></i> Priority support</li>
                <li><i class="fas fa-check"></i> Cancel anytime</li>
            </ul>
            <button class="subscription-button" onclick="purchaseSubscription('weekly')">
                Choose Weekly
            </button>
        </div>

        <!-- Monthly Plan -->
        <div class="subscription-card featured">
            <div class="subscription-badge">Most Popular</div>
            <div class="subscription-icon">
                <i class="fas fa-calendar-alt"></i>
            </div>
            <h3 class="subscription-title">Monthly</h3>
            <div class="subscription-price">
                <span class="price-amount">$9.99</span>
                <span class="price-period">/month</span>
            </div>
            <div class="savings-badge">Save 17%</div>
            <ul class="subscription-features">
                <li><i class="fas fa-check"></i> All premium features</li>
                <li><i class="fas fa-check"></i> Unlimited* servers</li>
                <li><i class="fas fa-check"></i> Priority support</li>
                <li><i class="fas fa-check"></i> Cancel anytime</li>
            </ul>
            <button class="subscription-button" onclick="purchaseSubscription('monthly')">
                Choose Monthly
            </button>
        </div>

        <!-- Yearly Plan -->
        <div class="subscription-card">
            <div class="subscription-badge">Best Value</div>
            <div class="subscription-icon">
                <i class="fas fa-calendar"></i>
            </div>
            <h3 class="subscription-title">Yearly</h3>
            <div class="subscription-price">
                <span class="price-amount">$99.99</span>
                <span class="price-period">/year</span>
            </div>
            <div class="savings-badge">Save 36%</div>
            <ul class="subscription-features">
                <li><i class="fas fa-check"></i> All premium features</li>
                <li><i class="fas fa-check"></i> Unlimited* servers</li>
                <li><i class="fas fa-check"></i> Priority support</li>
                <li><i class="fas fa-check"></i> Cancel anytime</li>
                <li><i class="fas fa-star"></i> Exclusive yearly perks</li>
            </ul>
            <button class="subscription-button" onclick="purchaseSubscription('yearly')">
                Choose Yearly
            </button>
        </div>
    </div>

    <!-- Features Overview -->
    <div class="features-overview">
        <h3>What's Included in Ryzuo Premium?</h3>
        <div class="features-grid">
            <div class="feature-item">
                <i class="fas fa-robot"></i>
                <h4>Auto-Role System</h4>
                <p>Automatically assign roles based on user activity</p>
            </div>
            <div class="feature-item">
                <i class="fas fa-music"></i>
                <h4>Music System</h4>
                <p>High-quality music bot with playlist support</p>
            </div>
            <div class="feature-item">
                <i class="fas fa-microphone"></i>
                <h4>Temp Voice Channels</h4>
                <p>Dynamic voice channels that auto-create and delete</p>
            </div>
            <div class="feature-item">
                <i class="fas fa-thumbs-up"></i>
                <h4>Rep System</h4>
                <p>Reward active members with reputation points</p>
            </div>
            <div class="feature-item">
                <i class="fas fa-gift"></i>
                <h4>Giveaway System</h4>
                <p>Create and manage server giveaways easily</p>
            </div>
            <div class="feature-item">
                <i class="fas fa-headset"></i>
                <h4>DM Support</h4>
                <p>Professional support ticket system</p>
            </div>
        </div>
    </div>

    <!-- Old product cards removed - subscription model only -->
</div>

<script>
// Check authentication status on page load
document.addEventListener('DOMContentLoaded', function() {
    checkAuthStatus();

    // Debug and force navbar dropdown initialization
    setTimeout(function() {
        const userDropdownToggle = document.getElementById('userDropdownToggle');
        const userDropdown = document.querySelector('.user-dropdown');

        if (userDropdownToggle && userDropdown) {
            userDropdownToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                userDropdown.classList.toggle('active');
            });
        } else {
            console.log('Shop page: Dropdown elements not found');
        }

        // Also try the global init function
        if (typeof window.initNavbar === 'function') {
            window.initNavbar();
        }
    }, 500);
});

function checkAuthStatus() {
    // Check if user is authenticated by looking for user data in session
    fetch('/api/user-info')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.user) {
                // User is authenticated, show subscription plans
                document.getElementById('auth-required').style.display = 'none';
                document.getElementById('subscription-plans').style.display = 'grid';
            } else {
                // User is not authenticated, show auth notice
                document.getElementById('auth-required').style.display = 'block';
                document.getElementById('subscription-plans').style.display = 'none';
            }
        })
        .catch(error => {
            console.error('Error checking auth status:', error);
            // Default to showing auth notice on error
            document.getElementById('auth-required').style.display = 'block';
            document.getElementById('subscription-plans').style.display = 'none';
        });
}

function purchaseSubscription(tier) {
    // Validate user is authenticated
    fetch('/api/user-info')
        .then(response => response.json())
        .then(data => {
            if (!data.success || !data.user) {
                alert('Please sign in with Discord first');
                window.location.href = '/login';
                return;
            }

            // Create Stripe checkout session
            return fetch('/api/create-checkout-session', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    subscription_tier: tier,
                    user_id: data.user.id
                })
            });
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.checkout_url) {
                // Redirect to Stripe checkout
                window.location.href = data.checkout_url;
            } else {
                alert('Error creating checkout session: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error purchasing subscription:', error);
            alert('An error occurred while processing your request');
        });
}

// Stripe payment link URLs (these will need to be updated with actual Stripe payment links)
const STRIPE_PAYMENT_LINKS = {
    weekly: 'https://buy.stripe.com/test_weekly_link',
    monthly: 'https://buy.stripe.com/test_monthly_link',
    yearly: 'https://buy.stripe.com/test_yearly_link'
};

function purchaseSubscriptionDirect(tier) {
    // Alternative method using direct Stripe payment links
    const paymentLink = STRIPE_PAYMENT_LINKS[tier];
    if (paymentLink) {
        window.open(paymentLink, '_blank');
    } else {
        alert('Payment link not configured for ' + tier + ' subscription');
    }
}
</script>

{% endblock %}
