﻿<!DOCTYPE html>
<html lang="en" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{% block title %}ryzuo Bot Dashboard{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.sell.app/embed/script.js" type="module"></script>
    <link href="https://cdn.sell.app/embed/style.css" rel="stylesheet"/>
    <link rel="icon" href="{{ url_for('static', filename='favicon.ico') }}">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

        :root {
            --primary-color: #5865f2;
            --secondary-color: #7289da;
            --accent-color: #00d4ff;
            --bg-primary: #0a0a0f;
            --bg-secondary: #101218;
            --bg-tertiary: #1a1d29;
            --bg-hover: #2d3748;
            --text-primary: #ffffff;
            --text-secondary: #b9bbbe;
            --text-muted: #72767d;
            --border-color: rgba(255, 255, 255, 0.1);
            --glow-primary: rgba(88, 101, 242, 0.5);
            --glow-accent: rgba(0, 212, 255, 0.3);
            --success-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --info-color: #3b82f6;
            --sidebar-width: 280px;
            --header-height: 70px;
            --transition-speed: 0.3s;
            --border-radius: 20px;
            --border-radius-sm: 10px;
            --box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
            --box-shadow-hover: 0 20px 50px rgba(0, 0, 0, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            overflow-x: hidden;
            line-height: 1.6;
            -webkit-text-size-adjust: 100%;
            -webkit-tap-highlight-color: transparent;
            touch-action: manipulation;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Animated Background */
        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: linear-gradient(45deg, #0a0a0f 25%, transparent 25%),
                        linear-gradient(-45deg, #0a0a0f 25%, transparent 25%),
                        linear-gradient(45deg, transparent 75%, #0a0a0f 75%),
                        linear-gradient(-45deg, transparent 75%, #0a0a0f 75%);
            background-size: 60px 60px;
            background-position: 0 0, 0 30px, 30px -30px, -30px 0px;
            animation: backgroundMove 20s linear infinite;
            opacity: 0.03;
        }

        @keyframes backgroundMove {
            0% { background-position: 0 0, 0 30px, 30px -30px, -30px 0px; }
            100% { background-position: 60px 60px, 60px 90px, 90px 30px, 30px 60px; }
        }

        /* Navigation */
        .navbar {
            height: var(--header-height);
            background: rgba(16, 18, 24, 0.95) !important;
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border-color);
            padding: 0 1.5rem;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }

        /* Header from navbar.html */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: rgba(10, 10, 15, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border-color);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.4rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--text-primary) !important;
            transition: all var(--transition-speed) ease;
        }

        .navbar-brand:hover {
            color: var(--primary-color) !important;
        }

        .nav-link {
            color: var(--text-secondary);
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius-sm);
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all var(--transition-speed) cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .nav-link:hover, .nav-link.active {
            background: rgba(88, 101, 242, 0.1);
            color: var(--primary-color);
            transform: translateY(-1px);
        }

        .nav-link i {
            width: 20px;
            text-align: center;
        }

        /* Navigation and Notification Styles */
        .user-nav {
            display: flex;
            align-items: center;
            gap: 8px; /* This controls the space between notification bell and user profile */
        }

        .nav-item.notification-link {
            display: flex;
            align-items: center;
        }

        .notification-bell {
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-primary);
            font-size: 1.2rem;
            padding: 8px;
            border-radius: 50%;
            transition: all 0.2s ease;
            text-decoration: none;
        }

        .notification-bell:hover {
            color: var(--primary-color);
            transform: scale(1.1);
        }

        .notification-badge {
            position: absolute;
            top: 0;
            right: 0;
            background: var(--danger-color);
            color: white;
            border-radius: 50%;
            min-width: 18px;
            height: 18px;
            font-size: 11px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2px;
            border: 2px solid var(--bg-primary);
            font-weight: bold;
        }

        .notification-badge.new {
            animation: notificationPulse 1s ease infinite;
        }

        @keyframes notificationPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }

        /* Main Content */
        .main-content {
            padding: 2rem;
            margin-top: calc(var(--header-height) + 20px); /* Add extra spacing from navbar */
            position: relative;
            z-index: 1;
            min-height: 70vh; /* Ensure enough content height to push footer down */
        }

        /* Server Card */
        .server-card {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            overflow: hidden;
            transition: all var(--transition-speed) cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(20px);
            position: relative;
        }

        .server-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }

        .server-card:hover::before {
            transform: translateX(0);
        }

        .server-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.05);
            box-shadow: var(--box-shadow-hover);
        }

        .server-card.disabled {
            opacity: 0.7;
            position: relative;
        }

        .server-card.disabled::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
        }

        .server-icon {
            width: 64px;
            height: 64px;
            border-radius: 50%;
            object-fit: cover;
        }

        .server-initials {
            width: 64px;
            height: 64px;
            border-radius: 50%;
            background: var(--bg-tertiary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--text-primary);
        }

        /* Buttons */
        .btn {
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            transition: all var(--transition-speed) cubic-bezier(0.4, 0, 0.2, 1);
            border: none;
            cursor: pointer;
            font-size: 1rem;
            min-height: 44px;
            -webkit-tap-highlight-color: transparent;
            user-select: none;
        }

        .btn i {
            font-size: 0.9em;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: var(--text-primary);
            box-shadow: 0 6px 20px rgba(88, 101, 242, 0.4);
            border: none;
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(88, 101, 242, 0.6);
            color: var(--text-primary);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.05);
            color: var(--text-primary);
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
        }

        .btn-outline-primary {
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
            background: transparent;
        }

        .btn-outline-primary:hover {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        .btn-sm {
            padding: 8px 16px;
            font-size: 0.875rem;
            min-height: 36px;
        }

        /* Dashboard specific styles */
        .sidebar {
            background: rgba(255, 255, 255, 0.03);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            height: calc(100vh - 140px); /* Account for navbar (70px) + padding (70px) */
            backdrop-filter: blur(20px);
            position: sticky;
            top: 90px; /* Navbar height (70px) + some spacing (20px) */
            overflow-y: auto;
            overflow-x: hidden;
            z-index: 10;
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
        }

        /* Sidebar Navigation Styles */
        .sidebar .nav-link {
            color: var(--text-secondary);
            padding: 12px 16px;
            border-radius: 12px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 12px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            text-decoration: none;
            margin-bottom: 4px;
        }

        .sidebar .nav-link:hover {
            background: rgba(88, 101, 242, 0.1);
            color: var(--text-primary);
            transform: translateY(-2px);
        }

        .sidebar .nav-link.active {
            background: linear-gradient(135deg, rgba(88, 101, 242, 0.15), rgba(0, 212, 255, 0.1));
            color: var(--text-primary);
            border: 1px solid rgba(88, 101, 242, 0.2);
        }

        .sidebar .nav-link i {
            width: 20px;
            text-align: center;
            font-size: 1rem;
        }

        .sidebar .nav-link span {
            flex: 1;
        }

        .nav-indicator {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            width: 6px;
            height: 6px;
            background: var(--primary-color);
            border-radius: 50%;
            box-shadow: 0 0 10px var(--glow-primary);
        }

        .nav-section {
            padding: 0 16px;
        }

        .sidebar-header {
            position: relative;
        }

        .sidebar-header::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, var(--primary-color), transparent);
        }

        /* Ensure sidebar nav is vertical */
        .sidebar .nav {
            display: flex !important;
            flex-direction: column !important;
            gap: 4px !important;
            align-items: stretch !important;
        }

        .sidebar .nav.flex-column {
            flex-direction: column !important;
        }

        /* Fix any Bootstrap nav overrides */
        .sidebar .nav-link {
            width: 100% !important;
            display: flex !important;
            flex-direction: row !important;
            align-items: center !important;
            justify-content: flex-start !important;
            text-align: left !important;
        }

        /* Prevent any horizontal scrolling or layout issues */
        .sidebar {
            overflow-x: hidden !important;
        }

        /* Ensure nav sections are also vertical */
        .sidebar .nav-section {
            width: 100% !important;
            display: block !important;
        }

        /* Additional Bootstrap nav overrides */
        .sidebar .nav.nav-pills,
        .sidebar .nav.nav-tabs,
        .sidebar .nav {
            flex-direction: column !important;
            flex-wrap: nowrap !important;
        }

        /* Prevent any flex-row classes from affecting sidebar */
        .sidebar .nav.flex-row {
            flex-direction: column !important;
        }

        /* Ensure sidebar container is properly constrained */
        .col-md-3 .sidebar {
            width: 100%;
            max-width: 100%;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            transition: all var(--transition-speed) cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(20px);
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }

        .feature-card:hover::before {
            transform: translateX(0);
        }

        .feature-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.05);
            box-shadow: var(--box-shadow-hover);
        }

        .server-info {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            backdrop-filter: blur(20px);
            position: relative;
            overflow: hidden;
        }

        .server-info::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
        }

        .status-badge {
            font-size: 0.875rem;
            padding: 0.375rem 0.75rem;
            border-radius: 15px;
            font-weight: 600;
        }

        /* Form improvements */
        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-sm);
            color: var(--text-primary) !important;
            padding: 12px 16px;
            transition: all var(--transition-speed) ease;
            backdrop-filter: blur(10px);
            font-size: 0.95rem;
            font-weight: 500;
        }

        .form-control:focus, .form-select:focus {
            background: rgba(255, 255, 255, 0.08);
            border-color: var(--primary-color);
            color: var(--text-primary) !important;
            box-shadow: 0 0 0 0.2rem rgba(88, 101, 242, 0.25);
            outline: none;
        }

        .form-control:hover, .form-select:hover {
            background: rgba(255, 255, 255, 0.07);
            border-color: rgba(88, 101, 242, 0.5);
        }

        .form-control::placeholder {
            color: var(--text-muted);
        }

        /* Enhanced select dropdown styling */
        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 16px 12px;
            padding-right: 40px;
        }

        .form-select option {
            background: var(--bg-secondary);
            color: var(--text-primary);
            padding: 8px 12px;
            border: none;
        }

        .form-select option:hover {
            background: var(--bg-hover);
        }

        .form-select option:checked {
            background: var(--primary-color);
            color: white;
        }

        /* Optgroup (category) styling for dropdowns */
        .form-select optgroup {
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            font-weight: 600;
            font-size: 0.9rem;
            padding: 8px 12px;
            border: none;
        }

        .form-select optgroup option {
            background: var(--bg-secondary);
            color: var(--text-primary);
            padding-left: 20px;
            font-weight: 500;
        }

        .form-label {
            color: var(--text-secondary);
            font-weight: 500;
            margin-bottom: 8px;
        }

        .form-text {
            color: var(--text-muted);
            font-size: 0.875rem;
        }

        /* Card improvements */
        .card {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            backdrop-filter: blur(20px);
            position: relative;
            overflow: hidden;
            transition: all var(--transition-speed) cubic-bezier(0.4, 0, 0.2, 1);
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }

        .card:hover::before {
            transform: translateX(0);
        }

        .card:hover {
            transform: translateY(-2px);
            background: rgba(255, 255, 255, 0.05);
            box-shadow: var(--box-shadow);
        }

        .card-header {
            background: rgba(255, 255, 255, 0.05);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 1.5rem;
            font-weight: 600;
        }

        .card-body {
            padding: 1.5rem;
        }

        /* Alert improvements */
        .alert {
            border-radius: var(--border-radius);
            border: none;
            backdrop-filter: blur(10px);
            font-weight: 500;
        }

        .alert-warning {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
            border: 1px solid rgba(245, 158, 11, 0.2);
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .alert-info {
            background: rgba(59, 130, 246, 0.1);
            color: var(--info-color);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .alert-sm {
            padding: 0.5rem 0.75rem;
            font-size: 0.875rem;
        }

        /* Utilities */
        .text-muted {
            color: var(--text-muted) !important;
        }

        .text-secondary {
            color: var(--text-secondary) !important;
        }

        .cursor-pointer {
            cursor: pointer;
        }

        /* Loading states */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        /* Badge improvements */
        .badge {
            border-radius: 15px;
            font-weight: 600;
            padding: 0.375rem 0.75rem;
        }

        .badge.bg-success {
            background: linear-gradient(135deg, var(--success-color), #059669) !important;
        }

        .badge.bg-danger {
            background: linear-gradient(135deg, var(--danger-color), #dc2626) !important;
        }

        .badge.bg-warning {
            background: linear-gradient(135deg, var(--warning-color), #d97706) !important;
        }

        .badge.bg-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
        }

        /* Switch improvements */
        .form-check-input:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .form-check-input:focus {
            box-shadow: 0 0 0 0.2rem rgba(88, 101, 242, 0.25);
        }

        /* Animations */
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        .reveal {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .reveal.active {
            opacity: 1;
            transform: translateY(0);
        }

        /* Notification Styles */
        .notification-link {
            position: relative;
            margin-right: 1.5rem;
        }

        .notification-bell {
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-primary);
            font-size: 1.2rem;
            padding: 0.5rem;
            border-radius: 50%;
            transition: all 0.2s ease;
            text-decoration: none;
        }

        .notification-bell:hover {
            color: var(--primary-color);
            transform: scale(1.1);
        }

        .notification-badge {
            position: absolute;
            top: 0;
            right: 0;
            background: var(--danger-color);
            color: white;
            border-radius: 50%;
            min-width: 18px;
            height: 18px;
            font-size: 11px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2px;
            border: 2px solid var(--bg-primary);
            font-weight: bold;
        }

        .notification-badge.new {
            animation: notificationPulse 1s ease infinite;
        }

        @keyframes notificationPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }
    </style>
    {% block styles %}{% endblock %}
</head>
<body{% block body_attributes %}{% endblock %}>
    <!-- Animated Background -->
    <div class="animated-bg"></div>

    <!-- Navigation -->
    {% include 'navbar.html' %}

    <!-- Notification System -->
    <div id="notification-container" class="position-fixed" style="top: 90px; right: 20px; z-index: 1050; max-width: 400px;">
        <!-- Notifications will be dynamically inserted here -->
    </div>

    <!-- Notification Bell (for authenticated users) -->
    {% if session.get('user_id') %}
    <div id="notification-bell" class="position-fixed" style="top: 20px; right: 20px; z-index: 1051;">
        <div class="dropdown">
            <button class="btn btn-outline-light position-relative" type="button" id="notificationDropdown" data-bs-toggle="dropdown" aria-expanded="false" style="border-radius: 50%; width: 50px; height: 50px; border: 2px solid var(--primary-color); background: rgba(88, 101, 242, 0.1); backdrop-filter: blur(10px);">
                <i class="fas fa-bell"></i>
                <span id="notification-badge" class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style="display: none;">
                    0
                </span>
            </button>
            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="notificationDropdown" style="width: 350px; max-height: 400px; overflow-y: auto; background: var(--bg-secondary); border: 1px solid var(--border-color); border-radius: var(--border-radius);">
                <li class="dropdown-header d-flex justify-content-between align-items-center">
                    <span>Notifications</span>
                    <button id="clear-all-notifications" class="btn btn-sm btn-outline-danger" style="font-size: 0.75rem;">Clear All</button>
                </li>
                <li><hr class="dropdown-divider"></li>
                <div id="notification-list">
                    <li class="dropdown-item text-center text-muted">
                        <i class="fas fa-spinner fa-spin"></i> Loading...
                    </li>
                </div>
            </ul>
        </div>
    </div>
    {% endif %}

    <!-- Main Content -->
    <main class="main-content">
        {% if request.endpoint not in ['index', 'shop', 'status'] %}
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        {% endif %}

        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    {% include 'footer.html' %}

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Global error handler for unhandled promise rejections
        window.addEventListener('unhandledrejection', function(event) {
            console.error('Unhandled promise rejection:', event.reason);
            showGlobalError('An unexpected error occurred. Please refresh the page and try again.');
        });

        // Global error handler for JavaScript errors
        window.addEventListener('error', function(event) {
            console.error('JavaScript error:', event.error);
            // Don't show error for every JS error, only critical ones
        });

        // Global function to show errors
        window.showGlobalError = function(message, type = 'danger') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.style.position = 'fixed';
            alertDiv.style.top = '80px';
            alertDiv.style.right = '20px';
            alertDiv.style.zIndex = '9999';
            alertDiv.style.maxWidth = '400px';
            alertDiv.innerHTML = `
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alertDiv);

            // Auto-remove after 10 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 10000);
        };

        // Global function to show success messages
        window.showGlobalSuccess = function(message) {
            showGlobalError(message, 'success');
        };

        // Initialize modern animations and interactions
        function initializeModernInteractions() {
            // Animate elements on page load
            const animatedElements = document.querySelectorAll('.feature-card, .server-info, .sidebar, .stat-card, .chart-card');
            animatedElements.forEach((element, index) => {
                element.style.opacity = '0';
                element.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    element.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, index * 100);
            });

            // Add loading states to buttons (except those with no-auto-spinner class)
            const buttons = document.querySelectorAll('.btn:not(.no-auto-spinner)');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    if (this.type === 'submit' || this.closest('form')) {
                        this.style.position = 'relative';
                        this.style.overflow = 'hidden';

                        const spinner = document.createElement('span');
                        spinner.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>';
                        spinner.style.position = 'absolute';
                        spinner.style.left = '50%';
                        spinner.style.top = '50%';
                        spinner.style.transform = 'translate(-50%, -50%)';
                        spinner.style.opacity = '0';
                        spinner.style.transition = 'opacity 0.3s ease';

                        this.appendChild(spinner);

                        setTimeout(() => {
                            this.style.color = 'transparent';
                            spinner.style.opacity = '1';
                        }, 100);
                    }
                });
            });

            // Add smooth scrolling
            const links = document.querySelectorAll('a[href^="#"]:not([href="#"])');
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const href = this.getAttribute('href');
                    if (href && href !== '#') {
                        const target = document.querySelector(href);
                        if (target) {
                            target.scrollIntoView({
                                behavior: 'smooth',
                                block: 'start'
                            });
                        }
                    }
                });
            });

            // Add parallax effect to backgrounds
            window.addEventListener('scroll', () => {
                const scrolled = window.pageYOffset;
                const parallaxElements = document.querySelectorAll('.animated-bg');
                parallaxElements.forEach(element => {
                    element.style.transform = `translateY(${scrolled * 0.1}px)`;
                });
            });

            // Add intersection observer for reveal animations
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('active');
                    }
                });
            }, observerOptions);

            document.querySelectorAll('.reveal').forEach(el => {
                observer.observe(el);
            });

            // Add typing effect to code elements
            const codeElements = document.querySelectorAll('code');
            codeElements.forEach(code => {
                code.addEventListener('mouseenter', function() {
                    this.style.background = 'rgba(88, 101, 242, 0.1)';
                    this.style.borderColor = 'rgba(88, 101, 242, 0.3)';
                    this.style.transform = 'scale(1.02)';
                });

                code.addEventListener('mouseleave', function() {
                    this.style.background = 'rgba(255, 255, 255, 0.1)';
                    this.style.borderColor = 'transparent';
                    this.style.transform = 'scale(1)';
                });
            });

            // Add glow effect to important elements
            const glowElements = document.querySelectorAll('.badge, .status-badge');
            glowElements.forEach(element => {
                element.addEventListener('mouseenter', function() {
                    this.style.boxShadow = '0 0 20px rgba(88, 101, 242, 0.5)';
                });

                element.addEventListener('mouseleave', function() {
                    this.style.boxShadow = '';
                });
            });
        }

        // Add CSRF token to all AJAX requests
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize modern interactions
            initializeModernInteractions();
            // Get CSRF token from meta tag or create one
            let csrfToken = document.querySelector('meta[name="csrf-token"]')?.content;
            
            if (!csrfToken) {
                // If no CSRF token found, create a new one
                csrfToken = '{{ csrf_token() }}';
                const meta = document.createElement('meta');
                meta.name = 'csrf-token';
                meta.content = csrfToken;
                document.head.appendChild(meta);
            }
            
            // Set up AJAX to include CSRF token in all requests
            const csrfSafeMethod = (method) => {
                // These HTTP methods do not require CSRF protection
                return (/^(GET|HEAD|OPTIONS|TRACE)$/.test(method));
            };
            
            // Set up AJAX to include CSRF token in all requests
            const csrftoken = csrfToken;
            
            // Function to get cookie by name
            function getCookie(name) {
                let cookieValue = null;
                if (document.cookie && document.cookie !== '') {
                    const cookies = document.cookie.split(';');
                    for (let i = 0; i < cookies.length; i++) {
                        const cookie = cookies[i].trim();
                        // Does this cookie string begin with the name we want?
                        if (cookie.substring(0, name.length + 1) === (name + '=')) {
                            cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                            break;
                        }
                    }
                }
                return cookieValue;
            }
            
            // Set up AJAX to include CSRF token in all requests
            const csrftoken2 = getCookie('csrftoken') || csrfToken;
            
            // Set up AJAX to include CSRF token in all requests
            const xhrOpen = XMLHttpRequest.prototype.open;
            const xhrSend = XMLHttpRequest.prototype.send;

            XMLHttpRequest.prototype.open = function(method, url, ...args) {
                this._method = method;
                this._url = url;
                return xhrOpen.apply(this, [method, url, ...args]);
            };

            XMLHttpRequest.prototype.send = function(data) {
                // Only add headers for our own requests, not third-party ones like Tawk.to
                if (this._url && !this._url.includes('tawk.to') && !csrfSafeMethod(this._method) && !this._headersSet) {
                    this._headersSet = true;
                    try {
                        this.setRequestHeader('X-CSRFToken', csrftoken2);
                        this.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
                    } catch (e) {
                        // Ignore errors for third-party requests
                        console.debug('Could not set headers:', e);
                    }
                }
                return xhrSend.apply(this, [data]);
            };
            
            // Override fetch to include CSRF token
            const originalFetch = window.fetch;
            window.fetch = function(resource, options = {}) {
                // Get the URL string from resource
                const url = typeof resource === 'string' ? resource : resource.url;

                // Only modify requests to our own domain, not third-party services
                const isOwnDomain = !url || url.startsWith('/') || url.includes(window.location.hostname);

                // Set up default headers if they don't exist
                options.headers = options.headers || {};

                // Add CSRF token for non-GET requests to our own domain only
                if (isOwnDomain && options.method && !csrfSafeMethod(options.method)) {
                    options.headers = {
                        ...options.headers,
                        'X-CSRFToken': csrftoken2,
                        'X-Requested-With': 'XMLHttpRequest'
                    };

                    // Ensure credentials are included for same-origin requests
                    options.credentials = options.credentials || 'same-origin';
                }

                return originalFetch(resource, options);
            };
        });
    </script>
    {% block scripts %}{% endblock %}

    <!-- Notification System JavaScript -->
    {% if session.get('user_id') %}
    <script>
        class NotificationSystem {
            constructor() {
                this.notifications = [];
                this.unreadCount = 0;
                this.init();
            }

            init() {
                this.loadNotifications();
                this.setupEventListeners();
                // Check for new notifications every 30 seconds
                setInterval(() => this.checkForNewNotifications(), 30000);
            }

            setupEventListeners() {
                // Clear all notifications button
                document.getElementById('clear-all-notifications')?.addEventListener('click', () => {
                    this.clearAllNotifications();
                });

                // Mark notifications as read when dropdown is opened
                document.getElementById('notificationDropdown')?.addEventListener('click', () => {
                    setTimeout(() => this.markAllAsRead(), 100);
                });
            }

            async loadNotifications() {
                try {
                    const response = await fetch('/api/notifications');
                    const data = await response.json();
                    
                    if (data.success) {
                        this.notifications = data.notifications;
                        this.unreadCount = data.unread_count;
                        this.updateUI();
                    }
                } catch (error) {
                    console.error('Error loading notifications:', error);
                }
            }

            async checkForNewNotifications() {
                try {
                    const response = await fetch('/api/notifications/count');
                    const data = await response.json();
                    
                    if (data.success && data.count !== this.unreadCount) {
                        this.loadNotifications(); // Reload if count changed
                    }
                } catch (error) {
                    console.error('Error checking notifications:', error);
                }
            }

            updateUI() {
                this.updateBadge();
                this.updateDropdown();
            }

            updateBadge() {
                const badge = document.getElementById('notification-badge');
                if (badge) {
                    if (this.unreadCount > 0) {
                        badge.textContent = this.unreadCount > 99 ? '99+' : this.unreadCount;
                        badge.style.display = 'block';
                    } else {
                        badge.style.display = 'none';
                    }
                }
            }

            updateDropdown() {
                const list = document.getElementById('notification-list');
                if (!list) return;

                if (this.notifications.length === 0) {
                    list.innerHTML = '<li class="dropdown-item text-center text-muted">No notifications</li>';
                    return;
                }

                list.innerHTML = this.notifications.map(notification => {
                    const timeAgo = this.formatTimeAgo(notification.created_at);
                    const typeIcon = this.getTypeIcon(notification.type);
                    const isUnread = !notification.read;
                    
                    return `
                        <li class="dropdown-item ${isUnread ? 'bg-primary bg-opacity-10' : ''}" style="white-space: normal; padding: 12px 16px; border-bottom: 1px solid var(--border-color);">
                            <div class="d-flex align-items-start">
                                <div class="me-2 mt-1">
                                    <i class="${typeIcon}" style="color: ${this.getTypeColor(notification.type)};"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="fw-semibold mb-1" style="font-size: 0.9rem;">${notification.title}</div>
                                    <div class="text-muted mb-1" style="font-size: 0.8rem;">${notification.message}</div>
                                    <div class="text-muted" style="font-size: 0.75rem;">${timeAgo}</div>
                                </div>
                                ${isUnread ? '<div class="ms-2"><span class="badge bg-primary" style="font-size: 0.6rem;">New</span></div>' : ''}
                            </div>
                        </li>
                    `;
                }).join('');
            }

            getTypeIcon(type) {
                const icons = {
                    'success': 'fas fa-check-circle',
                    'info': 'fas fa-info-circle',
                    'warning': 'fas fa-exclamation-triangle',
                    'error': 'fas fa-times-circle',
                    'system': 'fas fa-cog'
                };
                return icons[type] || 'fas fa-bell';
            }

            getTypeColor(type) {
                const colors = {
                    'success': 'var(--success-color)',
                    'info': 'var(--info-color)',
                    'warning': 'var(--warning-color)',
                    'error': 'var(--danger-color)',
                    'system': 'var(--primary-color)'
                };
                return colors[type] || 'var(--text-secondary)';
            }

            formatTimeAgo(timestamp) {
                const now = new Date();
                const time = new Date(timestamp);
                const diff = now - time;
                
                const minutes = Math.floor(diff / 60000);
                const hours = Math.floor(diff / 3600000);
                const days = Math.floor(diff / 86400000);
                
                if (days > 0) return `${days}d ago`;
                if (hours > 0) return `${hours}h ago`;
                if (minutes > 0) return `${minutes}m ago`;
                return 'Just now';
            }

            async markAllAsRead() {
                if (this.unreadCount === 0) return;
                
                try {
                    // Mark all as read locally first for immediate UI update
                    this.notifications.forEach(n => n.read = true);
                    this.unreadCount = 0;
                    this.updateUI();
                    
                    // Then sync with server
                    await fetch('/api/notifications/mark-all-read', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    });
                } catch (error) {
                    console.error('Error marking notifications as read:', error);
                }
            }

            async clearAllNotifications() {
                try {
                    const response = await fetch('/api/notifications/clear-all', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    });
                    
                    if (response.ok) {
                        this.notifications = [];
                        this.unreadCount = 0;
                        this.updateUI();
                        this.showToast('All notifications cleared', 'success');
                    }
                } catch (error) {
                    console.error('Error clearing notifications:', error);
                    this.showToast('Failed to clear notifications', 'error');
                }
            }

            showToast(message, type = 'info') {
                const container = document.getElementById('notification-container');
                if (!container) return;

                const toast = document.createElement('div');
                toast.className = `alert alert-${type} alert-dismissible fade show`;
                toast.style.cssText = 'margin-bottom: 10px; animation: slideInRight 0.3s ease;';
                toast.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                container.appendChild(toast);

                // Auto-remove after 5 seconds
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.remove();
                    }
                }, 5000);
            }
        }

        // Initialize notification system when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            window.notificationSystem = new NotificationSystem();
        });

        // Add CSS animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
    {% endif %}

    <!--Start of Tawk.to Script-->
    <script type="text/javascript">
    var Tawk_API=Tawk_API||{}, Tawk_LoadStart=new Date();

    // Configure Tawk.to to handle CORS properly
    Tawk_API.onLoad = function(){
        console.log('Tawk.to loaded successfully');
    };

    Tawk_API.onStatusChange = function(status){
        console.log('Tawk.to status:', status);
    };

    // Load Tawk.to with error handling
    (function(){
        try {
            var s1=document.createElement("script"),s0=document.getElementsByTagName("script")[0];
            s1.async=true;
            s1.src='https://embed.tawk.to/686ceed760021f19101cc1b6/1ivko9v1b';
            s1.charset='UTF-8';
            s1.setAttribute('crossorigin','anonymous');

            // Add error handling
            s1.onerror = function() {
                console.warn('Tawk.to failed to load - chat widget may not be available');
            };

            s0.parentNode.insertBefore(s1,s0);
        } catch (e) {
            console.warn('Error loading Tawk.to:', e);
        }
    })();
    </script>
    <!--End of Tawk.to Script-->
</body>
</html>
