{% extends "base.html" %}

{% block title %}Configure Repping System - {{ server_name }}{% endblock %}

{% block styles %}
<style>
    .config-header {
        background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(251, 191, 36, 0.1) 100%);
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(20px);
        border: 1px solid var(--border-color);
    }

    .config-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #f59e0b, #fbbf24);
    }

    .config-title {
        font-size: 2rem;
        font-weight: 700;
        background: linear-gradient(135deg, var(--text-primary), #f59e0b);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .config-subtitle {
        color: var(--text-secondary);
        font-size: 1.1rem;
        margin: 0;
    }

    .form-section {
        background: rgba(255, 255, 255, 0.03);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        backdrop-filter: blur(20px);
        position: relative;
        overflow: hidden;
    }

    .form-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
        transform: translateX(-100%);
        transition: transform 0.6s ease;
    }

    .form-section:hover::before {
        transform: translateX(0);
    }

    .section-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-group:last-child {
        margin-bottom: 0;
    }

    .current-config {
        background: rgba(88, 101, 242, 0.1);
        border: 1px solid rgba(88, 101, 242, 0.2);
        border-radius: var(--border-radius);
        padding: 1.5rem;
        margin-top: 2rem;
    }

    .config-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-top: 1rem;
    }

    .config-item {
        text-align: center;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 10px;
        border: 1px solid var(--border-color);
    }

    .config-item-label {
        font-weight: 600;
        color: var(--text-secondary);
        font-size: 0.875rem;
        margin-bottom: 0.5rem;
    }

    .config-item-value {
        color: var(--text-primary);
        font-family: 'Monaco', 'Menlo', monospace;
        background: rgba(255, 255, 255, 0.1);
        padding: 0.5rem;
        border-radius: 6px;
        font-size: 0.875rem;
    }

    @media (max-width: 768px) {
        .config-header {
            padding: 1.5rem;
        }

        .config-title {
            font-size: 1.5rem;
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .form-section {
            padding: 1.5rem;
        }

        .config-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="row mt-3">
    <!-- Sidebar -->
    <div class="col-md-3">
        {% include 'sidebar.html' %}
    </div>

    <!-- Main Content -->
    <div class="col-md-9">
        <div class="config-header">
            <div class="d-flex justify-content-between align-items-start flex-wrap gap-3">
                <div>
                    <h1 class="config-title">
                        <i class="fas fa-star text-warning"></i>
                        <span>Repping System Configuration</span>
                    </h1>
                    <p class="config-subtitle">Configure automatic role assignment based on user status</p>
                </div>
                <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                </a>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <div class="form-section">
                    <h2 class="section-title">
                        <i class="fas fa-cog text-primary"></i>
                        Configure Automatic Role Assignment
                    </h2>

                    <form method="POST">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                        <div class="form-group">
                            <label for="trigger_word" class="form-label">
                                <i class="fas fa-hashtag me-2 text-warning"></i>Trigger Word
                            </label>
                            <input type="text" class="form-control" id="trigger_word" name="trigger_word"
                                   value="{{ config.trigger_word if config else '' }}"
                                   placeholder="e.g., /ryzuo" required>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                The word to look for in user custom statuses (case insensitive)
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="role_id" class="form-label">
                                <i class="fas fa-user-tag me-2 text-success"></i>Role
                            </label>
                            <select class="form-select" id="role_id" name="role_id" required>
                                <option value="">Select a role...</option>
                            </select>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                The Discord role to assign to users with the trigger word in their status
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="channel_id" class="form-label">
                                <i class="fas fa-bell me-2 text-info"></i>Notification Channel
                            </label>
                            <select class="form-select" id="channel_id" name="channel_id" required>
                                <option value="">Select a channel...</option>
                            </select>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                The Discord channel where role assignment notifications will be sent
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Save Configuration
                            </button>
                        </div>
                    </form>
                </div>

                {% if config and config.trigger_word %}
                <div class="current-config">
                    <h3 class="section-title">
                        <i class="fas fa-check-circle text-success"></i>
                        Current Configuration
                    </h3>

                    <div class="config-grid">
                        <div class="config-item">
                            <div class="config-item-label">Trigger Word</div>
                            <div class="config-item-value">{{ config.trigger_word }}</div>
                        </div>
                        <div class="config-item">
                            <div class="config-item-label">Role ID</div>
                            <div class="config-item-value">{{ config.role_id }}</div>
                        </div>
                        <div class="config-item">
                            <div class="config-item-label">Channel ID</div>
                            <div class="config-item-value">{{ config.channel_id }}</div>
                        </div>
                        {% if config.log_channel_id %}
                        <div class="config-item">
                            <div class="config-item-label">Log Channel ID</div>
                            <div class="config-item-value">{{ config.log_channel_id }}</div>
                        </div>
                        <div class="config-item">
                            <div class="config-item-label">Ignored Users</div>
                            <div class="config-item-value">{{ config.ignored_users|length if config.ignored_users else 0 }} users</div>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}
            </div>

            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>How to Get IDs</h6>
                    </div>
                    <div class="card-body">
                        <ol class="mb-0">
                            <li>Enable Developer Mode in Discord settings</li>
                            <li>Right-click on a role or channel</li>
                            <li>Select "Copy ID"</li>
                            <li>Paste the ID in the form</li>
                        </ol>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-lightbulb me-2"></i>How It Works</h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-2">The repping system automatically:</p>
                        <ul class="mb-0">
                            <li>Monitors user custom statuses</li>
                            <li>Assigns roles when trigger word is found</li>
                            <li>Removes roles when trigger word is removed</li>
                            <li>Sends notifications to the configured channel</li>
                        </ul>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-cogs me-2"></i>Additional Settings</h6>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">Use Discord commands for:</p>
                        <ul class="mb-0">
                            <li><code>/set-log-id</code> - Set detailed logging</li>
                            <li><code>/add-ignored-user</code> - Ignore specific users</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Global error handler
function showError(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show mt-3';
    alertDiv.role = 'alert';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    // Add to the top of the content
    const content = document.querySelector('.col-md-9');
    if (content) {
        content.insertBefore(alertDiv, content.firstChild);
    }
    
    console.error(message);
}

// Show loading state
function setLoading(element, isLoading) {
    const button = element.closest('form')?.querySelector('button[type="submit"]');
    if (button) {
        if (isLoading) {
            button.disabled = true;
            button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Loading...';
        } else {
            button.disabled = false;
            button.innerHTML = '<i class="fas fa-save me-2"></i>Save Configuration';
        }
    }
}

// Load Discord data for dropdowns
document.addEventListener('DOMContentLoaded', function() {
    loadRoles();
    loadChannels();
    
    // Handle form submission
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', handleFormSubmit);
    }
});

async function loadRoles() {
    const roleSelect = document.getElementById('role_id');
    if (!roleSelect) return;

    roleSelect.innerHTML = '<option value="">Loading roles...</option>';

    try {
        const response = await fetch("{{ url_for('api_roles') }}");
        const data = await response.json();

        // Handle fallback cases (bot not connected)
        if (!response.ok || data.fallback) {
            throw new Error(data.message || data.error || 'Failed to load roles');
        }

        if (data.error && !data.fallback) {
            throw new Error(data.error);
        }

        roleSelect.innerHTML = '<option value="">Select a role...</option>';

        data.forEach(role => {
            const option = document.createElement('option');
            option.value = role.id;
            option.textContent = role.name;
            option.style.color = role.color !== '#000000' ? role.color : '';

            // Select current role if editing
            {% if config and config.role_id %}
            if (role.id === '{{ config.role_id }}') {
                option.selected = true;
            }
            {% endif %}

            roleSelect.appendChild(option);
        });

        console.log(`Successfully loaded ${data.length} roles`);
    } catch (error) {
        console.error('Error loading roles:', error);

        // Show user-friendly error message
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-warning alert-dismissible fade show mt-2';
        alertDiv.innerHTML = `
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Bot Connection Issue:</strong> ${error.message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        roleSelect.parentNode.insertBefore(alertDiv, roleSelect.nextSibling);

        // Fallback to text input with current value
        const currentValue = '{{ config.role_id if config else "" }}';
        roleSelect.outerHTML = `
            <input type="text" class="form-control" id="role_id" name="role_id"
                   value="${currentValue}"
                   placeholder="Role ID (e.g., 123456789012345678)" required>
        `;

        // Add help text
        const helpDiv = document.createElement('div');
        helpDiv.className = 'form-text';
        helpDiv.innerHTML = '<i class="fas fa-info-circle me-1"></i>Enter the Discord role ID manually. Right-click a role → Copy ID (Developer Mode required)';
        document.getElementById('role_id').parentNode.appendChild(helpDiv);
    }
}

async function loadChannels() {
    const channelSelect = document.getElementById('channel_id');
    if (!channelSelect) return;

    channelSelect.innerHTML = '<option value="">Loading channels...</option>';

    try {
        const response = await fetch('{{ url_for("api_channels") }}', {
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            },
            credentials: 'same-origin'
        });

        const data = await response.json();

        // Handle fallback cases (bot not connected)
        if (!response.ok || data.fallback) {
            throw new Error(data.message || data.error || 'Failed to load channels');
        }

        if (data.error && !data.fallback) {
            throw new Error(data.error);
        }

        channelSelect.innerHTML = '<option value="">Select a channel...</option>';

        // Filter to text channels only
        const textChannels = Array.isArray(data) ? data.filter(channel => {
            return channel.type === 'text' || channel.type === 0 || channel.type === '0';
        }) : [];

        if (textChannels.length === 0) {
            channelSelect.innerHTML = '<option value="">No text channels found</option>';
            return;
        }

        // Group by category
        const categorized = {};
        textChannels.forEach(channel => {
            const category = channel.category || 'No Category';
            if (!categorized[category]) {
                categorized[category] = [];
            }
            categorized[category].push(channel);
        });

        // Add channels grouped by category
        Object.keys(categorized).sort().forEach(category => {
            const optgroup = document.createElement('optgroup');
            optgroup.label = category;

            categorized[category].sort((a, b) => a.name.localeCompare(b.name)).forEach(channel => {
                const option = document.createElement('option');
                option.value = channel.id;
                option.textContent = `#${channel.name}`;

                // Select current channel if editing
                const currentChannelId = '{{ config.channel_id if config else "" }}';
                if (currentChannelId && channel.id === currentChannelId) {
                    option.selected = true;
                }

                optgroup.appendChild(option);
            });

            channelSelect.appendChild(optgroup);
        });

        console.log(`Successfully loaded ${textChannels.length} text channels`);
    } catch (error) {
        console.error('Error loading channels:', error);

        // Show user-friendly error message
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-warning alert-dismissible fade show mt-2';
        alertDiv.innerHTML = `
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Bot Connection Issue:</strong> ${error.message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        channelSelect.parentNode.insertBefore(alertDiv, channelSelect.nextSibling);

        // Fallback to text input with current value
        const currentValue = '{{ config.channel_id if config else "" }}';
        channelSelect.outerHTML = `
            <input type="text" class="form-control" id="channel_id" name="channel_id"
                   value="${currentValue}"
                   placeholder="Channel ID (e.g., 123456789012345678)" required>
        `;

        // Add help text
        const helpDiv = document.createElement('div');
        helpDiv.className = 'form-text';
        helpDiv.innerHTML = '<i class="fas fa-info-circle me-1"></i>Enter the Discord channel ID manually. Right-click a channel → Copy ID (Developer Mode required)';
        document.getElementById('channel_id').parentNode.appendChild(helpDiv);
    }
}

async function handleFormSubmit(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    const submitButton = form.querySelector('button[type="submit"]');
    
    try {
        setLoading(submitButton, true);
        
        const response = await fetch(form.action || window.location.href, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        const result = await response.json().catch(() => ({}));
        
        if (!response.ok) {
            throw new Error(result.error || 'Failed to save configuration');
        }
        
        // Show success message
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-success alert-dismissible fade show';
        alertDiv.role = 'alert';
        alertDiv.innerHTML = `
            Configuration saved successfully!
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;
        
        form.insertBefore(alertDiv, form.firstChild);
        
        // Scroll to top to show the message
        window.scrollTo({ top: 0, behavior: 'smooth' });
        
    } catch (error) {
        console.error('Error saving configuration:', error);
        showError(error.message || 'An error occurred while saving the configuration.');
    } finally {
        setLoading(submitButton, false);
    }
}
</script>
{% endblock %}
