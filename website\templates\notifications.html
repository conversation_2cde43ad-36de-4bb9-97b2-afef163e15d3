{% extends "base.html" %}

{% block title %}Notifications{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="mb-0">Notifications</h1>
                <button class="btn btn-outline-danger" onclick="clearAllNotifications()">
                    <i class="fas fa-trash-alt me-2"></i>Clear All
                </button>
            </div>

            <div id="notificationsList" class="notification-list">
                <!-- Notifications will be loaded dynamically -->
                <div class="text-center py-4">
                    <i class="fas fa-spinner fa-spin fa-2x text-muted mb-2"></i>
                    <p class="text-muted mb-0">Loading notifications...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .notification-list {
        max-width: 800px;
        margin: 0 auto;
    }

    .notification-item {
        background: var(--bg-secondary);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius-sm);
        padding: 1.5rem;
        margin-bottom: 1rem;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .notification-item:hover {
        transform: translateY(-2px);
        box-shadow: var(--box-shadow);
    }

    .notification-item.unread {
        background: rgba(88, 101, 242, 0.1);
        border-left: 4px solid var(--primary-color);
    }

    .notification-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 0.5rem;
    }

    .notification-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
    }

    .notification-actions {
        display: flex;
        gap: 0.5rem;
    }

    .notification-message {
        color: var(--text-secondary);
        margin-bottom: 0.5rem;
        line-height: 1.5;
    }

    .notification-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid var(--border-color);
    }

    .notification-time {
        color: var(--text-muted);
        font-size: 0.875rem;
    }

    .empty-state {
        text-align: center;
        padding: 3rem;
        background: var(--bg-secondary);
        border-radius: var(--border-radius);
        border: 1px solid var(--border-color);
    }

    .empty-state i {
        font-size: 3rem;
        color: var(--text-muted);
        margin-bottom: 1rem;
    }

    .empty-state p {
        color: var(--text-muted);
        margin: 0;
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    loadNotifications();
    if (document.getElementById('notificationBadge')) {
        loadNotificationCount();
        setInterval(loadNotificationCount, 30000); // Refresh every 30 seconds
    }
});

async function loadNotificationCount() {
    try {
        const response = await fetch('/api/notifications/count', {
            headers: {
                'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
            }
        });
        if (!response.ok) throw new Error('Network response was not ok');
        
        const data = await response.json();
        const badge = document.getElementById('notificationBadge');
        
        if (data.count > 0) {
            badge.textContent = data.count;
            badge.style.display = 'flex';
            if (!badge.classList.contains('new')) {
                badge.classList.add('new');
            }
        } else {
            badge.style.display = 'none';
            badge.classList.remove('new');
        }
    } catch (error) {
        console.error('Error loading notification count:', error);
    }
}

async function loadNotifications() {
    try {
        const response = await fetch('/api/notifications', {
            headers: {
                'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
            }
        });
        if (!response.ok) throw new Error('Network response was not ok');
        
        const notifications = await response.json();
        const notificationsList = document.getElementById('notificationsList');
        
        if (notifications.length === 0) {
            notificationsList.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-bell-slash"></i>
                    <p>No notifications</p>
                </div>`;
            return;
        }
        
        notificationsList.innerHTML = notifications.map(notif => `
            <div class="notification-item ${!notif.read ? 'unread' : ''}" data-id="${notif._id}">
                <div class="notification-header">
                    <h3 class="notification-title">
                        ${notif.type === 'error' ? '<i class="fas fa-exclamation-circle text-danger me-2"></i>' : 
                          notif.type === 'warning' ? '<i class="fas fa-exclamation-triangle text-warning me-2"></i>' :
                          notif.type === 'success' ? '<i class="fas fa-check-circle text-success me-2"></i>' :
                          '<i class="fas fa-info-circle text-info me-2"></i>'}
                        ${escapeHtml(notif.title)}
                    </h3>
                    <div class="notification-actions">
                        ${!notif.read ? `
                            <button class="btn btn-sm btn-outline-primary" onclick="markNotificationRead('${notif._id}')">
                                <i class="fas fa-check me-1"></i>Mark as Read
                            </button>
                        ` : ''}
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteNotification('${notif._id}')">
                            <i class="fas fa-trash-alt"></i>
                        </button>
                    </div>
                </div>
                <p class="notification-message">${escapeHtml(notif.message)}</p>
                <div class="notification-footer">
                    <span class="notification-time">${formatDate(notif.created_at)}</span>
                </div>
            </div>
        `).join('');
    } catch (error) {
        console.error('Error loading notifications:', error);
    }
}

async function markNotificationRead(id) {
    try {
        const response = await fetch(`/api/notifications/read/${id}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
            }
        });
        
        if (!response.ok) throw new Error('Network response was not ok');
        loadNotifications();
    } catch (error) {
        console.error('Error marking notification as read:', error);
    }
}

async function deleteNotification(id) {
    try {
        const response = await fetch(`/api/notifications/delete/${id}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
            }
        });
        
        if (!response.ok) throw new Error('Network response was not ok');
        loadNotifications();
    } catch (error) {
        console.error('Error deleting notification:', error);
    }
}

async function clearAllNotifications() {
    if (!confirm('Are you sure you want to clear all notifications?')) return;
    
    try {
        const response = await fetch('/api/notifications/clear', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
            }
        });
        
        if (!response.ok) throw new Error('Network response was not ok');
        loadNotifications();
    } catch (error) {
        console.error('Error clearing notifications:', error);
    }
}

// Helper functions remain the same
function escapeHtml(unsafe) {
    return unsafe
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
}

function formatDate(dateStr) {
    const date = new Date(dateStr);
    const now = new Date();
    const diff = now - date;
    
    // If less than 24 hours ago, show relative time
    if (diff < 24 * 60 * 60 * 1000) {
        if (diff < 60 * 1000) return 'Just now';
        if (diff < 60 * 60 * 1000) return `${Math.floor(diff / (60 * 1000))}m ago`;
        return `${Math.floor(diff / (60 * 60 * 1000))}h ago`;
    }
    
    // Otherwise show date and time
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
}
</script>
{% endblock %}
