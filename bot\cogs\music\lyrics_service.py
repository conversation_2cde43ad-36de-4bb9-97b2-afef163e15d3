"""
Lyrics Service for Discord Music Bot
Integrates with Genius API to fetch song lyrics
"""

import os
import re
import logging
import aiohttp
import asyncio
from typing import Optional, Dict, Any
from urllib.parse import quote

logger = logging.getLogger(__name__)

class LyricsService:
    def __init__(self):
        self.client_id = os.getenv('GENIUS_CLIENT_ID')
        self.client_secret = os.getenv('GENIUS_CLIENT_SECRET')
        self.access_token = os.getenv('GENIUS_CLIENT_ACCESS_TOKEN')
        self.base_url = "https://api.genius.com"
        
        if not all([self.client_id, self.client_secret, self.access_token]):
            logger.warning("Genius API credentials not found. Lyrics functionality will be disabled.")
            self.enabled = False
        else:
            self.enabled = True
            logger.info("Genius API credentials loaded successfully")

    async def search_song(self, query: str) -> Optional[Dict[str, Any]]:
        """Search for a song on Genius"""
        if not self.enabled:
            return None
            
        try:
            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'User-Agent': 'Ryzuo Discord Bot'
            }
            
            search_url = f"{self.base_url}/search"
            params = {'q': query}
            
            async with aiohttp.ClientSession() as session:
                async with session.get(search_url, headers=headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        hits = data.get('response', {}).get('hits', [])
                        
                        if hits:
                            # Return the first hit
                            song = hits[0].get('result', {})
                            return {
                                'id': song.get('id'),
                                'title': song.get('title'),
                                'artist': song.get('primary_artist', {}).get('name'),
                                'url': song.get('url'),
                                'thumbnail': song.get('song_art_image_thumbnail_url')
                            }
                    else:
                        logger.error(f"Genius API search failed with status {response.status}")
                        
        except Exception as e:
            logger.error(f"Error searching for song on Genius: {e}")
            
        return None

    async def get_lyrics(self, song_title: str, artist_name: str = None) -> Optional[str]:
        """Get lyrics for a song"""
        if not self.enabled:
            return "❌ Lyrics service is not available (missing Genius API credentials)"
            
        try:
            # Create search query
            if artist_name:
                query = f"{song_title} {artist_name}"
            else:
                query = song_title
                
            # Clean up the query
            query = self._clean_search_query(query)
            
            # Search for the song
            song_info = await self.search_song(query)
            
            if not song_info:
                return f"❌ Could not find lyrics for: **{song_title}**"
                
            # Get lyrics from the song URL
            lyrics_text = await self._scrape_lyrics(song_info['url'])
            
            if lyrics_text:
                # Format the response
                title = song_info.get('title', song_title)
                artist = song_info.get('artist', artist_name or 'Unknown Artist')
                
                # Truncate lyrics if too long for Discord
                if len(lyrics_text) > 1800:  # Leave room for title and formatting
                    lyrics_text = lyrics_text[:1800] + "..."
                    
                return f"🎵 **{title}** by **{artist}**\n\n{lyrics_text}\n\n*Lyrics provided by Genius*"
            else:
                return f"❌ Could not retrieve lyrics for: **{song_title}**"
                
        except Exception as e:
            logger.error(f"Error getting lyrics: {e}")
            return f"❌ Error retrieving lyrics for: **{song_title}**"

    def _clean_search_query(self, query: str) -> str:
        """Clean up search query for better results"""
        # Remove common video suffixes
        query = re.sub(r'\s*\(.*?(official|video|audio|lyric|music|mv)\).*?$', '', query, flags=re.IGNORECASE)
        query = re.sub(r'\s*\[.*?(official|video|audio|lyric|music|mv)\].*?$', '', query, flags=re.IGNORECASE)
        
        # Remove "ft.", "feat.", "featuring"
        query = re.sub(r'\s*(ft\.?|feat\.?|featuring)\s+.*$', '', query, flags=re.IGNORECASE)
        
        # Remove extra whitespace
        query = ' '.join(query.split())
        
        return query.strip()

    async def _scrape_lyrics(self, genius_url: str) -> Optional[str]:
        """Scrape lyrics from Genius page"""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(genius_url, headers=headers) as response:
                    if response.status == 200:
                        html = await response.text()
                        
                        # Extract lyrics using regex (basic implementation)
                        # This is a simplified approach - in production you might want to use BeautifulSoup
                        lyrics_match = re.search(r'<div[^>]*data-lyrics-container[^>]*>(.*?)</div>', html, re.DOTALL)
                        
                        if lyrics_match:
                            lyrics_html = lyrics_match.group(1)
                            # Remove HTML tags and clean up
                            lyrics_text = re.sub(r'<[^>]+>', '\n', lyrics_html)
                            lyrics_text = re.sub(r'\n+', '\n', lyrics_text)
                            lyrics_text = lyrics_text.strip()
                            
                            # Decode HTML entities
                            lyrics_text = lyrics_text.replace('&amp;', '&')
                            lyrics_text = lyrics_text.replace('&lt;', '<')
                            lyrics_text = lyrics_text.replace('&gt;', '>')
                            lyrics_text = lyrics_text.replace('&quot;', '"')
                            lyrics_text = lyrics_text.replace('&#x27;', "'")
                            
                            return lyrics_text
                            
        except Exception as e:
            logger.error(f"Error scraping lyrics from {genius_url}: {e}")
            
        return None

# Global instance
lyrics_service = LyricsService()
