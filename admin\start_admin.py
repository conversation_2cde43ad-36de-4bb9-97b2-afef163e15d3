#!/usr/bin/env python3
"""
Admin Panel Startup Script
Starts the admin panel on a separate port with enhanced security
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('admin_panel.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def main():
    """Main function to start the admin panel"""
    try:
        # Import the admin app
        from admin_panel import admin_app
        
        # Get configuration
        admin_port = int(os.getenv('ADMIN_PORT', 5001))
        admin_host = os.getenv('ADMIN_HOST', '0.0.0.0')
        debug_mode = os.getenv('ADMIN_DEBUG', 'False').lower() == 'true'
        
        logger.info(f"Starting ryzuo Bot Admin Panel on {admin_host}:{admin_port}")
        logger.info(f"Debug mode: {debug_mode}")
        logger.info(f"Admin user ID: {os.getenv('ADMIN_USER_ID')}")
        
        # Start the admin panel
        admin_app.run(
            host=admin_host,
            port=admin_port,
            debug=debug_mode,
            threaded=True
        )
        
    except Exception as e:
        logger.error(f"Failed to start admin panel: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
