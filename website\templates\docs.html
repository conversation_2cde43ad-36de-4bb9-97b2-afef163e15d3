{% extends "base.html" %}

{% block title %}Documentation - <PERSON><PERSON><PERSON><PERSON>{% endblock %}

{% block styles %}
<style>
    .docs-container {
        max-width: 1000px;
        margin: 0 auto;
        padding: 40px 20px;
    }

    .docs-header {
        text-align: center;
        margin-bottom: 50px;
    }

    .docs-header h1 {
        font-size: 2.5rem;
        font-weight: 800;
        margin-bottom: 10px;
        background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .docs-header p {
        color: var(--text-secondary);
        font-size: 1.1rem;
    }

    .docs-nav {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid var(--border-color);
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 40px;
        backdrop-filter: blur(20px);
    }

    .docs-nav h2 {
        color: var(--text-primary);
        font-size: 1.3rem;
        margin-bottom: 20px;
        text-align: center;
    }

    .docs-nav-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
    }

    .docs-nav-item {
        background: rgba(88, 101, 242, 0.1);
        border: 1px solid rgba(88, 101, 242, 0.3);
        border-radius: 10px;
        padding: 15px;
        text-decoration: none;
        transition: all 0.3s ease;
        display: block;
    }

    .docs-nav-item:hover {
        background: rgba(88, 101, 242, 0.2);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(88, 101, 242, 0.3);
    }

    .docs-nav-item h3 {
        color: var(--primary-color);
        font-size: 1.1rem;
        margin-bottom: 8px;
    }

    .docs-nav-item p {
        color: var(--text-secondary);
        font-size: 0.9rem;
        margin: 0;
    }

    .docs-section {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid var(--border-color);
        border-radius: 20px;
        padding: 40px;
        margin-bottom: 30px;
        backdrop-filter: blur(20px);
    }

    .docs-section h2 {
        color: var(--text-primary);
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid var(--primary-color);
    }

    .docs-section h3 {
        color: var(--text-primary);
        font-size: 1.3rem;
        font-weight: 600;
        margin: 25px 0 15px 0;
    }

    .docs-section p {
        color: var(--text-secondary);
        line-height: 1.7;
        margin-bottom: 15px;
    }

    .docs-section ul, .docs-section ol {
        color: var(--text-secondary);
        margin-bottom: 15px;
        padding-left: 25px;
    }

    .docs-section li {
        margin-bottom: 8px;
        line-height: 1.6;
    }

    .docs-section strong {
        color: var(--text-primary);
    }

    .docs-section code {
        background: rgba(88, 101, 242, 0.2);
        color: var(--accent-color);
        padding: 2px 6px;
        border-radius: 4px;
        font-family: 'Courier New', monospace;
    }

    .command-example {
        background: rgba(0, 0, 0, 0.3);
        border: 1px solid var(--border-color);
        border-radius: 8px;
        padding: 15px;
        margin: 15px 0;
        font-family: 'Courier New', monospace;
        color: var(--accent-color);
    }

    .feature-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin: 20px 0;
    }

    .feature-card {
        background: rgba(88, 101, 242, 0.1);
        border: 1px solid rgba(88, 101, 242, 0.3);
        border-radius: 12px;
        padding: 20px;
    }

    .feature-card h4 {
        color: var(--primary-color);
        font-size: 1.1rem;
        margin-bottom: 10px;
    }

    .feature-card p {
        margin: 0;
        font-size: 0.95rem;
    }

    @media (max-width: 768px) {
        .docs-container {
            padding: 20px 15px;
        }

        .docs-header h1 {
            font-size: 2rem;
        }

        .docs-section {
            padding: 25px 20px;
        }

        .docs-nav {
            padding: 20px;
        }

        .docs-nav-grid {
            grid-template-columns: 1fr;
        }

        .feature-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="docs-container">
    <div class="docs-header">
        <h1>Documentation</h1>
        <p>Complete guide to using ryzuo Bot features and commands</p>
    </div>

    <div class="docs-nav">
        <h2>Quick Navigation</h2>
        <div class="docs-nav-grid">
            <a href="#getting-started" class="docs-nav-item">
                <h3>Getting Started</h3>
                <p>Initial setup and configuration</p>
            </a>
            <a href="#repping-system" class="docs-nav-item">
                <h3>Repping System</h3>
                <p>User engagement and status tracking</p>
            </a>
            <a href="#tempvoice" class="docs-nav-item">
                <h3>Temp Voice</h3>
                <p>Dynamic voice channel management</p>
            </a>
            <a href="#moderation" class="docs-nav-item">
                <h3>Dashboard Features</h3>
                <p>Sticky messages, auto-role, and more</p>
            </a>
            <a href="#premium-features" class="docs-nav-item">
                <h3>Premium Features</h3>
                <p>Advanced functionality with subscriptions</p>
            </a>
            <a href="#support" class="docs-nav-item">
                <h3>Support</h3>
                <p>Getting help and troubleshooting</p>
            </a>
        </div>
    </div>

    <div class="docs-section" id="getting-started">
        <h2>Getting Started</h2>

        <h3>1. Invite the Bot</h3>
        <p>First, invite ryzuo Bot to your Discord server using our <a href="https://discord.com/oauth2/authorize?client_id=1389466386396483714" target="_blank" style="color: var(--primary-color);">invite link</a>.</p>

        <h3>2. Purchase a Subscription</h3>
        <p>Visit our <a href="/shop" style="color: var(--primary-color);">shop page</a> to purchase a subscription for premium features. All payments are processed securely through Stripe.</p>

        <h3>3. Access the Dashboard</h3>
        <p>Log in to the dashboard using Discord OAuth and select your server to begin configuration. All bot features are configured through the web dashboard - no slash commands needed for setup!</p>

        <h3>4. Configure Basic Settings</h3>
        <p>Navigate through the dashboard sidebar to set up essential components:</p>
        <ul>
            <li><strong>Repping System:</strong> Configure the role, channel, and trigger word for user engagement tracking</li>
            <li><strong>Logs:</strong> Set up a log channel to receive all bot activity notifications</li>
            <li><strong>Auto-Role:</strong> Automatically assign roles to new members</li>
            <li><strong>Temp Voice:</strong> Set up temporary voice channel creation</li>
        </ul>
    </div>

    <div class="docs-section" id="repping-system">
        <h2>Repping System</h2>

        <p>The repping system tracks users who have a specific trigger word in their Discord status and automatically assigns them a role.</p>

        <h3>How It Works</h3>
        <ol>
            <li>Users set their Discord status to include the configured trigger word</li>
            <li>The bot automatically detects this and assigns the repping role</li>
            <li>When users remove the trigger word, the role is automatically removed</li>
            <li>All activity is logged to the configured log channel</li>
        </ol>

        <h3>Dashboard Configuration</h3>
        <p>Set up the repping system in the dashboard under "Repping System":</p>
        <ul>
            <li><strong>Repping Role:</strong> Select which role to assign to active reppers from the dropdown</li>
            <li><strong>Log Channel:</strong> Choose where repping activity should be logged</li>
            <li><strong>Trigger Word:</strong> Set the word users need in their status (e.g., "/ryzuo")</li>
            <li><strong>Ignored Users:</strong> Add users who should be excluded from the repping system</li>
        </ul>

        <p><strong>Note:</strong> A log channel must be configured before the repping system can be activated.</p>
    </div>

    <div class="docs-section" id="tempvoice">
        <h2>Temp Voice Channels</h2>

        <p>Create temporary voice channels that are automatically deleted when empty.</p>

        <h3>Dashboard Setup</h3>
        <p>Configure temp voice in the dashboard under "Temp Voice":</p>
        <ol>
            <li><strong>Interface Channel:</strong> Select a text channel where users can see temp voice controls</li>
            <li><strong>Creator Channel:</strong> Choose a voice channel that users join to create their own temp channel</li>
            <li><strong>Category:</strong> Select which category new temp channels should be created in</li>
            <li><strong>Channel Name Format:</strong> Customize how temp channels are named</li>
        </ol>

        <h3>How It Works</h3>
        <ul>
            <li>Users join the creator channel to automatically create their own temp voice channel</li>
            <li>The temp channel is created in the specified category</li>
            <li>Channel permissions are copied from the creator channel</li>
            <li>Channels are automatically deleted when they become empty</li>
        </ul>
    </div>

    <div class="docs-section" id="moderation">
        <h2>Dashboard Features</h2>

        <h3>Sticky Messages</h3>
        <p>Configure persistent messages that stay at the bottom of channels through the dashboard under "Sticky Messages":</p>
        <ul>
            <li>Select the channel for the sticky message</li>
            <li>Enter your message content</li>
            <li>Enable/disable the sticky message as needed</li>
        </ul>

        <h3>Auto-Role System</h3>
        <p>Set up automatic role assignment in the dashboard under "Auto-Role":</p>
        <ul>
            <li>Choose roles to assign to new members</li>
            <li>Configure whether to assign roles to bots</li>
            <li>Set up ignored users who won't receive auto-roles</li>
        </ul>

        <h3>Gender Verification</h3>
        <p>Configure gender verification system in the dashboard under "Gender Verification":</p>
        <ul>
            <li>Set up verification channels and roles</li>
            <li>Configure support ticket integration</li>
            <li>Manage verification requests through the dashboard</li>
        </ul>

        <h3>DM Support System</h3>
        <p>Enable direct message support tickets through the dashboard under "DM Support":</p>
        <ul>
            <li>Configure support channels and roles</li>
            <li>Set up transcript logging</li>
            <li>Manage support ticket categories</li>
        </ul>
    </div>

    <div class="docs-section" id="premium-features">
        <h2>Premium Features & Commands</h2>

        <p>Premium features require an active subscription and include:</p>

        <h3>Vent System</h3>
        <p>Configure anonymous venting in the dashboard under "Vent System":</p>
        <ul>
            <li>Set up vent channels and categories</li>
            <li>Configure anonymous posting settings</li>
            <li>Users can use the <code>/vent</code> command to post anonymously</li>
        </ul>
        <div class="command-example">/vent &lt;message&gt;</div>

        <h3>Giveaway System</h3>
        <p>Create and manage giveaways through the dashboard under "Giveaways":</p>
        <ul>
            <li>Web-based giveaway creation with full customization</li>
            <li>Automatic winner selection and announcement</li>
            <li>Reroll functionality and early ending options</li>
            <li>Comprehensive giveaway management interface</li>
        </ul>

        <h3>Advanced Analytics</h3>
        <p>View detailed server statistics in the dashboard under "Statistics":</p>
        <ul>
            <li>User activity tracking and repping statistics</li>
            <li>Voice channel usage analytics</li>
            <li>Message and engagement metrics</li>
            <li>Comprehensive activity charts and graphs</li>
        </ul>

        <h3>Site Logs</h3>
        <p>Monitor all bot activity through the dashboard under "Site Logs":</p>
        <ul>
            <li>Real-time activity monitoring</li>
            <li>Filterable log categories</li>
            <li>Detailed action tracking</li>
            <li>Export capabilities for record keeping</li>
        </ul>
    </div>

    <div class="docs-section" id="support">
        <h2>Support</h2>
        
        <h3>Getting Help</h3>
        <p>If you need assistance:</p>
        <ul>
            <li>Open a support ticket for personalized help</li>
            <li>Check this documentation for common questions</li>
        </ul>
        
        <h3>Common Issues</h3>
        <ul>
            <li><strong>Bot not responding:</strong> Ensure the bot has proper permissions in your server</li>
            <li><strong>Repping not working:</strong> Check that the trigger word is correctly configured and a log channel is set</li>
            <li><strong>Dashboard access issues:</strong> Verify your subscription is active and you have the proper server permissions</li>
            <li><strong>Features not working:</strong> Most features require a log channel to be configured first</li>
            <li><strong>Can't see dashboard options:</strong> Make sure you have administrator permissions in the Discord server</li>
        </ul>
        
        <h3>Contact Information</h3>
        <p>For additional support, reach out through our live chat on the bottom right or check our legal pages for policies:</p>
        <ul>
            <li><a href="/terms" style="color: var(--primary-color);">Terms of Service</a></li>
            <li><a href="/privacy" style="color: var(--primary-color);">Privacy Policy</a></li>
            <li><a href="/refund" style="color: var(--primary-color);">Refund Policy</a></li>
        </ul>
    </div>
</div>
{% endblock %}
