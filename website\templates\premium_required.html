{% extends "base.html" %}

{% block title %}Premium Feature - <PERSON><PERSON><PERSON><PERSON>{% endblock %}

{% block styles %}
<style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
    
    :root {
        --primary-color: #5865f2;
        --secondary-color: #7289da;
        --accent-color: #00d4ff;
        --bg-primary: #0a0a0f;
        --bg-secondary: #101218;
        --bg-tertiary: #1a1d29;
        --text-primary: #ffffff;
        --text-secondary: #b9bbbe;
        --border-color: #2f3349;
        --border-radius: 12px;
        --danger-color: #ed4245;
        --warning-color: #faa61a;
        --success-color: #3ba55c;
        --glow-primary: rgba(88, 101, 242, 0.5);
    }

    body {
        font-family: 'Inter', sans-serif;
        background: var(--bg-primary);
        color: var(--text-primary);
        margin: 0;
        padding: 0;
        min-height: 100vh;
        background-image: 
            radial-gradient(circle at 20% 80%, rgba(88, 101, 242, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%);
    }

    .premium-container {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        padding: 2rem;
    }

    .premium-card {
        background: var(--bg-secondary);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        padding: 3rem;
        max-width: 600px;
        width: 100%;
        text-align: center;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        position: relative;
        overflow: hidden;
    }

    .premium-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    }

    .premium-icon {
        font-size: 4rem;
        color: var(--primary-color);
        margin-bottom: 1.5rem;
        display: block;
    }

    .premium-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .premium-subtitle {
        font-size: 1.2rem;
        color: var(--text-secondary);
        margin-bottom: 2rem;
        line-height: 1.6;
    }

    .feature-name {
        color: var(--accent-color);
        font-weight: 600;
        text-transform: capitalize;
    }

    .premium-benefits {
        background: var(--bg-tertiary);
        border-radius: var(--border-radius);
        padding: 2rem;
        margin: 2rem 0;
        text-align: left;
    }

    .premium-benefits h3 {
        color: var(--text-primary);
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 1rem;
        text-align: center;
    }

    .benefits-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .benefits-list li {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.5rem 0;
        color: var(--text-secondary);
    }

    .benefits-list li i {
        color: var(--success-color);
        font-size: 1.1rem;
        width: 1.2rem;
        text-align: center;
    }

    .premium-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
        margin-top: 2rem;
    }

    .btn {
        padding: 1rem 2rem;
        border-radius: var(--border-radius);
        font-weight: 600;
        font-size: 1rem;
        text-decoration: none;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-primary {
        background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
        color: white;
        box-shadow: 0 4px 15px rgba(88, 101, 242, 0.3);
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(88, 101, 242, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-secondary {
        background: var(--bg-tertiary);
        color: var(--text-primary);
        border: 1px solid var(--border-color);
    }

    .btn-secondary:hover {
        background: var(--border-color);
        color: var(--text-primary);
        text-decoration: none;
        transform: translateY(-2px);
    }

    @media (max-width: 768px) {
        .premium-container {
            padding: 1rem;
        }

        .premium-card {
            padding: 2rem;
        }

        .premium-title {
            font-size: 2rem;
        }

        .premium-actions {
            flex-direction: column;
        }

        .btn {
            width: 100%;
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="premium-container">
    <div class="premium-card">
        <i class="fas fa-crown premium-icon"></i>
        
        <h1 class="premium-title">Premium Feature</h1>
        
        <p class="premium-subtitle">
            <span class="feature-name">{{ feature_name or 'This feature' }}</span> is part of our premium subscription. 
            Upgrade to Ryzuo Premium to unlock this feature and many more!
        </p>

        <div class="premium-benefits">
            <h3>What you get with Ryzuo Premium:</h3>
            <ul class="benefits-list">
                <li><i class="fas fa-check"></i> Advanced Repping System</li>
                <li><i class="fas fa-check"></i> Auto-Role Management</li>
                <li><i class="fas fa-check"></i> Temporary Voice Channels</li>
                <li><i class="fas fa-check"></i> High-Quality Music Bot</li>
                <li><i class="fas fa-check"></i> Sticky Messages</li>
                <li><i class="fas fa-check"></i> Giveaway System</li>
                <li><i class="fas fa-check"></i> Priority Support</li>
                <li><i class="fas fa-check"></i> Unlimited* servers</li>
            </ul>
        </div>

        <div class="premium-actions">
            <a href="{{ url_for('shop') }}" class="btn btn-primary">
                <i class="fas fa-shopping-cart"></i>
                Upgrade to Premium
            </a>
            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i>
                Back to Dashboard
            </a>
        </div>
    </div>
</div>
{% endblock %}
