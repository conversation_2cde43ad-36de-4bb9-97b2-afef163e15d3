"""
Shard Manager for ryzuo Bot
Handles shard status reporting and communication with the website
"""

import asyncio
import aiohttp
import logging
import os
import time
from datetime import datetime, timezone
from typing import Dict, Any, Optional
import discord

logger = logging.getLogger(__name__)

class ShardManager:
    def __init__(self, bot: discord.Client, shard_id: int = 0, total_shards: int = 1):
        self.bot = bot
        self.shard_id = shard_id
        self.total_shards = total_shards
        self.start_time = time.time()
        self.last_heartbeat = None
        
        # Configuration
        self.website_url = os.getenv('WEBSITE_URL')
        self.heartbeat_interval = int(os.getenv('SHARD_HEARTBEAT_INTERVAL'))  # seconds
        self.api_key = os.getenv('SHARD_API_KEY')  # For authentication
        
        # Status tracking
        self.status = "starting"
        self.last_latency = 0
        self.guild_count = 0
        self.user_count = 0
        
        # Start heartbeat task
        self.heartbeat_task = None
        
    async def start_heartbeat(self):
        """Start the heartbeat task"""
        if self.heartbeat_task is None or self.heartbeat_task.done():
            self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
            logger.info(f"Started heartbeat task for shard {self.shard_id}")
    
    async def stop_heartbeat(self):
        """Stop the heartbeat task"""
        if self.heartbeat_task and not self.heartbeat_task.done():
            self.heartbeat_task.cancel()
            try:
                await self.heartbeat_task
            except asyncio.CancelledError:
                pass
            logger.info(f"Stopped heartbeat task for shard {self.shard_id}")
    
    async def _heartbeat_loop(self):
        """Main heartbeat loop"""
        while True:
            try:
                await self.send_heartbeat()
                await asyncio.sleep(self.heartbeat_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in heartbeat loop for shard {self.shard_id}: {e}")
                await asyncio.sleep(self.heartbeat_interval)
    
    async def send_heartbeat(self):
        """Send heartbeat to the website"""
        try:
            # Update status information
            self.update_status()
            
            # Prepare heartbeat data
            heartbeat_data = {
                'shard_id': self.shard_id,
                'total_shards': self.total_shards,
                'status': self.status,
                'uptime': int(time.time() - self.start_time),
                'latency': self.last_latency,
                'guild_count': self.guild_count,
                'user_count': self.user_count,
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'api_key': self.api_key
            }
            
            # Send to website
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.website_url}/api/shard-heartbeat",
                    json=heartbeat_data,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status == 200:
                        self.last_heartbeat = datetime.now(timezone.utc)
                        logger.debug(f"Heartbeat sent successfully for shard {self.shard_id}")
                    else:
                        logger.warning(f"Heartbeat failed for shard {self.shard_id}: {response.status}")
                        
        except Exception as e:
            logger.error(f"Failed to send heartbeat for shard {self.shard_id}: {e}")
    
    def update_status(self):
        """Update shard status based on bot state"""
        if not self.bot.is_ready():
            self.status = "connecting"
        elif self.bot.is_closed():
            self.status = "offline"
        else:
            self.status = "operational"
            
        # Update latency
        if hasattr(self.bot, 'latency'):
            self.last_latency = round(self.bot.latency * 1000)  # Convert to ms
            
        # Update guild and user counts
        if self.bot.guilds:
            self.guild_count = len(self.bot.guilds)
            self.user_count = sum(guild.member_count or 0 for guild in self.bot.guilds)
    
    async def report_guild_join(self, guild: discord.Guild):
        """Report when bot joins a guild"""
        try:
            data = {
                'shard_id': self.shard_id,
                'event': 'guild_join',
                'guild_id': guild.id,
                'guild_name': guild.name,
                'member_count': guild.member_count,
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'api_key': self.api_key
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.website_url}/api/shard-event",
                    json=data,
                    timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    if response.status != 200:
                        logger.warning(f"Failed to report guild join: {response.status}")
                        
        except Exception as e:
            logger.error(f"Failed to report guild join: {e}")
    
    async def report_guild_leave(self, guild: discord.Guild):
        """Report when bot leaves a guild"""
        try:
            data = {
                'shard_id': self.shard_id,
                'event': 'guild_leave',
                'guild_id': guild.id,
                'guild_name': guild.name,
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'api_key': self.api_key
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.website_url}/api/shard-event",
                    json=data,
                    timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    if response.status != 200:
                        logger.warning(f"Failed to report guild leave: {response.status}")
                        
        except Exception as e:
            logger.error(f"Failed to report guild leave: {e}")
    
    def get_status_info(self) -> Dict[str, Any]:
        """Get current shard status information"""
        self.update_status()
        return {
            'shard_id': self.shard_id,
            'total_shards': self.total_shards,
            'status': self.status,
            'uptime': int(time.time() - self.start_time),
            'latency': self.last_latency,
            'guild_count': self.guild_count,
            'user_count': self.user_count,
            'last_heartbeat': self.last_heartbeat.isoformat() if self.last_heartbeat else None
        }
