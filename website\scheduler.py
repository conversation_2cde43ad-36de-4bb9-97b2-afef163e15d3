from apscheduler.schedulers.background import BackgroundScheduler
from datetime import datetime
import logging
from database import DatabaseManager
from dotenv import load_dotenv
import os
import time

# Load environment variables
load_dotenv()

# Initialize logging
logger = logging.getLogger(__name__)

# Initialize the database manager
mongo_url = os.getenv("MONGO_URL")

db = DatabaseManager(mongo_url)

# Function to check subscription status
def check_subscription_status():
    logger.info("Running subscription status check...")
    try:
        # Get all servers with active subscriptions
        server_ids = db.get_all_subscription_servers()
        for server_id in server_ids:
            user_id = db.get_user_id_by_server(server_id)  # Implement this method if not available
            subscription = db.get_user_subscription(user_id)

            if not subscription or subscription['status'] != 'active':
                # Revoke premium access
                db.revoke_premium_access(server_id)
                logger.info(f"Revoked premium access for server {server_id} due to inactive subscription.")
    except Exception as e:
        logger.error(f"Error during subscription status check: {e}")

# Initialize the scheduler
scheduler = BackgroundScheduler()

# Add job to run every hour
scheduler.add_job(check_subscription_status, 'interval', hours=1)

# Start the scheduler
scheduler.start()

logger.info("Subscription status scheduler started.")

# To ensure the scheduler keeps running
try:
    while True:
        time.sleep(2)
except (KeyboardInterrupt, SystemExit):
    scheduler.shutdown()
