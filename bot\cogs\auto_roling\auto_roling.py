"""
Auto-Roling System Cog for Discord Bot
Handles automatic role assignment to new members and offline protection
"""

import discord
from discord.ext import commands, tasks
import asyncio
import logging
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

class AutoRolingSystem(commands.Cog):
    """Cog for handling the auto-roling system functionality"""
    
    def __init__(self, bot):
        self.bot = bot
        self.db = bot.db
        
    async def cog_load(self):
        """Called when the cog is loaded"""
        logger.info("Loading Auto-Roling System cog...")
        
        # Start the auto-roling checking task
        if not self.check_auto_roling.is_running():
            self.check_auto_roling.start()
            
        logger.info("Auto-Roling System cog loaded successfully")
        
    async def cog_unload(self):
        """Called when the cog is unloaded"""
        logger.info("Unloading Auto-Roling System cog...")
        
        # Stop the auto-roling checking task
        if self.check_auto_roling.is_running():
            self.check_auto_roling.cancel()
            
        logger.info("Auto-Roling System cog unloaded")

    @commands.Cog.listener()
    async def on_member_join(self, member):
        """Handle member joins for auto-roling"""
        try:
            # Handle auto-roling (include bots but respect ignored users)
            guild_id = member.guild.id
            
            # Check if server has a license
            if not self.db.is_server_licensed(guild_id):
                return
                
            # Get ignored users list
            config = self.db.get_server_config(guild_id)
            ignored_users = config.get('ignored_users', []) if config else []

            if member.id not in ignored_users:
                # Get auto-roling settings
                auto_roling_settings = self.db.get_auto_roling_settings(guild_id)
                if auto_roling_settings and auto_roling_settings.get('enabled') and not auto_roling_settings.get('permission_error'):
                    role_id = auto_roling_settings.get('role_id')
                    if role_id:
                        # Get the role
                        role = member.guild.get_role(role_id)
                        if role:
                            # Assign the role
                            await self.assign_auto_role(member, role, guild_id)
                        else:
                            logger.warning(f"Auto-roling role {role_id} not found in guild {guild_id}")
            else:
                logger.debug(f"Skipping auto-roling for ignored user {member.display_name} ({member.id})")

        except Exception as e:
            logger.error(f"Error in auto-roling on_member_join handler: {e}")

    async def assign_auto_role(self, member, role, guild_id):
        """Assign auto-role to a member with permission checks"""
        try:
            # Check if user already has the role
            if role in member.roles:
                logger.debug(f"User {member.display_name} already has auto-role {role.name}")
                return

            # Check if we can actually assign the role
            bot_member = member.guild.me
            if not bot_member.guild_permissions.manage_roles:
                error_msg = "Missing 'Manage Roles' permission"
                logger.warning(f"Cannot assign auto-role in {member.guild.name} - {error_msg}")
                self.db.set_auto_roling_error(guild_id, error_msg)
                return

            if bot_member.top_role <= role and bot_member != member.guild.owner:
                error_msg = f"Auto-role {role.name} is above bot's highest role"
                logger.warning(f"Cannot assign auto-role {role.name} in {member.guild.name} - {error_msg}")
                self.db.set_auto_roling_error(guild_id, error_msg)
                return

            if member.top_role >= bot_member.top_role and member != member.guild.owner:
                logger.debug(f"Skipping auto-role for {member.display_name} - their highest role is above bot's")
                return

            # Add the role
            await member.add_roles(role, reason="Auto-roling system")

            logger.info(f"Assigned auto-role {role.name} to {member.display_name} in guild {member.guild.name}")

            # Log to database for dashboard
            try:
                self.db.log_bot_activity(
                    guild_id,
                    member.id,
                    f"{member.name}#{member.discriminator}",
                    f"Auto-role assigned: {role.name}",
                    f"New member automatically received role",
                    "auto_roling",
                    None
                )

                # Also log to Discord if enabled
                logging_cog = self.bot.get_cog('LoggingSystem')
                if logging_cog:
                    await logging_cog.log_bot_activity_to_channel(
                        guild_id, "ryzuo_logs", member.id, f"{member.name}#{member.discriminator}",
                        f"Auto-role assigned: {role.name}",
                        f"New member automatically received role",
                        None
                    )
            except Exception as e:
                logger.error(f"Failed to log auto-role assignment: {e}")

        except discord.HTTPException as e:
            if e.status == 429:  # Rate limited
                logger.warning(f"Rate limited during auto-role assignment, will retry later...")
            else:
                error_msg = f"HTTP error during role assignment: {e}"
                logger.error(error_msg)
                self.db.set_auto_roling_error(guild_id, error_msg)
        except discord.Forbidden:
            error_msg = "Forbidden: Bot lacks permission to assign roles"
            logger.error(f"Cannot assign auto-role - {error_msg}")
            self.db.set_auto_roling_error(guild_id, error_msg)
        except Exception as e:
            error_msg = f"Unexpected error during role assignment: {e}"
            logger.error(error_msg)
            self.db.set_auto_roling_error(guild_id, error_msg)

    @tasks.loop(seconds=10)  # Check every 10 seconds for auto-roling offline protection
    async def check_auto_roling(self):
        """Check for members missing auto-roles (offline protection)"""
        if not self.bot.is_ready():
            return

        try:
            for guild in self.bot.guilds:
                if not self.db.is_server_licensed(guild.id):
                    continue

                # Get auto-roling settings
                auto_roling_settings = self.db.get_auto_roling_settings(guild.id)
                if not auto_roling_settings or not auto_roling_settings.get('enabled'):
                    continue

                # Skip if there's a permission error
                if auto_roling_settings.get('permission_error'):
                    continue

                role_id = auto_roling_settings.get('role_id')
                if not role_id:
                    continue

                # Get the role
                role = guild.get_role(role_id)
                if not role:
                    logger.warning(f"Auto-roling role {role_id} not found in guild {guild.id}")
                    continue

                # Get ignored users list
                config = self.db.get_server_config(guild.id)
                ignored_users = config.get('ignored_users', []) if config else []

                # Check all members (including bots, but respect ignored list)
                members_to_assign = []
                for member in guild.members:
                    # Skip ignored users
                    if member.id in ignored_users:
                        continue

                    # Check if member should have the role but doesn't
                    if role not in member.roles:
                        members_to_assign.append(member)

                # Assign roles with rate limiting
                for i, member in enumerate(members_to_assign[:5]):  # Limit to 5 per check to avoid rate limits
                    try:
                        await self.assign_auto_role(member, role, guild.id)
                        # Small delay between assignments
                        if i < len(members_to_assign) - 1:
                            await asyncio.sleep(0.1)
                    except Exception as e:
                        logger.error(f"Error assigning auto-role to {member.display_name}: {e}")

        except Exception as e:
            logger.error(f"Error in check_auto_roling task: {e}")


async def setup(bot):
    """Setup function for the cog"""
    await bot.add_cog(AutoRolingSystem(bot))
