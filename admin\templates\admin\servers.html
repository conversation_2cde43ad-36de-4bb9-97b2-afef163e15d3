{% extends "admin/base.html" %}

{% block title %}Server Management{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-server me-2"></i>
        Server Management
    </h1>
    <div>
        <button class="btn btn-admin" onclick="refreshServerStats()">
            <i class="fas fa-sync me-2"></i>
            Refresh Stats
        </button>
    </div>
</div>

<!-- Server Statistics -->
<div class="row mb-4">
    <div class="col-md-3 col-sm-6">
        <div class="stat-card">
            <div class="stat-number">{{ stats.total_servers or 0 }}</div>
            <div class="stat-label">
                <i class="fas fa-server me-1"></i>
                Total Servers
            </div>
        </div>
    </div>
    <div class="col-md-3 col-sm-6">
        <div class="stat-card success">
            <div class="stat-number">{{ stats.premium_servers or 0 }}</div>
            <div class="stat-label">
                <i class="fas fa-crown me-1"></i>
                Premium Servers
            </div>
        </div>
    </div>
    <div class="col-md-3 col-sm-6">
        <div class="stat-card warning">
            <div class="stat-number">{{ stats.active_giveaways or 0 }}</div>
            <div class="stat-label">
                <i class="fas fa-gift me-1"></i>
                Active Giveaways
            </div>
        </div>
    </div>
    <div class="col-md-3 col-sm-6">
        <div class="stat-card danger">
            <div class="stat-number">{{ stats.total_members or 0 }}</div>
            <div class="stat-label">
                <i class="fas fa-users me-1"></i>
                Total Members
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="card admin-card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-6">
                <label for="search" class="form-label">Search Servers</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ search }}" placeholder="Search by server name or server ID">
            </div>
            <div class="col-md-3">
                <label for="filter" class="form-label">Filter</label>
                <select class="form-select" id="filter" name="filter">
                    <option value="all" {{ 'selected' if filter_type == 'all' }}>All Servers</option>
                    <option value="premium" {{ 'selected' if filter_type == 'premium' }}>Premium Servers</option>
                    <option value="large" {{ 'selected' if filter_type == 'large' }}>Large Servers (1000+ members)</option>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-admin me-2">
                    <i class="fas fa-search me-1"></i>Search
                </button>
                <a href="{{ url_for('admin_servers') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>Clear
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Servers Table -->
<div class="card admin-card">
    <div class="card-header admin-card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            Servers ({{ servers|length }} of {{ total_servers if total_servers else 0 }})
        </h5>
    </div>
    <div class="card-body p-0">
        {% if servers %}
        <div class="table-responsive">
            <table class="table table-admin mb-0">
                <thead>
                    <tr>
                        <th>Server</th>
                        <th>Server ID</th>
                        <th>Owner</th>
                        <th>License</th>
                        <th>Members</th>
                        <th>Added</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for server in servers %}
                    <tr id="server-{{ server.server_id }}">
                        <td>
                            <div class="d-flex align-items-center">
                                {% if server.server_icon %}
                                <img src="https://cdn.discordapp.com/icons/{{ server.server_id }}/{{ server.server_icon }}.png?size=64" 
                                     class="user-avatar me-2" alt="Server Icon">
                                {% else %}
                                <div class="user-avatar me-2 bg-secondary d-flex align-items-center justify-content-center">
                                    <i class="fas fa-server text-white"></i>
                                </div>
                                {% endif %}
                                <div>
                                    <strong>{{ server.server_name or 'Unknown' }}</strong>
                                    {% if server.get('premium_server') %}
                                    <span class="status-badge status-premium ms-2">
                                        <i class="fas fa-crown me-1"></i>Premium
                                    </span>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td>
                            <code>{{ server.server_id }}</code>
                        </td>
                        <td>
                            {% if server.owner_id %}
                            <code>{{ server.owner_id }}</code>
                            {% if server.owner_subscription %}
                            <span class="status-badge status-premium ms-1">
                                <i class="fas fa-crown me-1"></i>
                                {{ server.owner_subscription.subscription_tier.title() }}
                            </span>
                            {% endif %}
                            {% else %}
                            <span class="text-muted">Unknown</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if server.license %}
                            <span class="status-badge status-active">
                                <i class="fas fa-key me-1"></i>
                                Active
                            </span>
                            {% else %}
                            <span class="status-badge status-disabled">
                                <i class="fas fa-times me-1"></i>
                                None
                            </span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-info">{{ server.get('member_count', 'Unknown') }}</span>
                        </td>
                        <td>
                            <small class="text-muted">
                                {{ server.created_at.strftime('%Y-%m-%d') if server.created_at else 'Unknown' }}
                            </small>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <button class="btn btn-sm btn-outline-primary" 
                                        onclick="viewServerDetails('{{ server.server_id }}')">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-info" 
                                        onclick="viewServerLogs('{{ server.server_id }}')">
                                    <i class="fas fa-file-alt"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-warning" 
                                        onclick="resetServerConfig('{{ server.server_id }}')">
                                    <i class="fas fa-undo"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" 
                                        onclick="deleteServer('{{ server.server_id }}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if total_pages > 1 %}
        <div class="card-footer">
            <nav aria-label="Server pagination">
                <ul class="pagination justify-content-center mb-0">
                    {% if page > 1 %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page - 1 }}&search={{ search }}&filter={{ filter_type }}">Previous</a>
                    </li>
                    {% endif %}
                    
                    {% for p in range(1, total_pages + 1) %}
                    {% if p == page %}
                    <li class="page-item active">
                        <span class="page-link">{{ p }}</span>
                    </li>
                    {% elif p <= 3 or p >= total_pages - 2 or (p >= page - 2 and p <= page + 2) %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ p }}&search={{ search }}&filter={{ filter_type }}">{{ p }}</a>
                    </li>
                    {% elif p == 4 or p == total_pages - 3 %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                    {% endif %}
                    {% endfor %}
                    
                    {% if page < total_pages %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page + 1 }}&search={{ search }}&filter={{ filter_type }}">Next</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-server fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No servers found</h5>
            <p class="text-muted">Try adjusting your search criteria</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Server Details Modal -->
<div class="modal fade" id="serverDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-server me-2"></i>
                    Server Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="serverDetailsContent">
                <!-- Content loaded dynamically -->
            </div>
        </div>
    </div>
</div>

<!-- Server Logs Modal -->
<div class="modal fade" id="serverLogsModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-file-alt me-2"></i>
                    Server Logs
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="serverLogsContent">
                <!-- Content loaded dynamically -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function viewServerDetails(serverId) {
    document.getElementById('serverDetailsContent').innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</div>';
    new bootstrap.Modal(document.getElementById('serverDetailsModal')).show();
    
    fetch(`/api/admin/server/${serverId}/details`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const server = data.server;
            document.getElementById('serverDetailsContent').innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>Basic Information</h6>
                        <p><strong>Name:</strong> ${server.server_name || 'Unknown'}</p>
                        <p><strong>ID:</strong> <code>${server.server_id}</code></p>
                        <p><strong>Owner ID:</strong> <code>${server.owner_id || 'Unknown'}</code></p>
                        <p><strong>Member Count:</strong> ${server.member_count || 'Unknown'}</p>
                        <p><strong>Added:</strong> ${server.created_at || 'Unknown'}</p>
                    </div>
                    <div class="col-md-6">
                        <h6>Configuration</h6>
                        <p><strong>Premium:</strong> ${server.premium ? 'Yes' : 'No'}</p>
                        <p><strong>Logging Enabled:</strong> ${server.logging_enabled ? 'Yes' : 'No'}</p>
                        <p><strong>Music Enabled:</strong> ${server.music_enabled ? 'Yes' : 'No'}</p>
                        <p><strong>Giveaways:</strong> ${server.giveaway_count || 0} active</p>
                    </div>
                </div>
                <hr>
                <h6>Recent Activity</h6>
                <div id="serverActivity">Loading activity...</div>
            `;
            
            // Load recent activity
            loadServerActivity(serverId);
        } else {
            document.getElementById('serverDetailsContent').innerHTML = '<div class="alert alert-danger">Error loading server details</div>';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        document.getElementById('serverDetailsContent').innerHTML = '<div class="alert alert-danger">Error loading server details</div>';
    });
}

function loadServerActivity(serverId) {
    fetch(`/api/admin/server/${serverId}/activity`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const activityHtml = data.activities.map(activity => `
                <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                    <span>${activity.action}</span>
                    <small class="text-muted">${activity.timestamp}</small>
                </div>
            `).join('');
            document.getElementById('serverActivity').innerHTML = activityHtml || '<p class="text-muted">No recent activity</p>';
        }
    })
    .catch(error => {
        document.getElementById('serverActivity').innerHTML = '<p class="text-danger">Error loading activity</p>';
    });
}

function viewServerLogs(serverId) {
    document.getElementById('serverLogsContent').innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading logs...</div>';
    new bootstrap.Modal(document.getElementById('serverLogsModal')).show();
    
    fetch(`/api/admin/server/${serverId}/logs`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const logsHtml = data.logs.map(log => `
                <div class="log-entry border-bottom py-2">
                    <div class="d-flex justify-content-between">
                        <strong>${log.action}</strong>
                        <small class="text-muted">${log.timestamp}</small>
                    </div>
                    <div class="text-muted">${log.details || 'No details'}</div>
                </div>
            `).join('');
            document.getElementById('serverLogsContent').innerHTML = logsHtml || '<p class="text-muted">No logs found</p>';
        } else {
            document.getElementById('serverLogsContent').innerHTML = '<div class="alert alert-danger">Error loading logs</div>';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        document.getElementById('serverLogsContent').innerHTML = '<div class="alert alert-danger">Error loading logs</div>';
    });
}

function resetServerConfig(serverId) {
    if (!confirm('Are you sure you want to reset this server\'s configuration? This will restore all settings to default.')) {
        return;
    }
    
    fetch(`/api/admin/server/${serverId}/reset`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Server configuration reset successfully');
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while resetting server configuration');
    });
}

function deleteServer(serverId) {
    if (!confirm('Are you sure you want to PERMANENTLY DELETE this server and ALL its data? This action cannot be undone!')) {
        return;
    }
    
    if (!confirm('This will delete ALL server data including configurations, logs, giveaways, and more. Are you absolutely sure?')) {
        return;
    }
    
    fetch(`/api/admin/server/${serverId}/delete`, {
        method: 'DELETE',
        headers: {
            'X-CSRFToken': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById(`server-${serverId}`).remove();
            alert('Server deleted successfully');
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting server');
    });
}

function refreshServerStats() {
    location.reload();
}
</script>
{% endblock %}