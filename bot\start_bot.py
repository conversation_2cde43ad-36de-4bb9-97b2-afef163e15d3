#!/usr/bin/env python3
"""
ry<PERSON><PERSON> - Shard Startup Script
Starts a specific shard of the Discord bot
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    # Get shard configuration from command line or environment
    shard_id = int(sys.argv[1]) if len(sys.argv) > 1 else int(os.getenv('SHARD_ID', '0'))
    total_shards = int(sys.argv[2]) if len(sys.argv) > 2 else int(os.getenv('TOTAL_SHARDS', '1'))
    
    # Set environment variables for the bot
    os.environ['SHARD_ID'] = str(shard_id)
    os.environ['TOTAL_SHARDS'] = str(total_shards)
    
    logger.info(f"Starting ryzuo Bot - Shard {shard_id}/{total_shards}")
    
    # Import and run the bot
    try:
        from main import bot, BOT_TOKEN
        
        if BOT_TOKEN == "YOUR_BOT_TOKEN_HERE":
            logger.error("⚠️  Please configure your bot token!")
            logger.error("You need to set:")
            logger.error("- BOT_TOKEN: Your Discord bot token")
            logger.error("- MONGO_URL: Your MongoDB connection string")
            logger.error("- ADMIN_USER_ID: Discord user ID for admin commands")
            return
        
        # Run the bot
        bot.run(BOT_TOKEN)
        
    except KeyboardInterrupt:
        logger.info(f"Shard {shard_id} shutdown requested")
    except Exception as e:
        logger.error(f"Error starting shard {shard_id}: {e}")
        raise

if __name__ == "__main__":
    main()
