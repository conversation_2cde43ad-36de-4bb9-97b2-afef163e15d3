﻿{% extends "base.html" %}

{% block title %}Stats - {{ server_info.name }}{% endblock %}

{% block styles %}
{{ super() }}
<style>
/* Stats Page Modern Styling */
.stats-header {
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(88, 101, 242, 0.1) 100%);
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
}

.stats-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-color), var(--primary-color));
}

.stats-title {
    font-size: 2rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--text-primary), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.stats-subtitle {
    color: var(--text-secondary);
    font-size: 1.1rem;
    margin: 0;
}

/* Modern stat cards */
.stat-card {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    height: 140px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.stat-card:hover::before {
    transform: translateX(0);
}

.stat-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.05);
    box-shadow: var(--box-shadow-hover);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 900;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 1rem;
    font-weight: 500;
    margin: 0;
}

/* Chart containers */
.chart-card {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.chart-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.chart-card:hover::before {
    transform: translateX(0);
}

.chart-card:hover {
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 0.05);
    box-shadow: var(--box-shadow);
}

.chart-header {
    background: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.chart-body {
    padding: 1.5rem;
}

/* Progress bars */
.progress-thin {
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    transition: width 0.6s ease;
}

/* List items */
.list-group-item {
    background: rgba(255, 255, 255, 0.02) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--text-primary) !important;
    padding: 1rem;
    margin-bottom: 0.5rem;
    border-radius: 8px !important;
    transition: all 0.3s ease;
}

.list-group-item:hover {
    background: rgba(255, 255, 255, 0.05) !important;
    transform: translateX(4px);
}

.list-group-item:last-child {
    margin-bottom: 0;
}

/* User avatars */
.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: 2px solid var(--border-color);
    transition: all 0.3s ease;
}

.user-avatar:hover {
    border-color: var(--primary-color);
    box-shadow: 0 0 10px var(--glow-primary);
}

/* Activity card height fixes */
.card.h-100 {
    min-height: 400px;
}

.card-body.d-flex.flex-column {
    height: 100%;
}

.list-group.flex-grow-1 {
    flex: 1;
    overflow-y: auto;
}

/* Stats cards height control */
.row .col-md-6.col-xl-3 .card {
    height: auto !important;
    min-height: 120px;
    max-height: 150px;
}

.row .col-md-6.col-xl-3 .card .card-body {
    padding: 1rem;
}

/* Chart header improvements */
.chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* Icon shapes */
.icon-shape {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 3rem;
    height: 3rem;
    font-size: 1.25rem;
}

/* Activity list */
.activity-list {
    max-height: 400px;
    overflow-y: auto;
}

/* Progress bar width */
.progress-bar-custom {
    width: var(--progress-width, 0%);
}

/* Chart container height fix */
#activityChart {
    max-height: 300px !important;
    height: 300px !important;
}
</style>
{% endblock %}

{% block content %}
<div class="row mt-3">
    <!-- Sidebar -->
    <div class="col-md-3">
        {% include 'sidebar.html' %}
    </div>

    <!-- Main Content -->
    <div class="col-md-9">
        <div class="stats-header">
            <div class="d-flex justify-content-between align-items-start flex-wrap gap-3">
                <div>
                    <h1 class="stats-title">
                        <i class="fas fa-chart-line" style="color: var(--accent-color);"></i>
                        <span>Server Statistics</span>
                    </h1>
                    <p class="stats-subtitle">Analytics and insights for your server</p>
                </div>
                <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                </a>
            </div>
        </div>
        <!-- Stats Cards -->
        <div class="row g-4 mb-4">
            <!-- Total Members -->
            <div class="col-md-6 col-xl-3">
                <div class="stat-card">
                    <div class="stat-number">{{ server_stats.get('member_count', 0)|number_format }}</div>
                    <div class="stat-label">
                        <i class="fas fa-users me-2 text-primary"></i>Total Members
                    </div>
                </div>
            </div>

            <!-- Online Members -->
            <div class="col-md-6 col-xl-3">
                <div class="stat-card">
                    <div class="stat-number">{{ server_stats.get('online_count', 0)|number_format }}</div>
                    <div class="stat-label">
                        <i class="fas fa-signal me-2 text-success"></i>Online Now
                    </div>
                    {% set progress_width = (server_stats.get('online_count', 0) / server_stats.get('member_count', 1) * 100 if server_stats.get('member_count', 0) > 0 else 0)|round|int %}
                    <div class="progress progress-thin mt-2">
                        <div class="progress-bar"
                             role="progressbar"
                             style="width: {{ progress_width }}%"
                             aria-valuenow="{{ progress_width }}"
                             aria-valuemin="0"
                             aria-valuemax="100">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Total Channels -->
            <div class="col-md-6 col-xl-3">
                <div class="stat-card">
                    <div class="stat-number">{{ server_stats.get('channel_count', 0)|number_format }}</div>
                    <div class="stat-label">
                        <i class="fas fa-hashtag me-2 text-info"></i>Total Channels
                    </div>
                </div>
            </div>

            <!-- Server Boost -->
            <div class="col-md-6 col-xl-3">
                <div class="stat-card">
                    <div class="stat-number">{{ server_stats.get('premium_subscription_count', 0) }}</div>
                    <div class="stat-label">
                        <i class="fas fa-rocket me-2 text-warning"></i>Server Boosts
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="row g-4">
            <!-- Server Activity Chart -->
            <div class="col-lg-8">
                <div class="chart-card">
                    <div class="chart-header">
                        <i class="fas fa-chart-area text-primary"></i>
                        Server Activity Overview
                        <div class="ms-auto">
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="timeRangeDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                    Last 30 Days
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="timeRangeDropdown">
                                    <li><a class="dropdown-item" href="#" data-period="7">Last 7 Days</a></li>
                                    <li><a class="dropdown-item active" href="#" data-period="30">Last 30 Days</a></li>
                                    <li><a class="dropdown-item" href="#" data-period="90">Last 3 Months</a></li>
                                    <li><a class="dropdown-item" href="#" data-period="0">All Time</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="chart-body">
                        <canvas id="activityChart" style="height: 300px;"></canvas>
                    </div>
                </div>
            </div>

            <!-- Top Repping Users -->
            <div class="col-lg-4">
                <div class="chart-card">
                    <div class="chart-header">
                        <i class="fas fa-crown text-warning"></i>
                        Top Repping Users
                    </div>
                    <div class="chart-body">
                        <div class="list-group list-group-flush">
                            {% if top_repping_users %}
                                {% for user in top_repping_users[:6] %}
                                <div class="list-group-item d-flex align-items-center">
                                    {% if user.avatar_url %}
                                    <img src="{{ user.avatar_url }}" alt="{{ user.username }}" class="user-avatar me-3">
                                    {% else %}
                                    <div class="user-avatar me-3 d-flex align-items-center justify-content-center" style="background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));">
                                        <i class="fas fa-user text-white"></i>
                                    </div>
                                    {% endif %}
                                    <div class="flex-grow-1">
                                        <div class="fw-semibold">{{ user.username }}</div>
                                        <small class="text-muted">{{ user.total_hours|round(1) }} hours total</small>
                                    </div>
                                    <span class="badge bg-primary">{{ loop.index }}</span>
                                </div>
                                {% endfor %}
                            {% else %}
                            <div class="text-center py-4">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No repping activity yet</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>


                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
{{ super() }}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Activity Chart with real data
const activityCtx = document.getElementById('activityChart').getContext('2d');
let activityChart;

// Initialize chart with server data
function initActivityChart(logStats) {
    // Debug: log the data structure
    console.log('Chart data:', logStats);
    console.log('Daily activity:', logStats.daily_activity);
    console.log('Daily by category:', logStats.daily_by_category);

    // Prepare datasets for specific activity types only
    const dates = logStats.daily_activity ? Object.keys(logStats.daily_activity).sort() : [];
    const dailyByCategory = logStats.daily_by_category || {};

    // Define colors for specific metrics (only the ones we want)
    const metricColors = {
        'repping': { border: 'rgba(255, 193, 7, 1)', bg: 'rgba(255, 193, 7, 0.1)' },
        'tempvoice': { border: 'rgba(32, 201, 151, 1)', bg: 'rgba(32, 201, 151, 0.1)' },
        'message_sent': { border: 'rgba(13, 110, 253, 1)', bg: 'rgba(13, 110, 253, 0.1)' },
        'message_deleted': { border: 'rgba(220, 53, 69, 1)', bg: 'rgba(220, 53, 69, 0.1)' }
    };

    // Define metric labels
    const metricLabels = {
        'repping': 'Users Repping',
        'tempvoice': 'Voice Channels Created',
        'message_sent': 'Messages Sent',
        'message_deleted': 'Messages Deleted'
    };

    // Only include the specific metrics we want
    const allowedMetrics = ['repping', 'tempvoice', 'message_sent'];
    const datasets = [];

    // Create dataset for each allowed metric
    allowedMetrics.forEach(metric => {
        const data = dates.map(date => {
            return dailyByCategory[date] && dailyByCategory[date][metric] ? dailyByCategory[date][metric] : 0;
        });

        // Only add dataset if there's actual data
        const hasData = data.some(value => value > 0);
        if (hasData) {
            const colors = metricColors[metric];
            datasets.push({
                label: metricLabels[metric],
                data: data,
                borderColor: colors.border,
                backgroundColor: colors.bg,
                tension: 0.3,
                fill: false
            });
        }
    });

    // If no metric data, fall back to overall activity
    if (datasets.length === 0) {
        datasets.push({
            label: 'Total Bot Activity',
            data: logStats.daily_activity ? Object.values(logStats.daily_activity) : [],
            borderColor: 'rgba(75, 192, 192, 1)',
            backgroundColor: 'rgba(75, 192, 192, 0.1)',
            tension: 0.3,
            fill: true
        });
    }

    const data = {
        labels: dates,
        datasets: datasets
    };

    activityChart = new Chart(activityCtx, {
        type: 'line',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: true,
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// Initialize chart with server data
const initialLogStats = {{ log_stats|tojson }};
initActivityChart(initialLogStats);

// Time period dropdown functionality
document.querySelectorAll('[data-period]').forEach(item => {
    item.addEventListener('click', function(e) {
        e.preventDefault();
        const period = this.getAttribute('data-period');
        const text = this.textContent;

        // Update dropdown button text
        document.getElementById('timeRangeDropdown').textContent = text;

        // Remove active class from all items
        document.querySelectorAll('.dropdown-item').forEach(i => i.classList.remove('active'));
        // Add active class to clicked item
        this.classList.add('active');

        // Fetch new data
        fetch(`/api/stats-data?period=${period}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Destroy existing chart and recreate with new data
                    activityChart.destroy();
                    initActivityChart(data.log_stats);
                }
            })
            .catch(error => console.error('Error fetching stats data:', error));
    });
});
</script>
{% endblock %}
