2025-07-12 07:14:27,593 - root - ERROR - Failed to import required modules: No module named 'database'
2025-07-12 07:14:27,593 - __main__ - ERROR - Failed to start admin panel: No module named 'database'
2025-07-12 07:14:44,828 - root - ERROR - Failed to import required modules: No module named 'shard_communication'
2025-07-12 07:14:44,829 - __main__ - ERROR - Failed to start admin panel: No module named 'shard_communication'
2025-07-12 07:15:03,330 - shard_communication - ERROR - Shard config file not found: C:\Users\<USER>\Downloads\Leakin\admin\shard_config.json
2025-07-12 07:15:03,330 - shard_communication - INFO - Loaded 0 active shards: []
2025-07-12 07:15:31,218 - shard_communication - INFO - Loaded shard configuration from C:\Users\<USER>\Downloads\Leakin\admin\shard_config.json
2025-07-12 07:15:31,219 - shard_communication - INFO - Loaded 1 active shards: [0]
2025-07-12 07:15:31,885 - database - INFO - Successfully connected to MongoDB
2025-07-12 07:15:32,357 - admin_database - INFO - Admin database connection established
2025-07-12 07:15:32,391 - __main__ - INFO - Starting ryzuo Bot Admin Panel on 0.0.0.0:5001
2025-07-12 07:15:32,391 - __main__ - INFO - Debug mode: False
2025-07-12 07:15:32,391 - __main__ - INFO - Admin user ID: 1378705093301375026
2025-07-12 07:15:32,410 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://*************:5001
2025-07-12 07:15:32,410 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-12 07:15:36,243 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 07:15:36] "[32mGET / HTTP/1.1[0m" 302 -
2025-07-12 07:15:36,632 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 07:15:36] "GET /dashboard HTTP/1.1" 200 -
2025-07-12 07:16:07,422 - admin_database - INFO - Admin action logged: global_notification by 1378705093301375026
2025-07-12 07:16:07,424 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 07:16:07] "POST /api/admin/send-notification HTTP/1.1" 200 -
2025-07-12 07:16:24,069 - admin_database - INFO - Admin action logged: admin_logout by 1378705093301375026
2025-07-12 07:16:24,070 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 07:16:24] "[32mGET /logout HTTP/1.1[0m" 302 -
2025-07-12 07:16:24,142 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 07:16:24] "GET /login HTTP/1.1" 200 -
2025-07-12 07:16:26,731 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 07:16:26] "[32mGET / HTTP/1.1[0m" 302 -
2025-07-12 07:16:26,789 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 07:16:26] "GET /login HTTP/1.1" 200 -
2025-07-12 07:16:27,506 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 07:16:27] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-12 07:16:34,169 - admin_database - INFO - Admin action logged: admin_login by 1378705093301375026
2025-07-12 07:16:34,173 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 07:16:34] "[32mGET /oauth/callback?code=aFDxvMStmn36Tq9AheS05IKflUFqbj HTTP/1.1[0m" 302 -
2025-07-12 07:16:34,934 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 07:16:34] "GET /dashboard HTTP/1.1" 200 -
2025-07-12 07:18:39,689 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 07:18:39] "GET /environment HTTP/1.1" 200 -
2025-07-12 07:18:53,097 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 07:18:53] "GET /processes HTTP/1.1" 200 -
2025-07-12 07:19:01,947 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 07:19:01] "GET /security HTTP/1.1" 200 -
2025-07-12 07:19:03,488 - admin_panel - ERROR - Error loading pricing config: 'dict object' has no attribute 'lifetime'
2025-07-12 07:19:03,490 - admin_panel - ERROR - Exception on /pricing [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Leakin\admin\admin_panel.py", line 762, in admin_pricing
    return render_template('admin/pricing.html', pricing_config=pricing_config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Downloads\Leakin\admin\templates\admin\pricing.html", line 1, in top-level template code
    {% extends "admin/base.html" %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Leakin\admin\templates\admin\base.html", line 325, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Leakin\admin\templates\admin\pricing.html", line 208, in block 'content'
    {{ 'checked' if pricing_config.tiers.lifetime.enabled != false else '' }}>
    
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'lifetime'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Leakin\admin\admin_panel.py", line 108, in decorated_function
    return f(*args, **kwargs)
  File "C:\Users\<USER>\Downloads\Leakin\admin\admin_panel.py", line 767, in admin_pricing
    return render_template('admin/pricing.html', pricing_config={})
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Downloads\Leakin\admin\templates\admin\pricing.html", line 1, in top-level template code
    {% extends "admin/base.html" %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Leakin\admin\templates\admin\base.html", line 325, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Leakin\admin\templates\admin\pricing.html", line 27, in block 'content'
    {% for tier, config in pricing_config.tiers.items() %}
    ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'tiers'
2025-07-12 07:19:03,501 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 07:19:03] "[35m[1mGET /pricing HTTP/1.1[0m" 500 -
2025-07-12 07:19:05,681 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 07:19:05] "GET /dashboard HTTP/1.1" 200 -
2025-07-12 07:19:08,106 - admin_panel - ERROR - Error loading admin logs: 'stats' is undefined
2025-07-12 07:19:08,107 - admin_panel - ERROR - Exception on /logs [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Leakin\admin\admin_panel.py", line 866, in admin_logs
    return render_template('admin/logs.html',
                         logs=logs,
    ...<2 lines>...
                         action_filter=action_filter,
                         unique_actions=unique_actions)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Downloads\Leakin\admin\templates\admin\logs.html", line 1, in top-level template code
    {% extends "admin/base.html" %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Leakin\admin\templates\admin\base.html", line 325, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Leakin\admin\templates\admin\logs.html", line 27, in block 'content'
    <div class="stat-number">{{ stats.total_logs or 0 }}</div>
    ^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
jinja2.exceptions.UndefinedError: 'stats' is undefined

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Leakin\admin\admin_panel.py", line 108, in decorated_function
    return f(*args, **kwargs)
  File "C:\Users\<USER>\Downloads\Leakin\admin\admin_panel.py", line 876, in admin_logs
    return render_template('admin/logs.html', logs=[], page=1, total_pages=1, unique_actions=[])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Downloads\Leakin\admin\templates\admin\logs.html", line 1, in top-level template code
    {% extends "admin/base.html" %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Leakin\admin\templates\admin\base.html", line 325, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Leakin\admin\templates\admin\logs.html", line 27, in block 'content'
    <div class="stat-number">{{ stats.total_logs or 0 }}</div>
    ^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
jinja2.exceptions.UndefinedError: 'stats' is undefined
2025-07-12 07:19:08,115 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 07:19:08] "[35m[1mGET /logs HTTP/1.1[0m" 500 -
2025-07-12 07:19:20,132 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 07:19:20] "[32mGET / HTTP/1.1[0m" 302 -
2025-07-12 07:19:20,201 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 07:19:20] "GET /login HTTP/1.1" 200 -
2025-07-12 07:19:28,171 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 07:19:28] "[32mGET /oauth/callback?code=YzqUeSrn5hIMr8Q8DLebsABpMPUiaQ HTTP/1.1[0m" 302 -
2025-07-12 07:19:28,234 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 07:19:28] "GET /login HTTP/1.1" 200 -
2025-07-12 07:19:38,214 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 07:19:38] "[32mGET /dashboard HTTP/1.1[0m" 302 -
2025-07-12 07:19:38,272 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 07:19:38] "GET /login HTTP/1.1" 200 -
2025-07-12 09:43:02,164 - shard_communication - INFO - Loaded shard configuration from C:\Users\<USER>\Downloads\Leakin\admin\shard_config.json
2025-07-12 09:43:02,165 - shard_communication - INFO - Loaded 1 active shards: [0]
2025-07-12 09:43:02,903 - database - INFO - Successfully connected to MongoDB
2025-07-12 09:43:03,349 - admin_database - INFO - Admin database connection established
2025-07-12 09:43:03,365 - __main__ - INFO - Starting ryzuo Bot Admin Panel on 0.0.0.0:5001
2025-07-12 09:43:03,365 - __main__ - INFO - Debug mode: False
2025-07-12 09:43:03,366 - __main__ - INFO - Admin user ID: 1378705093301375026
2025-07-12 09:43:03,404 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://*************:5001
2025-07-12 09:43:03,404 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-12 09:54:17,143 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 09:54:17] "[32mGET / HTTP/1.1[0m" 302 -
2025-07-12 09:54:17,568 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 09:54:17] "GET /dashboard HTTP/1.1" 200 -
2025-07-12 09:56:00,383 - admin_database - INFO - Admin action logged: global_notification by 1378705093301375026
2025-07-12 09:56:00,385 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 09:56:00] "POST /api/admin/send-notification HTTP/1.1" 200 -
2025-07-12 09:57:51,523 - admin_database - INFO - Admin action logged: global_notification by 1378705093301375026
2025-07-12 09:57:51,525 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 09:57:51] "POST /api/admin/send-notification HTTP/1.1" 200 -
2025-07-12 10:15:06,178 - shard_communication - INFO - Loaded shard configuration from C:\Users\<USER>\Downloads\Leakin\admin\shard_config.json
2025-07-12 10:15:06,178 - shard_communication - INFO - Loaded 1 active shards: [0]
2025-07-12 10:15:06,843 - database - INFO - Successfully connected to MongoDB
2025-07-12 10:15:07,299 - admin_database - INFO - Admin database connection established
2025-07-12 10:15:07,337 - __main__ - INFO - Starting ryzuo Bot Admin Panel on 0.0.0.0:5001
2025-07-12 10:15:07,337 - __main__ - INFO - Debug mode: False
2025-07-12 10:15:07,338 - __main__ - INFO - Admin user ID: 1378705093301375026
2025-07-12 10:15:07,360 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://*************:5001
2025-07-12 10:15:07,361 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-12 10:37:17,117 - shard_communication - INFO - Loaded shard configuration from C:\Users\<USER>\Downloads\Leakin\admin\shard_config.json
2025-07-12 10:37:17,117 - shard_communication - INFO - Loaded 1 active shards: [0]
2025-07-12 10:37:17,779 - database - INFO - Successfully connected to MongoDB
2025-07-12 10:37:18,246 - admin_database - INFO - Admin database connection established
2025-07-12 10:37:18,287 - __main__ - INFO - Starting ryzuo Bot Admin Panel on 0.0.0.0:5001
2025-07-12 10:37:18,288 - __main__ - INFO - Debug mode: False
2025-07-12 10:37:18,288 - __main__ - INFO - Admin user ID: 1378705093301375026
2025-07-12 10:37:18,308 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://*************:5001
2025-07-12 10:37:18,308 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-12 10:38:32,876 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 10:38:32] "GET /dashboard HTTP/1.1" 200 -
2025-07-12 10:39:10,847 - admin_database - INFO - Admin action logged: global_notification by 1378705093301375026
2025-07-12 10:39:10,849 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 10:39:10] "POST /api/admin/send-notification HTTP/1.1" 200 -
2025-07-12 10:54:53,789 - shard_communication - INFO - Loaded shard configuration from C:\Users\<USER>\Downloads\Leakin\admin\shard_config.json
2025-07-12 10:54:53,790 - shard_communication - INFO - Loaded 1 active shards: [0]
2025-07-12 10:54:54,488 - database - INFO - Successfully connected to MongoDB
2025-07-12 10:54:54,932 - admin_database - INFO - Admin database connection established
2025-07-12 10:54:54,965 - __main__ - INFO - Starting ryzuo Bot Admin Panel on 0.0.0.0:5001
2025-07-12 10:54:54,965 - __main__ - INFO - Debug mode: False
2025-07-12 10:54:54,966 - __main__ - INFO - Admin user ID: 1378705093301375026
2025-07-12 10:54:54,987 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://*************:5001
2025-07-12 10:54:54,987 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
