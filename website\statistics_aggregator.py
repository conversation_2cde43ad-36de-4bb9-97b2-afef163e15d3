"""
Statistics Aggregation Service
Runs background tasks to collect and aggregate statistics from all shards
"""

import asyncio
import logging
import threading
import time
from datetime import datetime, timezone
from typing import Dict, Any

from database import DatabaseManager
from shard_communication import shard_comm

logger = logging.getLogger(__name__)

class StatisticsAggregator:
    def __init__(self, db: DatabaseManager):
        self.db = db
        self.running = False
        self.aggregation_thread = None
        self.aggregation_interval = shard_comm.config.get('aggregation_interval', 600)  # 10 minutes default
        
    def start(self):
        """Start the statistics aggregation background task"""
        if self.running:
            logger.warning("Statistics aggregator is already running")
            return
        
        self.running = True
        self.aggregation_thread = threading.Thread(target=self._aggregation_loop, daemon=True)
        self.aggregation_thread.start()
        logger.info(f"Started statistics aggregation with {self.aggregation_interval}s interval")
    
    def stop(self):
        """Stop the statistics aggregation background task"""
        self.running = False
        if self.aggregation_thread and self.aggregation_thread.is_alive():
            self.aggregation_thread.join(timeout=5)
        logger.info("Stopped statistics aggregation")
    
    def _aggregation_loop(self):
        """Main aggregation loop that runs in background thread"""
        logger.info("Statistics aggregation loop started")
        
        # Run initial aggregation
        self._run_aggregation()
        
        # Continue running at intervals
        while self.running:
            try:
                time.sleep(self.aggregation_interval)
                if self.running:  # Check again after sleep
                    self._run_aggregation()
            except Exception as e:
                logger.error(f"Error in aggregation loop: {e}")
                time.sleep(60)  # Wait 1 minute before retrying
    
    def _run_aggregation(self):
        """Run a single aggregation cycle"""
        try:
            logger.info("Starting statistics aggregation cycle")
            start_time = time.time()
            
            # Get aggregated statistics from all shards
            aggregated_stats = shard_comm.run_async(shard_comm.aggregate_global_statistics())
            
            # Save to database
            if aggregated_stats:
                success = self.db.save_global_statistics(aggregated_stats)
                if success:
                    elapsed = time.time() - start_time
                    logger.info(f"Statistics aggregation completed in {elapsed:.2f}s - "
                              f"{aggregated_stats['online_shards']}/{aggregated_stats['total_shards']} shards online, "
                              f"{aggregated_stats['total_servers']} servers, "
                              f"{aggregated_stats['total_members']} members")
                else:
                    logger.error("Failed to save aggregated statistics to database")
            else:
                logger.warning("No statistics data received from shards")
                
        except Exception as e:
            logger.error(f"Error during statistics aggregation: {e}")
    
    def force_aggregation(self) -> Dict[str, Any]:
        """Force an immediate aggregation and return the results"""
        try:
            logger.info("Forcing immediate statistics aggregation")
            aggregated_stats = shard_comm.run_async(shard_comm.aggregate_global_statistics())
            
            if aggregated_stats:
                # Save to database
                self.db.save_global_statistics(aggregated_stats)
                logger.info("Forced aggregation completed successfully")
                return aggregated_stats
            else:
                logger.warning("Forced aggregation returned no data")
                return {}
                
        except Exception as e:
            logger.error(f"Error during forced aggregation: {e}")
            return {}
    
    def get_cached_statistics(self) -> Dict[str, Any]:
        """Get the latest cached statistics from database"""
        try:
            stats = self.db.get_latest_global_statistics()
            if stats:
                return stats
            else:
                logger.warning("No cached statistics found, forcing aggregation")
                return self.force_aggregation()
        except Exception as e:
            logger.error(f"Error getting cached statistics: {e}")
            return {}
    
    def get_statistics_with_fallback(self) -> Dict[str, Any]:
        """Get statistics with fallback to live aggregation if cache is stale"""
        try:
            # Get latest cached statistics
            cached_stats = self.db.get_latest_global_statistics()
            
            if cached_stats:
                # Check if cache is fresh (less than 15 minutes old)
                last_updated = cached_stats.get('last_updated')
                if isinstance(last_updated, str):
                    last_updated = datetime.fromisoformat(last_updated.replace('Z', '+00:00'))
                elif isinstance(last_updated, datetime):
                    if last_updated.tzinfo is None:
                        last_updated = last_updated.replace(tzinfo=timezone.utc)
                
                if last_updated:
                    age_minutes = (datetime.now(timezone.utc) - last_updated).total_seconds() / 60
                    if age_minutes < 15:  # Cache is fresh
                        return cached_stats
                    else:
                        logger.info(f"Cached statistics are {age_minutes:.1f} minutes old, forcing refresh")
            
            # Cache is stale or missing, force aggregation
            return self.force_aggregation()
            
        except Exception as e:
            logger.error(f"Error getting statistics with fallback: {e}")
            # Return basic fallback data
            return {
                'total_shards': len(shard_comm.get_active_shards()),
                'online_shards': 0,
                'total_servers': 0,
                'total_members': 0,
                'total_channels': 0,
                'average_latency': 0,
                'last_updated': datetime.now(timezone.utc).isoformat(),
                'error': 'Failed to get statistics'
            }

# Global instance - will be initialized in web_dashboard.py
stats_aggregator = None

def initialize_aggregator(db: DatabaseManager):
    """Initialize the global statistics aggregator"""
    global stats_aggregator
    if stats_aggregator is None:
        stats_aggregator = StatisticsAggregator(db)
        stats_aggregator.start()
        logger.info("Statistics aggregator initialized and started")
    return stats_aggregator

def get_aggregator() -> StatisticsAggregator:
    """Get the global statistics aggregator instance"""
    global stats_aggregator
    if stats_aggregator is None:
        raise RuntimeError("Statistics aggregator not initialized. Call initialize_aggregator() first.")
    return stats_aggregator
