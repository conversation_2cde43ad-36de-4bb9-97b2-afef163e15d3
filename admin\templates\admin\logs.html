{% extends "admin/base.html" %}

{% block title %}Admin Logs{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-file-alt me-2"></i>
        Admin Action Logs
    </h1>
    <div>
        <button class="btn btn-admin" onclick="exportLogs()">
            <i class="fas fa-download me-2"></i>
            Export Logs
        </button>
        <button class="btn btn-outline-secondary" onclick="refreshLogs()">
            <i class="fas fa-sync me-2"></i>
            Refresh
        </button>
    </div>
</div>

<!-- Statistics -->
<div class="row mb-4">
    <div class="col-md-3 col-sm-6">
        <div class="stat-card">
            <div class="stat-number">{{ stats.total_logs or 0 }}</div>
            <div class="stat-label">
                <i class="fas fa-list me-1"></i>
                Total Logs
            </div>
        </div>
    </div>
    <div class="col-md-3 col-sm-6">
        <div class="stat-card success">
            <div class="stat-number">{{ stats.today_logs or 0 }}</div>
            <div class="stat-label">
                <i class="fas fa-calendar-day me-1"></i>
                Today's Logs
            </div>
        </div>
    </div>
    <div class="col-md-3 col-sm-6">
        <div class="stat-card warning">
            <div class="stat-number">{{ stats.unique_admins or 0 }}</div>
            <div class="stat-label">
                <i class="fas fa-user-shield me-1"></i>
                Active Admins
            </div>
        </div>
    </div>
    <div class="col-md-3 col-sm-6">
        <div class="stat-card danger">
            <div class="stat-number">{{ stats.critical_actions or 0 }}</div>
            <div class="stat-label">
                <i class="fas fa-exclamation-triangle me-1"></i>
                Critical Actions
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card admin-card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="action" class="form-label">Action Type</label>
                <select class="form-select" id="action" name="action">
                    <option value="">All Actions</option>
                    {% for action in unique_actions %}
                    <option value="{{ action }}" {{ 'selected' if action_filter == action }}>{{ action }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label for="admin_id" class="form-label">Admin ID</label>
                <input type="text" class="form-control" id="admin_id" name="admin_id" 
                       value="{{ request.args.get('admin_id', '') }}" placeholder="Filter by admin ID">
            </div>
            <div class="col-md-2">
                <label for="date_from" class="form-label">From Date</label>
                <input type="date" class="form-control" id="date_from" name="date_from" 
                       value="{{ request.args.get('date_from', '') }}">
            </div>
            <div class="col-md-2">
                <label for="date_to" class="form-label">To Date</label>
                <input type="date" class="form-control" id="date_to" name="date_to" 
                       value="{{ request.args.get('date_to', '') }}">
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-admin me-2">
                    <i class="fas fa-search me-1"></i>Filter
                </button>
                <a href="{{ url_for('admin_logs') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>Clear
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Logs Table -->
<div class="card admin-card">
    <div class="card-header admin-card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            Admin Action History ({{ logs|length }} entries)
        </h5>
    </div>
    <div class="card-body p-0">
        {% if logs %}
        <div class="table-responsive">
            <table class="table table-admin mb-0">
                <thead>
                    <tr>
                        <th>Timestamp</th>
                        <th>Admin</th>
                        <th>Action</th>
                        <th>Details</th>
                        <th>Target</th>
                        <th>IP Address</th>
                    </tr>
                </thead>
                <tbody>
                    {% for log in logs %}
                    <tr class="{{ get_log_row_class(log.action) }}">
                        <td>
                            <small class="text-muted">
                                {{ log.timestamp.strftime('%Y-%m-%d %H:%M:%S UTC') if log.timestamp else 'Unknown' }}
                            </small>
                        </td>
                        <td>
                            <code>{{ log.admin_id }}</code>
                        </td>
                        <td>
                            <span class="badge {{ get_action_badge_class(log.action) }}">
                                <i class="{{ get_action_icon(log.action) }} me-1"></i>
                                {{ log.action }}
                            </span>
                        </td>
                        <td>
                            <div class="log-details">
                                {{ log.details[:100] }}{% if log.details and log.details|length > 100 %}...{% endif %}
                                {% if log.details and log.details|length > 100 %}
                                <button class="btn btn-sm btn-link p-0 ms-1" onclick="showFullDetails('{{ loop.index }}')">
                                    <i class="fas fa-expand-alt"></i>
                                </button>
                                <div id="full-details-{{ loop.index }}" class="d-none">{{ log.details }}</div>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            {% if log.target_user_id %}
                            <small class="text-muted">User: <code>{{ log.target_user_id }}</code></small>
                            {% elif log.target_server_id %}
                            <small class="text-muted">Server: <code>{{ log.target_server_id }}</code></small>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if log.ip_address %}
                            <code>{{ log.ip_address }}</code>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if total_pages > 1 %}
        <div class="card-footer">
            <nav aria-label="Logs pagination">
                <ul class="pagination justify-content-center mb-0">
                    {% if page > 1 %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page - 1 }}&action={{ action_filter }}&admin_id={{ request.args.get('admin_id', '') }}&date_from={{ request.args.get('date_from', '') }}&date_to={{ request.args.get('date_to', '') }}">Previous</a>
                    </li>
                    {% endif %}
                    
                    {% for p in range(1, total_pages + 1) %}
                    {% if p == page %}
                    <li class="page-item active">
                        <span class="page-link">{{ p }}</span>
                    </li>
                    {% elif p <= 3 or p >= total_pages - 2 or (p >= page - 2 and p <= page + 2) %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ p }}&action={{ action_filter }}&admin_id={{ request.args.get('admin_id', '') }}&date_from={{ request.args.get('date_from', '') }}&date_to={{ request.args.get('date_to', '') }}">{{ p }}</a>
                    </li>
                    {% elif p == 4 or p == total_pages - 3 %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                    {% endif %}
                    {% endfor %}
                    
                    {% if page < total_pages %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page + 1 }}&action={{ action_filter }}&admin_id={{ request.args.get('admin_id', '') }}&date_from={{ request.args.get('date_from', '') }}&date_to={{ request.args.get('date_to', '') }}">Next</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No logs found</h5>
            <p class="text-muted">Admin actions will appear here</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Full Details Modal -->
<div class="modal fade" id="detailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle me-2"></i>
                    Full Action Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <pre id="modalDetailsContent" class="bg-light p-3 rounded"></pre>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.log-details {
    max-width: 300px;
    word-wrap: break-word;
}

.table-admin tr.table-danger {
    background-color: rgba(220, 53, 69, 0.1);
}

.table-admin tr.table-warning {
    background-color: rgba(255, 193, 7, 0.1);
}

.table-admin tr.table-success {
    background-color: rgba(25, 135, 84, 0.1);
}

.badge.bg-danger {
    background-color: #dc3545 !important;
}

.badge.bg-warning {
    background-color: #ffc107 !important;
    color: #000 !important;
}

.badge.bg-success {
    background-color: #198754 !important;
}

.badge.bg-info {
    background-color: #0dcaf0 !important;
    color: #000 !important;
}

.badge.bg-secondary {
    background-color: #6c757d !important;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function showFullDetails(index) {
    const fullDetails = document.getElementById(`full-details-${index}`).textContent;
    document.getElementById('modalDetailsContent').textContent = fullDetails;
    new bootstrap.Modal(document.getElementById('detailsModal')).show();
}

function refreshLogs() {
    location.reload();
}

function exportLogs() {
    // Get current filter parameters
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'true');
    
    // Create download link
    const downloadUrl = `/api/admin/logs/export?${params.toString()}`;
    
    // Trigger download
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = `admin_logs_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Auto-refresh every 30 seconds if on first page with no filters
{% if page == 1 and not action_filter and not request.args.get('admin_id') %}
setInterval(function() {
    if (document.visibilityState === 'visible') {
        location.reload();
    }
}, 30000);
{% endif %}
</script>
{% endblock %}