"""
Test script to check if cogs can be loaded properly
"""

import asyncio
import sys
import os

# Add the bot directory to the path
sys.path.insert(0, os.path.dirname(__file__))

async def test_cog_imports():
    """Test if all cogs can be imported"""
    cogs_to_test = [
        'cogs.core.utilities',
        'cogs.logging.logging_system',
        'cogs.admin.admin_commands',
        'cogs.events.event_handlers',
        'cogs.tasks.background_tasks',
        'cogs.repping.repping',
        'cogs.auto_roling.auto_roling',
        'cogs.vent.vent',
        'cogs.temp_voice.temp_voice',
        'cogs.music.music',
        'cogs.sticky_messages.sticky_messages',
        'cogs.giveaways.giveaways',
        'cogs.dm_support.dm_support',
    ]
    
    for cog in cogs_to_test:
        try:
            # Try to import the module
            __import__(cog)
            print(f"✅ {cog} - Import successful")
        except Exception as e:
            print(f"❌ {cog} - Import failed: {e}")

if __name__ == "__main__":
    asyncio.run(test_cog_imports())
