{% extends "admin/base.html" %}

{% block title %}Environment Configuration{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-cog me-2"></i>
        Environment Configuration
    </h1>
    <div class="text-muted">
        <i class="fas fa-exclamation-triangle me-1"></i>
        Changes require process restart
    </div>
</div>

<div class="alert alert-warning alert-admin">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <strong>Warning:</strong> Modifying environment variables can affect system functionality. 
    Make sure you understand the impact before making changes.
</div>

<!-- Website Environment Variables -->
<div class="card admin-card mb-4">
    <div class="card-header admin-card-header">
        <h5 class="mb-0">
            <i class="fas fa-globe me-2"></i>
            Website Environment Variables
        </h5>
    </div>
    <div class="card-body">
        <form id="websiteEnvForm">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="discord_client_id" class="form-label">Discord Client ID</label>
                    <input type="text" class="form-control" id="discord_client_id" name="DISCORD_CLIENT_ID" 
                           value="{{ website_config.env_vars.get('DISCORD_CLIENT_ID', '') }}">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="discord_client_secret" class="form-label">Discord Client Secret</label>
                    <input type="password" class="form-control" id="discord_client_secret" name="DISCORD_CLIENT_SECRET" 
                           value="{{ website_config.env_vars.get('DISCORD_CLIENT_SECRET', '') }}">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="discord_redirect_uri" class="form-label">Discord Redirect URI</label>
                    <input type="url" class="form-control" id="discord_redirect_uri" name="DISCORD_REDIRECT_URI" 
                           value="{{ website_config.env_vars.get('DISCORD_REDIRECT_URI', '') }}">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="secret_key" class="form-label">Secret Key</label>
                    <input type="password" class="form-control" id="secret_key" name="SECRET_KEY" 
                           value="{{ website_config.env_vars.get('SECRET_KEY', '') }}">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="website_host" class="form-label">Website Host</label>
                    <input type="text" class="form-control" id="website_host" name="WEBSITE_HOST" 
                           value="{{ website_config.env_vars.get('WEBSITE_HOST', '0.0.0.0') }}">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="website_port" class="form-label">Website Port</label>
                    <input type="number" class="form-control" id="website_port" name="WEBSITE_PORT" 
                           value="{{ website_config.env_vars.get('WEBSITE_PORT', '5000') }}">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="stripe_secret_key" class="form-label">Stripe Secret Key</label>
                    <input type="password" class="form-control" id="stripe_secret_key" name="STRIPE_SECRET_KEY" 
                           value="{{ website_config.env_vars.get('STRIPE_SECRET_KEY', '') }}">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="stripe_publishable_key" class="form-label">Stripe Publishable Key</label>
                    <input type="text" class="form-control" id="stripe_publishable_key" name="STRIPE_PUBLISHABLE_KEY" 
                           value="{{ website_config.env_vars.get('STRIPE_PUBLISHABLE_KEY', '') }}">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="stripe_webhook_secret" class="form-label">Stripe Webhook Secret</label>
                    <input type="password" class="form-control" id="stripe_webhook_secret" name="STRIPE_WEBHOOK_SECRET" 
                           value="{{ website_config.env_vars.get('STRIPE_WEBHOOK_SECRET', '') }}">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="mongo_url" class="form-label">MongoDB URL</label>
                    <input type="password" class="form-control" id="mongo_url" name="MONGO_URL" 
                           value="{{ website_config.env_vars.get('MONGO_URL', '') }}">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="redis_url" class="form-label">Redis URL</label>
                    <input type="text" class="form-control" id="redis_url" name="REDIS_URL" 
                           value="{{ website_config.env_vars.get('REDIS_URL', '') }}">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="support_server_invite" class="form-label">Support Server Invite</label>
                    <input type="url" class="form-control" id="support_server_invite" name="SUPPORT_SERVER_INVITE" 
                           value="{{ website_config.env_vars.get('SUPPORT_SERVER_INVITE', '') }}">
                </div>
            </div>
            <div class="d-flex justify-content-end">
                <button type="button" class="btn btn-admin" onclick="saveWebsiteConfig()">
                    <i class="fas fa-save me-2"></i>
                    Save Website Config
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Bot Environment Variables -->
<div class="card admin-card">
    <div class="card-header admin-card-header">
        <h5 class="mb-0">
            <i class="fas fa-robot me-2"></i>
            Discord Bot Environment Variables
        </h5>
    </div>
    <div class="card-body">
        <form id="botEnvForm">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="bot_token" class="form-label">Bot Token</label>
                    <input type="password" class="form-control" id="bot_token" name="BOT_TOKEN" 
                           value="{{ bot_config.env_vars.get('BOT_TOKEN', '') }}">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="admin_user_id" class="form-label">Admin User ID</label>
                    <input type="text" class="form-control" id="admin_user_id" name="ADMIN_USER_ID" 
                           value="{{ bot_config.env_vars.get('ADMIN_USER_ID', '') }}">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="total_shards" class="form-label">Total Shards</label>
                    <input type="number" class="form-control" id="total_shards" name="TOTAL_SHARDS" 
                           value="{{ bot_config.env_vars.get('TOTAL_SHARDS', '1') }}">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="website_url" class="form-label">Website URL</label>
                    <input type="url" class="form-control" id="website_url" name="WEBSITE_URL" 
                           value="{{ bot_config.env_vars.get('WEBSITE_URL', '') }}">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="shard_heartbeat_interval" class="form-label">Shard Heartbeat Interval</label>
                    <input type="number" class="form-control" id="shard_heartbeat_interval" name="SHARD_HEARTBEAT_INTERVAL" 
                           value="{{ bot_config.env_vars.get('SHARD_HEARTBEAT_INTERVAL', '30') }}">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="shard_api_key" class="form-label">Shard API Key</label>
                    <input type="password" class="form-control" id="shard_api_key" name="SHARD_API_KEY" 
                           value="{{ bot_config.env_vars.get('SHARD_API_KEY', '') }}">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="genius_client_id" class="form-label">Genius Client ID</label>
                    <input type="text" class="form-control" id="genius_client_id" name="GENIUS_CLIENT_ID" 
                           value="{{ bot_config.env_vars.get('GENIUS_CLIENT_ID', '') }}">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="genius_client_secret" class="form-label">Genius Client Secret</label>
                    <input type="password" class="form-control" id="genius_client_secret" name="GENIUS_CLIENT_SECRET" 
                           value="{{ bot_config.env_vars.get('GENIUS_CLIENT_SECRET', '') }}">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="genius_access_token" class="form-label">Genius Access Token</label>
                    <input type="password" class="form-control" id="genius_access_token" name="GENIUS_CLIENT_ACCESS_TOKEN" 
                           value="{{ bot_config.env_vars.get('GENIUS_CLIENT_ACCESS_TOKEN', '') }}">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="youtube_api_key" class="form-label">YouTube API Key</label>
                    <input type="password" class="form-control" id="youtube_api_key" name="YOUTUBE_API_KEY" 
                           value="{{ bot_config.env_vars.get('YOUTUBE_API_KEY', '') }}">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="shard_api_host" class="form-label">Shard API Host</label>
                    <input type="text" class="form-control" id="shard_api_host" name="SHARD_API_HOST" 
                           value="{{ bot_config.env_vars.get('SHARD_API_HOST', '0.0.0.0') }}">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="shard_api_port" class="form-label">Shard API Port</label>
                    <input type="number" class="form-control" id="shard_api_port" name="SHARD_API_PORT" 
                           value="{{ bot_config.env_vars.get('SHARD_API_PORT', '8000') }}">
                </div>
            </div>
            <div class="d-flex justify-content-end">
                <button type="button" class="btn btn-admin" onclick="saveBotConfig()">
                    <i class="fas fa-save me-2"></i>
                    Save Bot Config
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function saveWebsiteConfig() {
    const form = document.getElementById('websiteEnvForm');
    const formData = new FormData(form);
    const envVars = {};
    
    for (let [key, value] of formData.entries()) {
        envVars[key] = value;
    }
    
    fetch('/api/admin/environment/website', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        },
        body: JSON.stringify({env_vars: envVars})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Website configuration saved successfully! Restart required for changes to take effect.');
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while saving configuration');
    });
}

function saveBotConfig() {
    const form = document.getElementById('botEnvForm');
    const formData = new FormData(form);
    const envVars = {};
    
    for (let [key, value] of formData.entries()) {
        envVars[key] = value;
    }
    
    fetch('/api/admin/environment/bot', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        },
        body: JSON.stringify({env_vars: envVars})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Bot configuration saved successfully! Restart required for changes to take effect.');
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while saving configuration');
    });
}

// Toggle password visibility
document.querySelectorAll('input[type="password"]').forEach(input => {
    const wrapper = document.createElement('div');
    wrapper.className = 'input-group';
    input.parentNode.insertBefore(wrapper, input);
    wrapper.appendChild(input);
    
    const button = document.createElement('button');
    button.className = 'btn btn-outline-secondary';
    button.type = 'button';
    button.innerHTML = '<i class="fas fa-eye"></i>';
    button.onclick = function() {
        if (input.type === 'password') {
            input.type = 'text';
            button.innerHTML = '<i class="fas fa-eye-slash"></i>';
        } else {
            input.type = 'password';
            button.innerHTML = '<i class="fas fa-eye"></i>';
        }
    };
    
    wrapper.appendChild(button);
});
</script>
{% endblock %}
