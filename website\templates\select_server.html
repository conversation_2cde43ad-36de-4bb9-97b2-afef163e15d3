﻿{% extends "base.html" %}

{% block title %}Select Server - <PERSON><PERSON><PERSON><PERSON>{% endblock %}

{% block styles %}
<style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

    :root {
        --primary-color: #5865f2;
        --secondary-color: #7289da;
        --accent-color: #00d4ff;
        --bg-primary: #0a0a0f;
        --bg-secondary: #101218;
        --bg-tertiary: #1a1d29;
        --text-primary: #ffffff;
        --text-secondary: #b9bbbe;
        --text-muted: #72767d;
        --border-color: rgba(255, 255, 255, 0.1);
        --glow-primary: rgba(88, 101, 242, 0.5);
        --glow-accent: rgba(0, 212, 255, 0.3);
    }

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        background: var(--bg-primary);
        color: var(--text-primary);
        overflow-x: hidden;
        line-height: 1.6;
        -webkit-text-size-adjust: 100%;
        -webkit-tap-highlight-color: transparent;
        touch-action: manipulation;
    }

    /* Animated Background */
    .animated-bg {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        background: linear-gradient(45deg, #0a0a0f 25%, transparent 25%),
                    linear-gradient(-45deg, #0a0a0f 25%, transparent 25%),
                    linear-gradient(45deg, transparent 75%, #0a0a0f 75%),
                    linear-gradient(-45deg, transparent 75%, #0a0a0f 75%);
        background-size: 60px 60px;
        background-position: 0 0, 0 30px, 30px -30px, -30px 0px;
        animation: backgroundMove 20s linear infinite;
        opacity: 0.03;
    }

    @keyframes backgroundMove {
        0% { background-position: 0 0, 0 30px, 30px -30px, -30px 0px; }
        100% { background-position: 60px 60px, 60px 90px, 90px 30px, 30px 60px; }
    }

    /* Floating Particles */
    .particles {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        overflow: hidden;
    }

    .particle {
        position: absolute;
        width: 4px;
        height: 4px;
        background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
        border-radius: 50%;
        animation: float 6s ease-in-out infinite;
        opacity: 0.6;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.6; }
        50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
    }

    /* Main Container */
    .select-server-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 120px 20px 40px;
        position: relative;
        z-index: 1;
    }

    .select-server-content {
        max-width: 1200px;
        width: 100%;
        margin: 0 auto;
        position: relative;
        z-index: 1;
    }

    /* Server Selection Card */
    .server-selection-card {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid var(--border-color);
        border-radius: 20px;
        backdrop-filter: blur(20px);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        margin-bottom: 40px;
        animation: slideInUp 0.8s ease-out;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        overflow: hidden;
    }

    .server-selection-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
        border-color: rgba(255, 255, 255, 0.15);
    }

    .card-header {
        padding: 25px 30px 20px;
        border-bottom: 1px solid var(--border-color);
        background: rgba(255, 255, 255, 0.02);
    }

    .card-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--text-primary);
        margin: 0;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .card-title::before {
        content: '🖥️';
        font-size: 1.2rem;
    }

    .card-content {
        padding: 30px;
    }

    /* Server Grid */
    .servers-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 25px;
        margin: 0;
        animation: slideInUp 1s ease-out 0.2s both;
    }

    /* Server Card */
    .server-card {
        background: rgba(255, 255, 255, 0.03);
        border: 1px solid var(--border-color);
        border-radius: 20px;
        padding: 30px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        backdrop-filter: blur(20px);
        position: relative;
        overflow: hidden;
        text-align: center;
    }

    .server-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
        transform: translateX(-100%);
        transition: transform 0.6s ease;
    }

    .server-card:hover::before {
        transform: translateX(0);
    }

    .server-card:hover {
        transform: translateY(-5px);
        background: rgba(255, 255, 255, 0.05);
        box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
    }

    /* Server Icon */
    .server-icon-container {
        margin-bottom: 20px;
        display: flex;
        justify-content: center;
    }

    .server-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        border: 2px solid var(--border-color);
        transition: all 0.3s ease;
        object-fit: cover;
    }

    .server-icon-fallback {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        font-weight: 700;
        color: white;
        border: 2px solid var(--border-color);
        transition: all 0.3s ease;
    }

    .server-card:hover .server-icon,
    .server-card:hover .server-icon-fallback {
        border-color: var(--primary-color);
        box-shadow: 0 8px 25px rgba(88, 101, 242, 0.4);
    }

    /* Server Info */
    .server-name {
        font-size: 1.3rem;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 8px;
        word-break: break-word;
    }

    .server-members {
        color: var(--text-secondary);
        font-size: 0.95rem;
        margin-bottom: 25px;
    }

    /* Manage Button */
    .manage-btn {
        width: 100%;
        padding: 14px 24px;
        border-radius: 12px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border: none;
        cursor: pointer;
        font-size: 1rem;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: var(--text-primary);
        box-shadow: 0 6px 20px rgba(88, 101, 242, 0.4);
        min-height: 44px;
        -webkit-tap-highlight-color: transparent;
        user-select: none;
    }

    .manage-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 30px rgba(88, 101, 242, 0.6);
        color: var(--text-primary);
    }

    .manage-btn:disabled {
        opacity: 0.7;
        cursor: not-allowed;
        transform: none;
    }

    /* Footer Section */
    .footer-section {
        text-align: center;
        padding-top: 40px;
        border-top: 1px solid var(--border-color);
        animation: slideInUp 1.2s ease-out 0.4s both;
    }

    .logout-btn {
        padding: 12px 24px;
        border-radius: 12px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 10px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        background: rgba(255, 255, 255, 0.05);
        color: var(--text-primary);
        border: 1px solid rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
    }

    .logout-btn:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(255, 255, 255, 0.1);
        color: var(--text-primary);
    }

    /* Loading Spinner */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(10, 10, 15, 0.8);
        backdrop-filter: blur(10px);
        display: none;
        align-items: center;
        justify-content: center;
        z-index: 9999;
    }

    .loading-spinner {
        width: 60px;
        height: 60px;
        border: 3px solid rgba(88, 101, 242, 0.3);
        border-top: 3px solid var(--primary-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Alert Messages */
    .alert {
        position: fixed;
        top: 20px;
        right: 20px;
        max-width: 400px;
        padding: 16px 20px;
        border-radius: 12px;
        backdrop-filter: blur(20px);
        border: 1px solid;
        z-index: 10000;
        animation: slideInRight 0.3s ease-out;
    }

    .alert-danger {
        background: rgba(220, 53, 69, 0.1);
        border-color: rgba(220, 53, 69, 0.3);
        color: #ff6b7a;
    }

    .alert-success {
        background: rgba(40, 167, 69, 0.1);
        border-color: rgba(40, 167, 69, 0.3);
        color: #51cf66;
    }

    .alert-close {
        background: none;
        border: none;
        color: inherit;
        font-size: 1.2rem;
        cursor: pointer;
        float: right;
        margin-left: 10px;
        opacity: 0.7;
        transition: opacity 0.3s ease;
    }

    .alert-close:hover {
        opacity: 1;
    }

    /* Animations */
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(100%);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .select-server-container {
            padding: 100px 15px 30px;
        }

        .card-header {
            padding: 20px 25px 15px;
        }

        .card-title {
            font-size: 1.3rem;
        }

        .card-content {
            padding: 25px 20px;
        }

        .servers-grid {
            grid-template-columns: 1fr;
            gap: 20px;
            margin-bottom: 40px;
        }

        .server-card {
            padding: 25px 20px;
        }

        .server-icon,
        .server-icon-fallback {
            width: 70px;
            height: 70px;
        }

        .server-icon-fallback {
            font-size: 1.8rem;
        }

        .server-name {
            font-size: 1.2rem;
        }

        .manage-btn {
            padding: 12px 20px;
            font-size: 0.95rem;
        }

        .alert {
            top: 10px;
            right: 10px;
            left: 10px;
            max-width: none;
        }
    }

    @media (max-width: 480px) {
        .select-server-container {
            padding: 80px 10px 20px;
        }

        .card-header {
            padding: 15px 20px 12px;
        }

        .card-title {
            font-size: 1.2rem;
        }

        .card-content {
            padding: 20px 15px;
        }

        .server-card {
            padding: 20px 15px;
        }

        .server-icon,
        .server-icon-fallback {
            width: 60px;
            height: 60px;
        }

        .server-icon-fallback {
            font-size: 1.5rem;
        }
    }

    /* Touch device optimizations */
    @media (hover: none) and (pointer: coarse) {
        .server-card:hover {
            transform: none;
        }

        .manage-btn:hover,
        .logout-btn:hover {
            transform: none;
        }

        .particles {
            display: none;
        }

        .animated-bg {
            animation: none;
        }

        .manage-btn:active,
        .logout-btn:active {
            transform: scale(0.98);
            opacity: 0.8;
        }

        .server-card:active {
            transform: scale(0.98);
        }
    }

    /* Reduce motion for users who prefer it */
    @media (prefers-reduced-motion: reduce) {
        *,
        *::before,
        *::after {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }

        .particles {
            display: none;
        }

        .animated-bg {
            animation: none;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Animated Background -->
<div class="animated-bg"></div>

<!-- Particles -->
<div class="particles" id="particles"></div>

<!-- Include Navbar -->
{% include 'navbar.html' %}

<!-- Main Content -->
<div class="select-server-container">
    <div class="select-server-content">
        <!-- Server Selection Card -->
        <div class="server-selection-card">
            <div class="card-header">
                <h2 class="card-title">Server Selection</h2>
            </div>

            <div class="card-content">
                {% if no_servers %}
                <!-- No Servers Message -->
                <div class="no-servers-message" style="text-align: center; padding: 40px 20px;">
                    <div style="font-size: 4rem; color: var(--text-muted); margin-bottom: 20px;">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h3 style="color: var(--text-primary); margin-bottom: 15px;">Bot Not Found</h3>
                    <p style="color: var(--text-secondary); margin-bottom: 30px; max-width: 500px; margin-left: auto; margin-right: auto;">
                        The ryzuo bot is not present in any of your servers. Please invite the bot to your server first to access the dashboard.
                    </p>
                    <a href="{{ invite_url }}" target="_blank" class="manage-btn" style="display: inline-flex; align-items: center; gap: 10px; text-decoration: none;">
                        <i class="fab fa-discord"></i>
                        Invite Bot to Server
                    </a>
                </div>
                {% else %}
                <!-- Servers Grid -->
                <div class="servers-grid">
                    {% for server in user_servers %}
                    <div class="server-card" id="server-{{ server.id }}">
                        <div class="server-icon-container">
                            {% if server.icon %}
                                <img src="{{ server.icon }}"
                                     alt="{{ server.name }}"
                                     class="server-icon"
                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                <div class="server-icon-fallback" style="display: none;">
                                    {{ server.name[0]|upper }}
                                </div>
                            {% else %}
                                <div class="server-icon-fallback">
                                    {{ server.name[0]|upper }}
                                </div>
                            {% endif %}
                        </div>
                        <h3 class="server-name">{{ server.name }}</h3>
                        <p class="server-members">{{ server.member_count|default(0) }} members</p>
                        <button class="manage-btn manage-server" data-server-id="{{ server.id }}">
                            <i class="fas fa-cog"></i>
                            Manage Server
                        </button>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Footer Section -->
        <div class="footer-section">
            <a href="{{ url_for('logout') }}" class="logout-btn">
                <i class="fas fa-sign-out-alt"></i>
                Logout
            </a>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div id="loadingSpinner" class="loading-overlay">
    <div class="loading-spinner"></div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
// Create floating particles (optimized for mobile)
function createParticles() {
    const particles = document.getElementById('particles');
    if (!particles) return;

    // Reduce particle count on mobile devices
    const isMobile = window.innerWidth <= 768;
    const particleCount = isMobile ? 15 : 30;

    // Don't create particles if user prefers reduced motion
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
        return;
    }

    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.top = Math.random() * 100 + '%';
        particle.style.animationDelay = Math.random() * 6 + 's';
        particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
        particles.appendChild(particle);
    }
}

// Function to show alert messages
function showAlert(message, type = 'danger') {
    // Remove existing alerts
    const existingAlerts = document.querySelectorAll('.alert');
    existingAlerts.forEach(alert => alert.remove());

    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="alert-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;

    // Add to the page
    document.body.appendChild(alertDiv);

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentElement) {
            alertDiv.style.animation = 'slideOutRight 0.3s ease-out forwards';
            setTimeout(() => alertDiv.remove(), 300);
        }
    }, 5000);
}

// Function to handle server selection
async function selectServer(serverId) {
    const button = document.querySelector(`.manage-server[data-server-id="${serverId}"]`);
    const loadingSpinner = document.getElementById('loadingSpinner');

    try {
        // Show loading state
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>Loading...';
        loadingSpinner.style.display = 'flex';

        // Get CSRF token from meta tag or form
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content || '{{ csrf_token() }}';

        // Make the API call to select the server
        const response = await fetch(`/select-server/${serverId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': csrfToken
            },
            credentials: 'same-origin'  // Include cookies for session
        });

        const data = await response.json();

        if (response.ok && data.success) {

            // Redirect to dashboard on success
            setTimeout(() => {
                window.location.href = data.redirect || '/dashboard';
            }, 1000);
        } else {
            // Show error message
            const errorMsg = data.error || 'Failed to select server. Please try again.';
            showAlert(errorMsg, 'danger');

            // If the error indicates session expired, redirect to login
            if (data.logout) {
                setTimeout(() => {
                    window.location.href = '{{ url_for("logout") }}';
                }, 1500);
                return;
            }
        }
    } catch (error) {
        console.error('Error selecting server:', error);
        showAlert('An error occurred while selecting the server. Please try again.', 'danger');
    } finally {
        // Reset button state
        const buttons = document.querySelectorAll('.manage-server');
        buttons.forEach(btn => {
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-cog"></i>Manage Server';
        });
        loadingSpinner.style.display = 'none';
    }
}

// Add touch interactions for mobile devices
function addTouchInteractions() {
    const cards = document.querySelectorAll('.server-card');

    cards.forEach(card => {
        card.addEventListener('touchstart', function() {
            this.style.transform = 'translateY(-2px)';
        }, { passive: true });

        card.addEventListener('touchend', function() {
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        }, { passive: true });
    });
}

// Initialize everything when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Mobile detection
    const isMobile = window.innerWidth <= 768;
    const isTouch = 'ontouchstart' in window;

    // Create particles (skip on low-end devices)
    if (!isMobile || !isTouch) {
        createParticles();
    }

    // Handle server selection
    document.querySelectorAll('.manage-server').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const serverId = this.getAttribute('data-server-id');
            selectServer(serverId);
        });
    });

    // Add touch interactions for mobile
    if (isTouch) {
        addTouchInteractions();
    }

    // Handle orientation changes
    window.addEventListener('orientationchange', function() {
        setTimeout(() => {
            // Recalculate layouts after orientation change
            const particles = document.getElementById('particles');
            if (particles) {
                particles.innerHTML = '';
                if (!isMobile || !isTouch) {
                    createParticles();
                }
            }
        }, 100);
    });

    // Add slideOutRight animation for alerts (only if not already added)
    if (!window.selectServerAnimationStyleAdded) {
        const selectServerAnimationStyle = document.createElement('style');
        selectServerAnimationStyle.textContent = `
            @keyframes slideOutRight {
                from {
                    opacity: 1;
                    transform: translateX(0);
                }
                to {
                    opacity: 0;
                    transform: translateX(100%);
                }
            }
        `;
        document.head.appendChild(selectServerAnimationStyle);
        window.selectServerAnimationStyleAdded = true;
    }
});
</script>
{% endblock %}
