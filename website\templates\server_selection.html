{% extends "base.html" %}

{% block styles %}
<style>
    .server-selection-header {
        background: linear-gradient(135deg, rgba(88, 101, 242, 0.1) 0%, rgba(0, 212, 255, 0.1) 100%);
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(20px);
        border: 1px solid var(--border-color);
    }

    .server-selection-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    }

    .selection-title {
        font-size: 2.5rem;
        font-weight: 700;
        background: linear-gradient(135deg, var(--text-primary), var(--primary-color));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        justify-content: center;
    }

    .selection-subtitle {
        color: var(--text-secondary);
        font-size: 1.1rem;
        font-weight: 400;
        margin-bottom: 0;
        text-align: center;
    }

    .server-selection-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        box-shadow: 0 8px 25px rgba(88, 101, 242, 0.3);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .server-selection-header {
            padding: 1.5rem;
        }

        .server-selection-header .d-flex {
            flex-direction: column;
            gap: 1rem;
        }

        .selection-title {
            font-size: 2rem;
        }

        .server-selection-icon {
            width: 50px;
            height: 50px;
            font-size: 1.25rem;
        }

        .selection-subtitle {
            font-size: 1rem;
        }
    }

    /* Server Card Styles */
    .server-card {
        transition: transform 0.2s, box-shadow 0.2s;
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 10px;
        overflow: hidden;
    }

    .server-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .server-card.server-disabled {
        opacity: 0.7;
        background-color: #f8f9fa;
    }

    .server-icon-container {
        position: relative;
        width: 100px;
        height: 100px;
        margin: 0 auto;
    }

    .server-icon-wrapper {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .server-icon {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .server-icon-default {
        font-size: 2rem;
        color: white;
    }

    .alert {
        border-radius: 10px;
    }
</style>
{% endblock %}

{% block content %}
<!-- Loading Spinner Overlay -->
<div id="loadingSpinner" class="position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center" style="background-color: rgba(0, 0, 0, 0.5); z-index: 9999; display: none !important;">
    <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
        <span class="visually-hidden">Loading...</span>
    </div>
</div>

<div class="container-fluid py-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Server Selection Card -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">Server Selection</h3>
                </div>
                <div class="card-body">
                
                {% if not servers %}
                    <div class="alert alert-warning">
                        <h4>No Servers Found</h4>
                        <p>You don't have access to any servers with active licenses.</p>
                        <a href="{{ url_for('no_license') }}" class="btn btn-primary mt-3">Get Help</a>
                    </div>
                {% else %}
                    <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
                        {% for server in servers %}
                            <div class="col">
                                <div class="card h-100 server-card {% if server.disabled %}server-disabled{% endif %}"
                                     {% if server.disabled %}data-bs-toggle="tooltip" title="License Disabled"{% endif %}>
                                    <div class="card-body text-center">
                                        <div class="server-icon-container mb-3">
                                            <div class="server-icon-wrapper position-relative">
                                                {% if server.icon %}
                                                    <img src="{{ server.icon }}"
                                                         alt="{{ server.name }}"
                                                         class="server-icon">
                                                {% else %}
                                                    <div class="server-icon-default">
                                                        <i class="fas fa-server"></i>
                                                    </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                        <h5 class="card-title mt-2">{{ server.name }}</h5>
                                        {% if not server.disabled %}
                                            <p class="text-muted mb-3">
                                            </p>
                                            <div class="server-id small text-muted mb-2">
                                                ID: {{ server.id }}
                                            </div>
                                            <button class="btn {% if server.disabled %}btn-outline-secondary disabled{% else %}btn-primary{% endif %} manage-server"
                                                    data-server-id="{{ server.id }}"
                                                    {% if server.disabled %}disabled{% endif %}>
                                                {% if server.disabled %}
                                                    <i class="fas fa-lock me-1"></i> Disabled
                                                {% else %}
                                                    <i class="fas fa-tachometer-alt me-1"></i> Manage Server
                                                {% endif %}
                                            </button>
                                        {% else %}
                                            <p class="text-muted mb-3">
                                                <i class="fas fa-ban me-2"></i>
                                                <span class="badge bg-danger rounded-pill">
                                                    License Disabled
                                                </span>
                                            </p>
                                            <div class="server-id small text-muted mb-2">
                                                ID: {{ server.id }}
                                            </div>
                                            <div class="d-grid gap-2">
                                                <button class="btn btn-secondary" disabled>
                                                    <i class="fas fa-ban me-1"></i> Access Disabled
                                                </button>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}
                </div>
            </div>

            <!-- Footer Section -->
            <div class="text-center mt-4">
                <a href="{{ url_for('logout') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-sign-out-alt me-1"></i> Logout
                </a>
            </div>

            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
// Initialize tooltips
var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl)
});

document.addEventListener('DOMContentLoaded', function() {
    // Add click handlers to all manage server buttons
    document.querySelectorAll('.manage-server').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const serverId = this.getAttribute('data-server-id');
            console.log('Manage server button clicked for server ID:', serverId);
            selectServer(serverId, this);
        });
    });

    // Format all elements with number-format class
    document.querySelectorAll('.number-format').forEach(function(el) {
        el.textContent = new Intl.NumberFormat().format(el.textContent);
    });
});

async function selectServer(serverId, buttonElement) {
    if (!serverId) {
        console.error('No server ID provided');
        return;
    }

    // Show loading state
    const originalText = buttonElement.innerHTML;
    buttonElement.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Loading...';
    buttonElement.disabled = true;

    // Show loading spinner
    document.getElementById('loadingSpinner').style.display = 'flex !important';

    try {
        // Make API call to select the server
        const response = await fetch(`/select-server/${serverId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'same-origin'
        });

        const data = await response.json();

        if (response.ok && data.success) {

            // Redirect to dashboard on success
            setTimeout(() => {
                window.location.href = data.redirect || '/dashboard';
            }, 1000);
        } else {
            // Show error message
            const errorMsg = data.error || 'Failed to select server. Please try again.';
            showAlert(errorMsg, 'danger');

            // Reset button state
            buttonElement.innerHTML = originalText;
            buttonElement.disabled = false;
            document.getElementById('loadingSpinner').style.display = 'none';

            // If the error indicates session expired, redirect to login
            if (data.logout) {
                setTimeout(() => {
                    window.location.href = '/logout';
                }, 1500);
            }
        }
    } catch (error) {
        console.error('Error selecting server:', error);
        showAlert('An error occurred while selecting the server. Please try again.', 'danger');

        // Reset button state
        buttonElement.innerHTML = originalText;
        buttonElement.disabled = false;
        document.getElementById('loadingSpinner').style.display = 'none';
    }
}









function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '9999';
    alertDiv.style.maxWidth = '400px';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    document.body.appendChild(alertDiv);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        const bsAlert = new bootstrap.Alert(alertDiv);
        bsAlert.close();
    }, 5000);
}
</script>
{% endblock %}


