{% extends "base.html" %}

{% block title %}Dashboard - {{ server_info.name }}{% endblock %}

{% block styles %}
<style>
    .dashboard-header {
        background: linear-gradient(135deg, rgba(88, 101, 242, 0.1) 0%, rgba(0, 212, 255, 0.1) 100%);
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(20px);
        border: 1px solid var(--border-color);
    }

    .dashboard-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    }

    .server-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        border: 3px solid var(--border-color);
        transition: all 0.3s ease;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    }

    .server-avatar:hover {
        border-color: var(--primary-color);
        box-shadow: 0 8px 25px var(--glow-primary);
    }

    .server-title {
        font-size: 2rem;
        font-weight: 700;
        background: linear-gradient(135deg, var(--text-primary), var(--text-secondary));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 0.5rem;
    }

    .server-stats {
        display: flex;
        gap: 1.5rem;
        align-items: center;
        margin-bottom: 1rem;
        flex-wrap: wrap;
    }

    .server-stat {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--text-secondary);
        font-weight: 500;
    }

    .server-stat i {
        color: var(--primary-color);
    }

    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 600;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .dashboard-actions {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .go-back-btn {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid var(--border-color);
        color: var(--text-secondary);
        padding: 0.75rem 1.5rem;
        border-radius: 25px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        gap: 0.5rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .go-back-btn:hover {
        background: rgba(255, 255, 255, 0.1);
        border-color: var(--primary-color);
        color: var(--text-primary);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(88, 101, 242, 0.2);
    }

    .go-back-btn:active {
        transform: translateY(0);
    }

    .feature-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 1.5rem;
        margin-top: 2rem;
    }

    .feature-card {
        position: relative;
        overflow: hidden;
        background: rgba(255, 255, 255, 0.03);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        backdrop-filter: blur(20px);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .feature-card:hover {
        transform: translateY(-5px);
        border-color: var(--primary-color);
        box-shadow: 0 15px 40px rgba(88, 101, 242, 0.15);
    }

    .feature-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .feature-card:hover::before {
        opacity: 1;
    }

    .feature-card .card-header {
        display: flex;
        justify-content-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .feature-title {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: 600;
        margin: 0;
    }

    .feature-status {
        display: flex;
        align-items: center;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .config-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .config-item {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }

    .config-label {
        font-weight: 600;
        color: var(--text-secondary);
        font-size: 0.875rem;
    }

    .config-value {
        color: var(--text-primary);
    }

    .config-value code {
        background: rgba(255, 255, 255, 0.1);
        padding: 0.25rem 0.5rem;
        border-radius: 6px;
        font-family: 'Monaco', 'Menlo', monospace;
        font-size: 0.875rem;
    }

    .alert-compact {
        padding: 0.75rem 1rem;
        margin-bottom: 1rem;
        border-radius: 10px;
        font-size: 0.875rem;
    }

    .feature-description {
        color: var(--text-muted);
        font-size: 0.9rem;
        line-height: 1.5;
        margin-bottom: 1rem;
    }

    @media (max-width: 768px) {
        .dashboard-header {
            padding: 1.5rem;
        }

        .dashboard-header .d-flex {
            flex-direction: column;
            align-items: flex-start !important;
            gap: 1rem;
        }

        .dashboard-actions {
            width: 100%;
            justify-content: flex-end;
        }

        .server-title {
            font-size: 1.5rem;
        }

        .server-stats {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .feature-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .feature-card .card-header {
            flex-direction: column;
            align-items: flex-start;
        }

        .config-info {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="row mt-3">
    <!-- Sidebar -->
    <div class="col-md-3">
        {% include 'sidebar.html' %}
    </div>

    <!-- Main Content -->
    <div class="col-md-9">
        <!-- Server Info -->
        <div class="dashboard-header">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    {% if server_icon %}
                    <img src="{{ server_icon }}" alt="{{ server_name }}" class="server-avatar me-4">
                    {% else %}
                    <div class="server-avatar me-4 d-flex align-items-center justify-content-center" style="background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));">
                        <i class="fas fa-server fa-2x text-white"></i>
                    </div>
                    {% endif %}
                    <div class="flex-grow-1">
                        <h1 class="server-title">{{ server_name }}</h1>
                        <div class="server-stats">
                            <div class="server-stat">
                                <i class="fas fa-users"></i>
                                <span id="memberCount">{{ member_count|number_format if member_count is defined else 'Loading...' }}</span> members
                            </div>
                            <div class="server-stat">
                                <i class="fas fa-id-badge"></i>
                                <span>{{ server_id }}</span>
                            </div>
                        </div>
                        {% if is_configured %}
                        <span class="badge bg-success status-badge">
                            <i class="fas fa-check"></i>Fully Configured
                        </span>
                        {% else %}
                        <span class="badge bg-warning status-badge">
                            <i class="fas fa-exclamation-triangle"></i>Configuration Incomplete
                        </span>
                        {% endif %}
                    </div>
                </div>

                <!-- Go Back Button -->
                <div class="dashboard-actions">
                    <a href="{{ url_for('select_server') }}" class="btn btn-outline-secondary go-back-btn">
                        <i class="fas fa-arrow-left me-2"></i>Go Back
                    </a>
                </div>
            </div>
        </div>

        <!-- Bot Status -->
        {% if not bot_in_server %}
        <div class="alert alert-danger alert-compact">
            <div class="d-flex align-items-center mb-2">
                <i class="fas fa-robot me-2"></i>
                <strong>Bot Not in Server</strong>
            </div>
            <p class="mb-3">The bot is not currently in this server. Please invite the bot to the server to enable all features.</p>
            <a href="https://discord.com/oauth2/authorize?client_id={{ bot_client_id }}&permissions=8&scope=bot%20applications.commands"
               class="btn btn-sm btn-danger" target="_blank">
                <i class="fas fa-robot me-1"></i>Invite Bot
            </a>
        </div>
        {% endif %}

        <!-- Feature Cards -->
        <div class="feature-grid">
            <!-- Repping System -->
            <div class="card feature-card h-100">
                <div class="card-header">
                    <div class="feature-title">
                        <i class="fas fa-star text-warning"></i>
                        <span>Repping System</span>
                    </div>
                    <div class="feature-status">
                        {% if (config.repping_role_id or config.role_id) and (config.repping_channel_id or config.channel_id) and config.trigger_word %}
                        <span class="badge bg-success">Configured</span>
                        {% else %}
                        <span class="badge bg-danger">Not Configured</span>
                        {% endif %}

                        <!-- Enable/Disable Toggle -->
                        {% if (config.repping_role_id or config.role_id) and (config.repping_channel_id or config.channel_id) and config.trigger_word %}
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="reppingToggle"
                                   {% if config.get('repping_enabled', True) %}checked{% endif %}
                                   onchange="toggleFeature('repping', this.checked)">
                            <label class="form-check-label" for="reppingToggle">
                                <small>{% if config.get('repping_enabled', True) %}Enabled{% else %}Disabled{% endif %}</small>
                            </label>
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body d-flex flex-column">
                    <div class="config-info">
                        <div class="config-item">
                            <div class="config-label">Status</div>
                            <div class="config-value">
                                {% if (config.repping_role_id or config.role_id) and (config.repping_channel_id or config.channel_id) and config.trigger_word %}
                                    {% if config.get('repping_enabled', True) %}
                                    <span class="text-success">Active</span>
                                    {% else %}
                                    <span class="text-warning">Configured but Disabled</span>
                                    {% endif %}
                            {% else %}
                                <span class="text-danger">Inactive</span>
                                {% endif %}
                                {% if not bot_in_server %}
                                <span class="badge bg-danger ms-2">Bot Required</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="config-item">
                            <div class="config-label">Trigger Word</div>
                            <div class="config-value">
                                <code>{{ config.trigger_word if config.trigger_word else 'Not set' }}</code>
                            </div>
                        </div>
                        <div class="config-item">
                            <div class="config-label">Role</div>
                            <div class="config-value">
                                {% if config.repping_role_id or config.role_id %}
                                <span class="badge bg-primary">{{ config.repping_role_id or config.role_id }}</span>
                                {% else %}
                                <span class="text-muted">Not set</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="config-item">
                            <div class="config-label">Channel</div>
                            <div class="config-value">
                                {% if config.repping_channel_id or config.channel_id %}
                                <span class="badge bg-primary">{{ config.repping_channel_id or config.channel_id }}</span>
                                {% else %}
                                <span class="text-muted">Not set</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="mt-auto">
                        <p class="feature-description">Configure automatic role assignment based on user status.</p>
                        {% if not config.log_channel_id %}
                        {% endif %}
                        <a href="{{ url_for('configure_repping') }}" class="btn btn-sm btn-primary w-100">
                            <i class="fas fa-cog me-1"></i>{% if (config.repping_role_id or config.role_id) and (config.repping_channel_id or config.channel_id) and config.trigger_word %}Reconfigure{% else %}Configure{% endif %}
                        </a>
                    </div>
                </div>
            </div>

            <!-- Auto-Roling System -->
            <div class="card feature-card h-100">
                <div class="card-header">
                    <div class="feature-title">
                        <i class="fas fa-user-plus text-success"></i>
                        <span>Auto-Roling</span>
                    </div>
                    <div class="feature-status">
                        {% if auto_roling_settings and auto_roling_settings.role_id %}
                        <span class="badge bg-success">Configured</span>
                        {% else %}
                        <span class="badge bg-secondary">Not Configured</span>
                        {% endif %}

                        <!-- Enable/Disable Toggle -->
                        {% if auto_roling_settings and auto_roling_settings.role_id %}
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="autoRolingToggle"
                                   {% if auto_roling_settings.get('enabled', False) and not auto_roling_settings.get('permission_error', False) %}checked{% endif %}
                                   {% if auto_roling_settings.get('permission_error', False) %}disabled{% endif %}
                                   onchange="toggleFeature('auto_roling', this.checked)">
                            <label class="form-check-label" for="autoRolingToggle">
                                <small>
                                    {% if auto_roling_settings.get('permission_error', False) %}
                                    Permission Error
                                    {% elif auto_roling_settings.get('enabled', False) %}
                                    Enabled
                                    {% else %}
                                    Disabled
                                    {% endif %}
                                </small>
                            </label>
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body d-flex flex-column">
                    <div class="config-info">
                        <div class="config-item">
                            <div class="config-label">Status</div>
                            <div class="config-value">
                                {% if auto_roling_settings and auto_roling_settings.role_id %}
                                    {% if auto_roling_settings.get('permission_error', False) %}
                                    <span class="text-danger">Permission Error</span>
                                    {% elif auto_roling_settings.get('enabled', False) %}
                                    <span class="text-success">Active</span>
                                    {% else %}
                                    <span class="text-warning">Configured but Disabled</span>
                                    {% endif %}
                                {% else %}
                                <span class="text-danger">Inactive - Requires Configuration</span>
                                {% endif %}
                                {% if not bot_in_server %}
                                <span class="badge bg-danger ms-2">Bot Required</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="config-item">
                            <div class="config-label">Auto-Role</div>
                            <div class="config-value">
                                {% if auto_roling_settings and auto_roling_settings.role_id %}
                                <span class="badge bg-primary" id="auto-role-display">{{ auto_roling_settings.role_id }}</span>
                                {% else %}
                                <span class="text-muted">Not set</span>
                                {% endif %}
                            </div>
                        </div>
                        {% if auto_roling_settings and auto_roling_settings.get('permission_error') and auto_roling_settings.get('last_error') %}
                        <div class="config-item">
                            <div class="config-label">Error</div>
                            <div class="config-value">
                                <small class="text-danger">{{ auto_roling_settings.last_error }}</small>
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    <div class="mt-auto">
                        <p class="feature-description">Automatically assign roles to new members when they join.</p>
                        <a href="{{ url_for('configure_auto_roling') }}" class="btn btn-sm btn-primary w-100">
                            <i class="fas fa-cog me-1"></i>{% if auto_roling_settings and auto_roling_settings.role_id %}Reconfigure{% else %}Configure{% endif %}
                        </a>
                    </div>
                </div>
            </div>

            <!-- Vent System -->
            <div class="card feature-card h-100">
                <div class="card-header">
                    <div class="feature-title">
                        <i class="fas fa-heart text-danger"></i>
                        <span>Vent System</span>
                    </div>
                    <div class="feature-status">
                        {% if vent_settings and vent_settings.vent_channel_id %}
                        <span class="badge bg-success">Configured</span>
                        {% else %}
                        <span class="badge bg-secondary">Not Configured</span>
                        {% endif %}

                        <!-- Enable/Disable Toggle -->
                        {% if vent_settings and vent_settings.vent_channel_id %}
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="ventToggle"
                                   {% if vent_settings.get('enabled', True) %}checked{% endif %}
                                   onchange="toggleFeature('vent', this.checked)">
                            <label class="form-check-label" for="ventToggle">
                                <small>{% if vent_settings.get('enabled', True) %}Enabled{% else %}Disabled{% endif %}</small>
                            </label>
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body d-flex flex-column">
                    {% if vent_settings and vent_settings.vent_channel_id %}
                    <div class="config-info">
                        <div class="config-item">
                            <div class="config-label">Status</div>
                            <div class="config-value">
                                <span class="{% if vent_settings.get('enabled', True) %}text-success{% else %}text-warning{% endif %}">
                                    {% if vent_settings.get('enabled', True) %}Active{% else %}Disabled{% endif %}
                                </span>
                            </div>
                        </div>
                        <div class="config-item">
                            <div class="config-label">Vent Channel</div>
                            <div class="config-value">
                                <span class="badge bg-primary">{{ vent_settings.vent_channel_id }}</span>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <p class="feature-description">Allow users to send anonymous messages to a designated channel.</p>
                    {% endif %}

                    <div class="mt-auto">
                        <a href="{{ url_for('configure_vent') }}" class="btn btn-primary btn-sm w-100">
                            <i class="fas fa-cog me-1"></i>Configure
                        </a>
                    </div>
                </div>
            </div>

            <!-- Temp Voice -->
            <div class="card feature-card h-100">
                <div class="card-header">
                    <div class="feature-title">
                        <i class="fas fa-microphone text-info"></i>
                        <span>Temp Voice</span>
                    </div>
                    <div class="feature-status">
                        {% if tempvoice_settings and tempvoice_settings.interface_channel_id and tempvoice_settings.creator_channel_id %}
                        <span class="badge bg-success">Configured</span>
                        {% else %}
                        <span class="badge bg-secondary">Not Configured</span>
                        {% endif %}

                        <!-- Enable/Disable Toggle -->
                        {% if tempvoice_settings and tempvoice_settings.interface_channel_id and tempvoice_settings.creator_channel_id %}
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="tempvoiceToggle"
                                   {% if tempvoice_settings.get('enabled', True) %}checked{% endif %}
                                   onchange="toggleFeature('tempvoice', this.checked)">
                            <label class="form-check-label" for="tempvoiceToggle">
                                <small>{% if tempvoice_settings.get('enabled', True) %}Enabled{% else %}Disabled{% endif %}</small>
                            </label>
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body d-flex flex-column">
                    {% if tempvoice_settings and tempvoice_settings.interface_channel_id and tempvoice_settings.creator_channel_id %}
                    <div class="config-info">
                        <div class="config-item">
                            <div class="config-label">Status</div>
                            <div class="config-value">
                                <span class="{% if tempvoice_settings.get('enabled', True) %}text-success{% else %}text-warning{% endif %}">
                                    {% if tempvoice_settings.get('enabled', True) %}Active{% else %}Disabled{% endif %}
                                </span>
                            </div>
                        </div>
                        <div class="config-item">
                            <div class="config-label">Interface Channel</div>
                            <div class="config-value">
                                <span class="badge bg-primary">{{ tempvoice_settings.interface_channel_id }}</span>
                            </div>
                        </div>
                        <div class="config-item">
                            <div class="config-label">Creator Channel</div>
                            <div class="config-value">
                                <span class="badge bg-primary">{{ tempvoice_settings.creator_channel_id }}</span>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <p class="feature-description">Temporary voice channels with full user management.</p>
                    {% endif %}

                    <div class="mt-auto">
                        <a href="{{ url_for('configure_tempvoice') }}" class="btn btn-primary btn-sm w-100">
                            <i class="fas fa-cog me-1"></i>Configure
                        </a>
                    </div>
                </div>
            </div>

            <!-- Music System -->
            <div class="card feature-card h-100">
                <div class="card-header">
                    <div class="feature-title">
                        <i class="fas fa-music text-purple"></i>
                        <span>Music System</span>
                    </div>
                    <div class="feature-status">
                        {% if music_settings %}
                        <span class="badge bg-success">Configured</span>
                        {% else %}
                        <span class="badge bg-secondary">Ready to Configure</span>
                        {% endif %}

                        <!-- Enable/Disable Toggle -->
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="musicToggle"
                                   {% if music_settings and music_settings.get('enabled', True) %}checked{% endif %}
                                   onchange="toggleFeature('music', this.checked)">
                            <label class="form-check-label" for="musicToggle">
                                <small>{% if music_settings and music_settings.get('enabled', True) %}Enabled{% else %}Disabled{% endif %}</small>
                            </label>
                        </div>
                    </div>
                </div>
                <div class="card-body d-flex flex-column">
                    <div class="config-grid">
                        <div class="config-item">
                            <div class="config-label">Status</div>
                            <div class="config-value">
                                {% if music_settings and music_settings.enabled %}
                                <span class="badge bg-success">Enabled</span>
                                {% else %}
                                <span class="badge bg-secondary">Disabled</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="config-item">
                            <div class="config-label">DJ Role</div>
                            <div class="config-value">
                                {% if music_settings and music_settings.dj_role_id %}
                                <span class="badge bg-primary">Set</span>
                                {% else %}
                                <span class="badge bg-secondary">Manage Channels</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <p class="feature-description mt-2">Play music from YouTube with queue management and DJ controls.</p>
                    <div class="alert alert-info alert-compact">
                        <i class="fas fa-info-circle me-1"></i>
                        <small>Supports YouTube search, playlists, and voice channel management.</small>
                    </div>

                    <div class="mt-auto">
                        <a href="{{ url_for('configure_music') }}" class="btn btn-primary btn-sm w-100">
                            <i class="fas fa-cog me-1"></i>Configure
                        </a>
                    </div>
                </div>
            </div>

            <!-- Sticky Messages -->
            <div class="card feature-card h-100">
                <div class="card-header">
                    <div class="feature-title">
                        <i class="fas fa-thumbtack text-success"></i>
                        <span>Sticky Messages</span>
                    </div>
                    <div class="feature-status">
                        {% if sticky_messages %}
                        <span class="badge bg-success">{{ sticky_messages|length }} Active</span>
                        {% else %}
                        <span class="badge bg-secondary">None Active</span>
                        {% endif %}

                        <!-- Enable/Disable Toggle -->
                        {% if sticky_messages %}
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="sticky_messagesToggle"
                                   {% if sticky_messages[0].get('enabled', True) %}checked{% endif %}
                                   onchange="toggleFeature('sticky_messages', this.checked)">
                            <label class="form-check-label" for="sticky_messagesToggle">
                                <small>{% if sticky_messages[0].get('enabled', True) %}Enabled{% else %}Disabled{% endif %}</small>
                            </label>
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body d-flex flex-column">
                    {% if sticky_messages %}
                    <div class="config-info">
                        <div class="config-item">
                            <div class="config-label">Status</div>
                            <div class="config-value">
                                <span class="{% if sticky_messages[0].get('enabled', True) %}text-success{% else %}text-warning{% endif %}">
                                    {% if sticky_messages[0].get('enabled', True) %}Active{% else %}Disabled{% endif %}
                                </span>
                            </div>
                        </div>
                        <div class="config-item">
                            <div class="config-label">Messages</div>
                            <div class="config-value">
                                <span class="badge bg-primary">{{ sticky_messages|length }} configured</span>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <p class="feature-description">Persistent messages that automatically repost.</p>
                    <div class="alert alert-warning alert-compact">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                    </div>
                    {% endif %}

                    <div class="mt-auto">
                        <a href="{{ url_for('configure_sticky_messages') }}" class="btn btn-primary btn-sm w-100">
                            <i class="fas fa-cog me-1"></i>Configure
                        </a>
                    </div>
                </div>
            </div>

            <!-- DM Support -->
            <div class="card feature-card h-100">
                <div class="card-header">
                    <div class="feature-title">
                        <i class="fas fa-ticket-alt text-primary"></i>
                        <span>DM Support</span>
                    </div>
                    <div class="feature-status">
                        {% if dm_support_settings and dm_support_settings.category_id and dm_support_settings.support_role_id %}
                        <span class="badge bg-success">Configured</span>
                        {% else %}
                        <span class="badge bg-secondary">Not Configured</span>
                        {% endif %}

                        <!-- Enable/Disable Toggle -->
                        {% if dm_support_settings and dm_support_settings.category_id and dm_support_settings.support_role_id %}
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="dm_supportToggle"
                                   {% if dm_support_settings.get('enabled', True) %}checked{% endif %}
                                   onchange="toggleFeature('dm_support', this.checked)">
                            <label class="form-check-label" for="dm_supportToggle">
                                <small>{% if dm_support_settings.get('enabled', True) %}Enabled{% else %}Disabled{% endif %}</small>
                            </label>
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body d-flex flex-column">
                    {% if dm_support_settings and dm_support_settings.category_id and dm_support_settings.support_role_id %}
                    <div class="config-info">
                        <div class="config-item">
                            <div class="config-label">Status</div>
                            <div class="config-value">
                                <span class="{% if dm_support_settings.get('enabled', True) %}text-success{% else %}text-warning{% endif %}">
                                    {% if dm_support_settings.get('enabled', True) %}Active{% else %}Disabled{% endif %}
                                </span>
                            </div>
                        </div>
                        <div class="config-item">
                            <div class="config-label">Category</div>
                            <div class="config-value">
                                <span class="badge bg-primary">{{ dm_support_settings.category_id }}</span>
                            </div>
                        </div>
                        <div class="config-item">
                            <div class="config-label">Support Role</div>
                            <div class="config-value">
                                <span class="badge bg-primary">{{ dm_support_settings.support_role_id }}</span>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <p class="feature-description">DM-based support ticket system with admin responses.</p>
                    <div class="alert alert-warning alert-compact">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        <small>Requires log channel to be configured first.</small>
                    </div>
                    {% endif %}

                    <div class="mt-auto">
                        <a href="{{ url_for('configure_dm_support') }}" class="btn btn-primary btn-sm w-100">
                            <i class="fas fa-cog me-1"></i>Configure
                        </a>
                    </div>
                </div>
            </div>

            <!-- Gender Verification -->
            <div class="card feature-card h-100">
                <div class="card-header">
                    <div class="feature-title">
                        <i class="fas fa-shield-alt text-secondary"></i>
                        <span>Gender Verification</span>
                    </div>
                    <div class="feature-status">
                        {% if gender_verification_settings and gender_verification_settings.channel_id and gender_verification_settings.category_id %}
                        <span class="badge bg-success">Configured</span>
                        {% else %}
                        <span class="badge bg-secondary">Not Configured</span>
                        {% endif %}

                        <!-- Enable/Disable Toggle -->
                        {% if gender_verification_settings and gender_verification_settings.channel_id and gender_verification_settings.category_id %}
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="gender_verificationToggle"
                                   {% if gender_verification_settings.get('enabled', True) %}checked{% endif %}
                                   onchange="toggleFeature('gender_verification', this.checked)">
                            <label class="form-check-label" for="gender_verificationToggle">
                                <small>{% if gender_verification_settings.get('enabled', True) %}Enabled{% else %}Disabled{% endif %}</small>
                            </label>
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body d-flex flex-column">
                    {% if gender_verification_settings and gender_verification_settings.channel_id and gender_verification_settings.category_id %}
                    <div class="config-info">
                        <div class="config-item">
                            <div class="config-label">Status</div>
                            <div class="config-value">
                                <span class="{% if gender_verification_settings.get('enabled', True) %}text-success{% else %}text-warning{% endif %}">
                                    {% if gender_verification_settings.get('enabled', True) %}Active{% else %}Disabled{% endif %}
                                </span>
                            </div>
                        </div>
                        <div class="config-item">
                            <div class="config-label">Channel</div>
                            <div class="config-value">
                                <span class="badge bg-primary">{{ gender_verification_settings.channel_id }}</span>
                            </div>
                        </div>
                        <div class="config-item">
                            <div class="config-label">Category</div>
                            <div class="config-value">
                                <span class="badge bg-primary">{{ gender_verification_settings.category_id }}</span>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <p class="feature-description">Manual verification system with ticket support.</p>
                    <div class="alert alert-warning alert-compact">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        <small>Requires log channel to be configured first.</small>
                    </div>
                    {% endif %}

                    <div class="mt-auto">
                        <a href="{{ url_for('configure_gender_verification') }}" class="btn btn-primary btn-sm w-100">
                            <i class="fas fa-cog me-1"></i>Configure
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Ensure toggleFeature is available immediately
if (typeof window.toggleFeature === 'undefined') {
    window.toggleFeature = function(feature, enabled) {
        console.log('Fallback toggleFeature called with:', feature, enabled);
        alert('Feature toggle is loading, please try again in a moment.');
    };
}

// Load server info and update role displays
document.addEventListener('DOMContentLoaded', function() {
    loadServerInfoAndUpdateRoles();
    initializeAnimations();
});

// Initialize modern animations and interactions
function initializeAnimations() {
    // Animate feature cards on load
    const featureCards = document.querySelectorAll('.feature-card');
    featureCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';

        setTimeout(() => {
            card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Animate dashboard header
    const header = document.querySelector('.dashboard-header');
    if (header) {
        header.style.opacity = '0';
        header.style.transform = 'translateY(-20px)';

        setTimeout(() => {
            header.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
            header.style.opacity = '1';
            header.style.transform = 'translateY(0)';
        }, 200);
    }

    // Add hover effects to badges
    const badges = document.querySelectorAll('.badge');
    badges.forEach(badge => {
        badge.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05)';
        });

        badge.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });

    // Add click ripple effect to buttons
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s ease-out;
                pointer-events: none;
            `;

            this.style.position = 'relative';
            this.style.overflow = 'hidden';
            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

// Add ripple animation keyframes (only if not already added)
if (!window.dashboardRippleStyleAdded) {
    const dashboardRippleStyle = document.createElement('style');
    dashboardRippleStyle.textContent = `
        @keyframes ripple {
            to {
                transform: scale(2);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(dashboardRippleStyle);
    window.dashboardRippleStyleAdded = true;
}

async function loadServerInfoAndUpdateRoles() {
    try {
        const response = await fetch('/api/server-info');
        const data = await response.json();

        if (data.roles) {
            // Update auto-role display
            const autoRoleDisplay = document.getElementById('auto-role-display');
            if (autoRoleDisplay) {
                const roleId = autoRoleDisplay.textContent.trim();
                const role = data.roles.find(r => r.id === roleId);
                if (role) {
                    if (role.above_bot) {
                        autoRoleDisplay.innerHTML = `<span style="color: ${role.color || '#ffc107'}">${role.name}</span> <small class="text-warning">(Above Bot)</small>`;
                        autoRoleDisplay.className = 'badge bg-warning';
                    } else {
                        autoRoleDisplay.innerHTML = `<span style="color: ${role.color || '#ffffff'}">${role.name}</span>`;
                        autoRoleDisplay.className = 'badge bg-primary';
                    }
                } else if (roleId && roleId !== 'Not set') {
                    autoRoleDisplay.innerHTML = `<span class="text-danger">Role ID: ${roleId} (deleted)</span>`;
                    autoRoleDisplay.className = 'badge bg-danger';
                }
            }
        }
    } catch (error) {
        console.error('Error loading server info:', error);
    }
}

// Toggle feature enable/disable - Make it globally available
window.toggleFeature = function(feature, enabled) {
    console.log('toggleFeature called with:', feature, enabled);

    try {
        // Get CSRF token from meta tag or generate it
        const csrfToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content') || '{{ csrf_token() }}';

        fetch('/api/toggle-feature', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken
            },
            body: JSON.stringify({
                feature: feature,
                enabled: enabled
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update the label text
                const toggle = document.getElementById(feature + 'Toggle');
                if (toggle && toggle.nextElementSibling) {
                    const label = toggle.nextElementSibling.querySelector('small');
                    if (label) {
                        label.textContent = enabled ? 'Enabled' : 'Disabled';
                    }
                }

                // Show success message
                if (typeof showToast === 'function') {
                    showToast('Feature ' + (enabled ? 'enabled' : 'disabled') + ' successfully!', 'success');
                } else {
                    console.log('Feature ' + (enabled ? 'enabled' : 'disabled') + ' successfully!');
                }

                // Refresh the page after a short delay to update status
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                // Revert the toggle if the request failed
                const toggle = document.getElementById(feature + 'Toggle');
                if (toggle) {
                    toggle.checked = !enabled;
                }

                if (typeof showToast === 'function') {
                    showToast('Failed to update feature: ' + data.error, 'error');
                } else {
                    console.error('Failed to update feature:', data.error);
                    alert('Failed to update feature: ' + data.error);
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            // Revert the toggle if the request failed
            const toggle = document.getElementById(feature + 'Toggle');
            if (toggle) {
                toggle.checked = !enabled;
            }

            if (typeof showToast === 'function') {
                showToast('An error occurred while updating the feature.', 'error');
            } else {
                console.error('An error occurred while updating the feature.');
                alert('An error occurred while updating the feature.');
            }
        });
    } catch (error) {
        console.error('Error in toggleFeature:', error);
        alert('An error occurred while updating the feature.');
    }
};

// Simple toast notification function - Make it globally available
window.showToast = function(message, type) {
    try {
        // Create toast element
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Add to page
        document.body.appendChild(toast);

        // Auto-remove after 3 seconds
        setTimeout(() => {
            if (toast && toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    } catch (error) {
        console.error('Error showing toast:', error);
        // Fallback to alert
        alert(message);
    }
};

// Global error handler for dashboard
window.addEventListener('error', function(event) {
    console.error('Dashboard JavaScript error:', event.error);
    // Don't show toast for every error to avoid spam
});

// Global unhandled promise rejection handler
window.addEventListener('unhandledrejection', function(event) {
    console.error('Dashboard unhandled promise rejection:', event.reason);
});

// Ensure all required functions are available
document.addEventListener('DOMContentLoaded', function() {
    // Check if all required functions are defined
    const requiredFunctions = ['toggleFeature', 'showToast'];
    requiredFunctions.forEach(funcName => {
        if (typeof window[funcName] !== 'function') {
            console.error(`Required function ${funcName} is not defined`);
        }
    });
});
</script>
{% endblock %}
