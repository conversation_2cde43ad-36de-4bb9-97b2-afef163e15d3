{% extends "base.html" %}

{% block title %}Select License - ry<PERSON><PERSON>{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0"><i class="fas fa-key me-2"></i>Select License Key</h4>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-4">
                        You have multiple license keys associated with your account. 
                        Please select one to continue to the dashboard.
                    </p>
                    
                    <div class="list-group">
                        {% for license in license_keys %}
                        <a href="{{ url_for('use_license', license_key=license.key) }}" 
                           class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">
                                    {% if license.server_name %}
                                        {{ license.server_name }}
                                    {% else %}
                                        License Key: {{ license.key[:8] }}...{{ license.key[-4:] }}
                                    {% endif %}
                                </h6>
                                <small class="text-muted">
                                    <i class="fas fa-server me-1"></i> 
                                    {% if license.server_id %}
                                        Server ID: {{ license.server_id }}
                                    {% else %}
                                        Not yet redeemed
                                    {% endif %}
                                </small>
                            </div>
                            <div class="text-end">
                                <span class="badge {% if license.status == 'active' %}bg-success{% else %}bg-warning{% endif %} me-2">
                                    {{ license.status|title }}
                                </span>
                                <i class="fas fa-chevron-right"></i>
                            </div>
                        </a>
                        {% endfor %}
                    </div>
                    
                    <div class="mt-4 text-center">
                        <a href="https://ryzuo.com/shop" target="_blank" class="btn btn-outline-primary me-2">
                            <i class="fas fa-plus me-2"></i>Purchase Additional License
                        </a>
                        <a href="{{ url_for('logout') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
