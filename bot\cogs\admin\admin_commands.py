import discord
from discord.ext import commands
from discord import app_commands
import logging
import os

logger = logging.getLogger(__name__)

class AdminCommands(commands.Cog):
    """Admin-only commands for subscription management and bot administration"""

    def __init__(self, bot):
        self.bot = bot
        self.db = bot.db
        self.ADMIN_USER_ID = int(os.getenv('ADMIN_USER_ID')) if os.getenv('ADMIN_USER_ID') else None

    def format_admin_embed(self, title: str, description: str, color: discord.Color) -> discord.Embed:
        """Create a formatted embed for admin messages"""
        embed = discord.Embed(
            title=title,
            description=description,
            color=color
        )
        embed.set_footer(text="ryzuo Bot - Subscription Management")
        return embed
    
    @app_commands.command(name="disable-subscription", description="Disable a user's subscription (Admin only)")
    async def disable_subscription(self, interaction: discord.Interaction, stripe_subscription_id: str):
        """Disable a subscription (Admin only)"""
        logger.info(f"disable-subscription command used by {interaction.user} (ID: {interaction.user.id})")

        if interaction.user.id != self.ADMIN_USER_ID:
            await interaction.response.send_message("❌ Access denied.", ephemeral=True)
            return

        await interaction.response.defer(ephemeral=True)

        # Check if subscription exists
        subscription_info = self.db.get_subscription_by_stripe_id(stripe_subscription_id)
        if not subscription_info:
            embed = self.format_admin_embed(
                "❌ Subscription Not Found",
                f"Subscription `{stripe_subscription_id}` does not exist in the database.",
                discord.Color.red()
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
            return

        # Disable the subscription
        success = self.db.disable_subscription_admin(stripe_subscription_id)

        if success:
            embed = self.format_admin_embed(
                "✅ Subscription Disabled",
                f"Subscription `{stripe_subscription_id}` has been disabled.\n\n"
                f"**Subscription Information:**\n"
                f"• User ID: <@{subscription_info.get('user_id', 'None')}>\n"
                f"• Tier: {subscription_info.get('subscription_tier', 'Unknown')}\n"
                f"• Status: {subscription_info.get('status', 'Unknown')}\n\n"
                f"All premium features for this user are now disabled.",
                discord.Color.orange()
            )
            logger.info(f"Subscription {stripe_subscription_id} disabled by admin {interaction.user} ({interaction.user.id})")
        else:
            embed = self.format_admin_embed(
                "❌ Failed to Disable Subscription",
                f"Failed to disable subscription `{stripe_subscription_id}`. Please try again.",
                discord.Color.red()
            )

        await interaction.followup.send(embed=embed, ephemeral=True)

    @app_commands.command(name="enable-subscription", description="Enable a user's subscription (Admin only)")
    async def enable_subscription(self, interaction: discord.Interaction, stripe_subscription_id: str):
        """Enable a subscription (Admin only)"""
        if interaction.user.id != self.ADMIN_USER_ID:
            await interaction.response.send_message("❌ Access denied.", ephemeral=True)
            return

        await interaction.response.defer(ephemeral=True)

        # Check if subscription exists
        subscription_info = self.db.get_subscription_by_stripe_id(stripe_subscription_id)
        if not subscription_info:
            embed = self.format_admin_embed(
                "❌ Subscription Not Found",
                f"Subscription `{stripe_subscription_id}` does not exist in the database.",
                discord.Color.red()
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
            return

        # Enable the subscription
        success = self.db.enable_subscription_admin(stripe_subscription_id)

        if success:
            embed = self.format_admin_embed(
                "✅ Subscription Enabled",
                f"Subscription `{stripe_subscription_id}` has been enabled.\n\n"
                f"**Subscription Information:**\n"
                f"• User ID: <@{subscription_info.get('user_id', 'None')}>\n"
                f"• Tier: {subscription_info.get('subscription_tier', 'Unknown')}\n"
                f"• Status: {subscription_info.get('status', 'Unknown')}\n\n"
                f"All premium features for this user are now active.",
                discord.Color.green()
            )
            logger.info(f"Subscription {stripe_subscription_id} enabled by admin {interaction.user} ({interaction.user.id})")
        else:
            embed = self.format_admin_embed(
                "❌ Failed to Enable Subscription",
                f"Failed to enable subscription `{stripe_subscription_id}`. Please try again.",
                discord.Color.red()
            )

        await interaction.followup.send(embed=embed, ephemeral=True)

    @app_commands.command(name="user-subscription", description="Get subscription info for a specific user (Admin only)")
    async def user_subscription(self, interaction: discord.Interaction, user_id: str):
        """Get subscription information for a specific user (Admin only)"""
        if interaction.user.id != self.ADMIN_USER_ID:
            await interaction.response.send_message("❌ Access denied.", ephemeral=True)
            return

        await interaction.response.defer(ephemeral=True)

        try:
            user_id_int = int(user_id)
        except ValueError:
            await interaction.followup.send("❌ Invalid user ID format.", ephemeral=True)
            return

        # Get subscription for user
        subscription_info = self.db.get_user_subscription(user_id_int)
        if not subscription_info:
            await interaction.followup.send(f"❌ No active subscription found for user <@{user_id_int}>.", ephemeral=True)
            return

        # Get user information if possible
        user = self.bot.get_user(user_id_int)
        user_name = user.display_name if user else "Unknown User"

        embed = self.format_admin_embed(
            "👑 User Subscription",
            f"**User:** {user_name} (<@{user_id_int}>)\n"
            f"**Subscription ID:** `{subscription_info['stripe_subscription_id']}`\n\n"
            f"**Subscription Information:**\n"
            f"• Tier: {subscription_info.get('subscription_tier', 'Unknown')}\n"
            f"• Status: {subscription_info.get('status', 'Unknown')}\n"
            f"• Created: {subscription_info.get('created_at', 'Unknown')}\n"
            f"• Admin Disabled: {'Yes' if subscription_info.get('disabled_by_admin') else 'No'}",
            discord.Color.blue()
        )

        await interaction.followup.send(embed=embed, ephemeral=True)

async def setup(bot):
    await bot.add_cog(AdminCommands(bot))
