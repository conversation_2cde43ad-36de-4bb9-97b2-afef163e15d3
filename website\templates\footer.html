<!-- Footer -->
<footer class="site-footer">
    <div class="footer-container">
        <!-- Logo Section -->
        <div class="footer-logo">
            <img src="{{ url_for('static', filename='logo.png') }}" alt="ryzuo Bo<PERSON>" class="footer-logo-img">
        </div>

        <!-- Copyright Text -->
        <div class="footer-copyright">
            <p>Copyright © 2025 ryzuo Bot. All rights reserved.</p>
        </div>

        <!-- Bot Links -->
        <div class="footer-section">
            <h4>Bot</h4>
            <ul>
                <li><a href="https://discord.com/oauth2/authorize?client_id=1389466386396483714" target="_blank">Invite</a></li>
                <li><a href="/docs">Documentation</a></li>
                <li><a href="https://discord.gg/ryzuo" target="_blank">Support Server</a></li>
            </ul>
        </div>

        <!-- Legal Links -->
        <div class="footer-section">
            <h4>Legal</h4>
            <ul>
                <li><a href="/terms">Terms of Service</a></li>
                <li><a href="/privacy">Privacy Policy</a></li>
                <li><a href="/refund">Refund Policy</a></li>
            </ul>
        </div>
    </div>
</footer>

<!-- Back to Top Button -->
<button class="back-to-top" id="backToTop" onclick="scrollToTop()">
    <i class="fas fa-chevron-up"></i>
</button>

<style>
/* Ensure CSS variables are available */
:root {
    --primary-color: #5865f2;
    --secondary-color: #7289da;
    --accent-color: #00d4ff;
    --bg-primary: #0a0a0f;
    --bg-secondary: #101218;
    --bg-tertiary: #1a1d29;
    --text-primary: #ffffff;
    --text-secondary: #b9bbbe;
    --text-muted: #72767d;
    --border-color: rgba(255, 255, 255, 0.1);
    --glow-primary: rgba(88, 101, 242, 0.5);
    --glow-accent: rgba(0, 212, 255, 0.3);
}

/* Footer Styles */
.site-footer {
    background: rgba(10, 10, 15, 0.9);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(88, 101, 242, 0.3);
    box-shadow: 0 0 20px rgba(88, 101, 242, 0.1);
    padding: 40px 0 20px;
    margin-top: 120px; /* Increased margin to prevent overlap with content */
    position: relative;
    z-index: 10;
    width: 100%;
    clear: both;
    flex-shrink: 0; /* Prevent footer from shrinking */
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 80px;
    min-height: 80px;
}

.footer-logo {
    display: flex;
    align-items: center;
}

.footer-logo-img {
    width: 80px;
    height: 80px;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.footer-logo-img:hover {
    transform: scale(1.05);
    filter: brightness(1.1);
}

.footer-copyright {
    display: flex;
    align-items: center;
}

.footer-copyright p {
    color: var(--text-muted);
    font-size: 0.9rem;
    margin: 0;
    font-weight: 500;
    white-space: nowrap;
}

.footer-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.footer-section h4 {
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
    position: relative;
}

.footer-section h4::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: 1px;
}

.footer-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.footer-section ul li a {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.95rem;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: inline-block;
    position: relative;
}

.footer-section ul li a:hover {
    color: var(--primary-color);
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 100px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border: none;
    border-radius: 50%;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    box-shadow: 0 8px 25px rgba(88, 101, 242, 0.4);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Hide back to top button on status page */
body[data-page="status"] .back-to-top {
    display: none !important;
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    transform: translateY(-5px) scale(1.1);
    box-shadow: 0 12px 35px rgba(88, 101, 242, 0.6);
}

.back-to-top:active {
    transform: translateY(-3px) scale(1.05);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .site-footer {
        margin-top: 60px;
        padding: 30px 0 80px; /* Extra padding for chat widget */
    }

    .footer-container {
        flex-direction: column;
        gap: 25px;
        text-align: center;
        align-items: center;
    }

    .footer-logo {
        order: 1;
    }

    .footer-copyright {
        order: 2;
    }

    .footer-section {
        order: 3;
        width: 100%;
        max-width: 300px;
    }

    .footer-section:last-child {
        order: 4;
    }

    .footer-section ul {
        flex-direction: row;
        justify-content: center;
        flex-wrap: wrap;
        gap: 15px;
    }

    .back-to-top {
        bottom: 90px;
        right: 20px;
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }
}

@media (max-width: 480px) {
    .site-footer {
        padding: 25px 0 100px; /* Even more padding for smaller screens */
        margin-top: 40px;
    }

    .footer-container {
        padding: 0 15px;
        gap: 20px;
    }

    .footer-logo-icon {
        width: 70px;
        height: 70px;
    }

    .footer-copyright p {
        font-size: 0.85rem;
        text-align: center;
    }

    .footer-section h4 {
        font-size: 1rem;
        margin-bottom: 12px;
    }

    .footer-section ul {
        gap: 12px;
    }

    .footer-section ul li a {
        font-size: 0.9rem;
    }

    .back-to-top {
        bottom: 80px;
        right: 15px;
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
    .footer-section ul li a:hover {
        transform: none;
    }

    .footer-section ul li a:hover::before {
        opacity: 0;
    }

    .back-to-top:hover {
        transform: none;
    }

    .back-to-top:active {
        transform: scale(0.95);
        opacity: 0.8;
    }
}
</style>

<script>
// Back to Top Button Functionality
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

// Show/Hide Back to Top Button
function toggleBackToTopButton() {
    const backToTopButton = document.getElementById('backToTop');
    if (!backToTopButton) return;

    if (window.pageYOffset > 300) {
        backToTopButton.classList.add('show');
    } else {
        backToTopButton.classList.remove('show');
    }
}

// Initialize Back to Top Button
document.addEventListener('DOMContentLoaded', function() {
    const backToTopButton = document.getElementById('backToTop');

    // Check if we're on the status page
    const isStatusPage = document.body.getAttribute('data-page') === 'status';

    if (isStatusPage && backToTopButton) {
        backToTopButton.style.display = 'none';
        return; // Don't initialize the button on status page
    }

    // Initial check
    toggleBackToTopButton();

    // Listen for scroll events
    let ticking = false;
    function optimizedScrollHandler() {
        if (!ticking) {
            requestAnimationFrame(() => {
                toggleBackToTopButton();
                ticking = false;
            });
            ticking = true;
        }
    }

    window.addEventListener('scroll', optimizedScrollHandler, { passive: true });

    // Add keyboard support for back to top button
    if (backToTopButton) {
        backToTopButton.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                scrollToTop();
            }
        });

        // Make button focusable
        backToTopButton.setAttribute('tabindex', '0');
        backToTopButton.setAttribute('aria-label', 'Back to top');
    }
});
</script>
