"""
Shard Data Manager for Website
Handles shard status data and provides API endpoints
"""

import json
import logging
import os
import time
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional
from threading import Lock
import redis

logger = logging.getLogger(__name__)

class ShardDataManager:
    def __init__(self):
        # Use Redis for shard data storage if available, otherwise use in-memory
        self.redis_url = os.getenv('REDIS_URL')
        self.redis_client = None
        self.memory_storage = {}
        self.storage_lock = Lock()
        
        # Configuration
        self.shard_timeout = int(os.getenv('SHARD_TIMEOUT', '120'))  # seconds
        self.api_key = os.getenv('SHARD_API_KEY', 'default-key')
        
        # Initialize storage
        self._init_storage()
    
    def _init_storage(self):
        """Initialize storage backend"""
        if self.redis_url:
            try:
                import redis
                self.redis_client = redis.from_url(self.redis_url, decode_responses=True)
                self.redis_client.ping()
                logger.info("Connected to Redis for shard data storage")
            except Exception as e:
                logger.warning(f"Failed to connect to Red<PERSON>, using memory storage: {e}")
                self.redis_client = None
        else:
            logger.info("Using in-memory storage for shard data")
    
    def update_shard_status(self, shard_data: Dict[str, Any]) -> bool:
        """Update shard status data"""
        try:
            shard_id = shard_data.get('shard_id')
            if shard_id is None:
                return False
            
            # Add last_seen timestamp
            shard_data['last_seen'] = datetime.now(timezone.utc).isoformat()
            
            # Store data
            if self.redis_client:
                key = f"shard:{shard_id}"
                self.redis_client.setex(key, self.shard_timeout * 2, json.dumps(shard_data))
            else:
                with self.storage_lock:
                    self.memory_storage[shard_id] = shard_data
            
            logger.debug(f"Updated status for shard {shard_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update shard status: {e}")
            return False
    
    def get_shard_status(self, shard_id: int) -> Optional[Dict[str, Any]]:
        """Get status for a specific shard"""
        try:
            if self.redis_client:
                key = f"shard:{shard_id}"
                data = self.redis_client.get(key)
                if data:
                    return json.loads(data)
            else:
                with self.storage_lock:
                    return self.memory_storage.get(shard_id)
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get shard status for {shard_id}: {e}")
            return None
    
    def get_all_shards(self) -> List[Dict[str, Any]]:
        """Get status for all shards"""
        try:
            shards = []
            
            if self.redis_client:
                # Get all shard keys
                keys = self.redis_client.keys("shard:*")
                for key in keys:
                    data = self.redis_client.get(key)
                    if data:
                        shard_data = json.loads(data)
                        # Check if shard is still alive
                        if self._is_shard_alive(shard_data):
                            shards.append(shard_data)
            else:
                with self.storage_lock:
                    for shard_data in self.memory_storage.values():
                        if self._is_shard_alive(shard_data):
                            shards.append(shard_data)
            
            # Sort by shard_id
            shards.sort(key=lambda x: x.get('shard_id', 0))
            return shards
            
        except Exception as e:
            logger.error(f"Failed to get all shards: {e}")
            return []
    
    def _is_shard_alive(self, shard_data: Dict[str, Any]) -> bool:
        """Check if a shard is considered alive based on last_seen"""
        try:
            last_seen_str = shard_data.get('last_seen')
            if not last_seen_str:
                return False
            
            last_seen = datetime.fromisoformat(last_seen_str.replace('Z', '+00:00'))
            now = datetime.now(timezone.utc)
            
            return (now - last_seen).total_seconds() < self.shard_timeout
            
        except Exception as e:
            logger.error(f"Error checking shard alive status: {e}")
            return False
    
    def cleanup_dead_shards(self):
        """Remove data for shards that haven't reported in a while"""
        try:
            if self.redis_client:
                # Redis handles expiration automatically
                pass
            else:
                with self.storage_lock:
                    dead_shards = []
                    for shard_id, shard_data in self.memory_storage.items():
                        if not self._is_shard_alive(shard_data):
                            dead_shards.append(shard_id)
                    
                    for shard_id in dead_shards:
                        del self.memory_storage[shard_id]
                        logger.info(f"Removed dead shard {shard_id}")
                        
        except Exception as e:
            logger.error(f"Failed to cleanup dead shards: {e}")
    
    def find_server_shard(self, server_id: str) -> Optional[int]:
        """Find which shard contains a specific server"""
        try:
            # For now, we'll use a simple hash-based approach
            # In a real implementation, you'd query each shard or maintain a server->shard mapping
            server_id_int = int(server_id)
            
            # Get all active shards
            shards = self.get_all_shards()
            if not shards:
                return None
            
            # Simple hash-based shard assignment (Discord's default)
            total_shards = max(shard.get('total_shards', 1) for shard in shards)
            shard_id = (server_id_int >> 22) % total_shards
            
            # Check if this shard is actually active
            for shard in shards:
                if shard.get('shard_id') == shard_id:
                    return shard_id
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to find server shard: {e}")
            return None
    
    def get_total_stats(self) -> Dict[str, Any]:
        """Get aggregated stats across all shards"""
        try:
            shards = self.get_all_shards()
            
            total_guilds = sum(shard.get('guild_count', 0) for shard in shards)
            total_users = sum(shard.get('user_count', 0) for shard in shards)
            avg_latency = 0
            
            if shards:
                latencies = [shard.get('latency', 0) for shard in shards if shard.get('latency', 0) > 0]
                if latencies:
                    avg_latency = sum(latencies) / len(latencies)
            
            return {
                'total_shards': len(shards),
                'total_guilds': total_guilds,
                'total_users': total_users,
                'average_latency': round(avg_latency),
                'operational_shards': len([s for s in shards if s.get('status') == 'operational']),
                'last_updated': datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get total stats: {e}")
            return {
                'total_shards': 0,
                'total_guilds': 0,
                'total_users': 0,
                'average_latency': 0,
                'operational_shards': 0,
                'last_updated': datetime.now(timezone.utc).isoformat()
            }
    
    def validate_api_key(self, provided_key: str) -> bool:
        """Validate API key for shard authentication"""
        return provided_key == self.api_key

# Global instance
shard_data_manager = ShardDataManager()
