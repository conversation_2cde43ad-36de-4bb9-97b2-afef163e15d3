{% extends "base.html" %}

{% block title %}Admin Panel - <PERSON><PERSON><PERSON><PERSON>{% endblock %}

{% block styles %}
<style>
    .admin-header {
        background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 127, 0.1) 100%);
        border: 1px solid rgba(239, 68, 68, 0.2);
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        text-align: center;
    }

    .admin-title {
        font-size: 2.5rem;
        font-weight: 700;
        background: linear-gradient(135deg, #ef4444, #dc2626);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 0.5rem;
    }

    .admin-subtitle {
        color: var(--text-secondary);
        font-size: 1.1rem;
    }

    .admin-card {
        background: rgba(255, 255, 255, 0.03);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        backdrop-filter: blur(20px);
    }

    .admin-card h3 {
        color: var(--text-primary);
        margin-bottom: 1.5rem;
        font-weight: 600;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        color: var(--text-secondary);
        font-weight: 500;
        margin-bottom: 0.5rem;
        display: block;
    }

    .form-control, .form-select {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid var(--border-color);
        border-radius: 10px;
        color: var(--text-primary);
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        background: rgba(255, 255, 255, 0.1);
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(88, 101, 242, 0.25);
        color: var(--text-primary);
    }

    .btn-admin {
        background: linear-gradient(135deg, #ef4444, #dc2626);
        color: white;
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 10px;
        font-weight: 600;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .btn-admin:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
    }

    .notification-preview {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid var(--border-color);
        border-radius: 10px;
        padding: 1rem;
        margin-top: 1rem;
    }

    .notification-preview h5 {
        color: var(--text-primary);
        margin-bottom: 0.5rem;
        font-size: 1rem;
    }

    .notification-preview p {
        color: var(--text-secondary);
        margin: 0;
        font-size: 0.9rem;
    }

    .alert-admin {
        padding: 1rem;
        border-radius: 10px;
        margin-bottom: 1rem;
        border: none;
    }

    .alert-success {
        background: rgba(34, 197, 94, 0.1);
        color: #22c55e;
        border: 1px solid rgba(34, 197, 94, 0.2);
    }

    .alert-danger {
        background: rgba(239, 68, 68, 0.1);
        color: #ef4444;
        border: 1px solid rgba(239, 68, 68, 0.2);
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="admin-header">
        <h1 class="admin-title">
            <i class="fas fa-shield-alt me-2"></i>Admin Panel
        </h1>
        <p class="admin-subtitle">Manage notifications and system settings</p>
    </div>

    <!-- Send Notification Card -->
    <div class="admin-card">
        <h3><i class="fas fa-bell me-2"></i>Send Notification</h3>
        
        <div id="notification-alerts"></div>

        <form id="notificationForm">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label" for="userId">User ID</label>
                        <input type="text" class="form-control" id="userId" placeholder="Enter Discord User ID" required>
                        <small class="text-muted">Enter a Discord User ID (your ID: {{ session.get('user_id') }})</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label" for="notificationType">Type</label>
                        <select class="form-select" id="notificationType" required>
                            <option value="info">Info</option>
                            <option value="success">Success</option>
                            <option value="warning">Warning</option>
                            <option value="error">Error</option>
                            <option value="system">System</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="form-label" for="notificationTitle">Title</label>
                <input type="text" class="form-control" id="notificationTitle" placeholder="Notification title" required>
            </div>

            <div class="form-group">
                <label class="form-label" for="notificationMessage">Message</label>
                <textarea class="form-control" id="notificationMessage" rows="4" placeholder="Notification message" required></textarea>
            </div>

            <!-- Preview -->
            <div class="notification-preview" id="notificationPreview" style="display: none;">
                <h5 id="previewTitle"></h5>
                <p id="previewMessage"></p>
                <small class="text-muted">Type: <span id="previewType"></span></small>
            </div>

            <button type="submit" class="btn btn-admin">
                <i class="fas fa-paper-plane me-2"></i>Send Notification
            </button>
        </form>
    </div>

    <!-- Quick Actions -->
    <div class="admin-card">
        <h3><i class="fas fa-bolt me-2"></i>Quick Actions</h3>
        <div class="row">
            <div class="col-md-6">
                <button class="btn btn-outline-primary w-100 mb-2" onclick="sendTestNotification()">
                    <i class="fas fa-test-tube me-2"></i>Send Test Notification to Yourself
                </button>
            </div>
            <div class="col-md-6">
                <button class="btn btn-outline-info w-100 mb-2" onclick="fillSampleData()">
                    <i class="fas fa-fill me-2"></i>Fill Sample Data
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('notificationForm');
    const alertsContainer = document.getElementById('notification-alerts');
    const preview = document.getElementById('notificationPreview');
    
    // Update preview when form changes
    function updatePreview() {
        const title = document.getElementById('notificationTitle').value;
        const message = document.getElementById('notificationMessage').value;
        const type = document.getElementById('notificationType').value;
        
        if (title || message) {
            document.getElementById('previewTitle').textContent = title || 'Notification Title';
            document.getElementById('previewMessage').textContent = message || 'Notification message';
            document.getElementById('previewType').textContent = type;
            preview.style.display = 'block';
        } else {
            preview.style.display = 'none';
        }
    }
    
    // Add event listeners for preview
    document.getElementById('notificationTitle').addEventListener('input', updatePreview);
    document.getElementById('notificationMessage').addEventListener('input', updatePreview);
    document.getElementById('notificationType').addEventListener('change', updatePreview);
    
    // Handle form submission
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const userId = document.getElementById('userId').value;
        const title = document.getElementById('notificationTitle').value;
        const message = document.getElementById('notificationMessage').value;
        const type = document.getElementById('notificationType').value;
        
        try {
            const response = await fetch('/admin/send-notification', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    user_id: userId,
                    title: title,
                    message: message,
                    type: type
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                showAlert('Notification sent successfully!', 'success');
                form.reset();
                preview.style.display = 'none';
            } else {
                showAlert('Error: ' + data.error, 'danger');
            }
        } catch (error) {
            showAlert('Network error: ' + error.message, 'danger');
        }
    });
    
    function showAlert(message, type) {
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-admin`;
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close float-end" onclick="this.parentElement.remove()"></button>
        `;
        alertsContainer.appendChild(alert);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 5000);
    }
    
    // Make showAlert globally available
    window.showAlert = showAlert;
});

// Quick action functions
function sendTestNotification() {
    document.getElementById('userId').value = '{{ session.get("user_id") }}';
    document.getElementById('notificationTitle').value = 'Test Notification';
    document.getElementById('notificationMessage').value = 'This is a test notification from the admin panel.';
    document.getElementById('notificationType').value = 'info';
    
    // Trigger preview update
    document.getElementById('notificationTitle').dispatchEvent(new Event('input'));
}

function fillSampleData() {
    document.getElementById('userId').value = '{{ session.get("user_id") }}';
    document.getElementById('notificationTitle').value = 'Welcome to ryzuo Bot!';
    document.getElementById('notificationMessage').value = 'Thank you for using our Discord bot. We hope you enjoy all the features!';
    document.getElementById('notificationType').value = 'success';
    
    // Trigger preview update
    document.getElementById('notificationTitle').dispatchEvent(new Event('input'));
}
</script>
{% endblock %}