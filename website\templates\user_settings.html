{% extends "base.html" %}
{% block title %}User Settings - ry<PERSON>o{% endblock %}

{% block styles %}
<style>
    .user-settings-header {
        background: linear-gradient(135deg, rgba(88, 101, 242, 0.1) 0%, rgba(0, 212, 255, 0.1) 100%);
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(20px);
        border: 1px solid var(--border-color);
    }

    .user-settings-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    }

    .settings-title {
        font-size: 2rem;
        font-weight: 700;
        background: linear-gradient(135deg, var(--text-primary), var(--primary-color));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .settings-subtitle {
        color: var(--text-secondary);
        font-size: 1.1rem;
        margin: 0;
    }

    @media (max-width: 768px) {
        .user-settings-header {
            padding: 1.5rem;
        }

        .settings-title {
            font-size: 1.5rem;
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid" style="padding-top: 2rem;">
    <div class="row">
        <div class="col-12">
            <div class="user-settings-header">
                <div class="d-flex justify-content-between align-items-start flex-wrap gap-3">
                    <div>
                        <h1 class="settings-title">
                            <i class="fas fa-user-cog text-primary"></i>
                            <span>User Settings</span>
                        </h1>
                        <p class="settings-subtitle">Manage your account and preferences</p>
                    </div>
                    <a href="{{ url_for('select_server') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Servers
                    </a>
                </div>
            </div>

            <!-- User Profile Card -->
            <div class="row g-4 mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-user me-2"></i>Profile Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-md-2 text-center">
                                    {% if discord_user.avatar %}
                                    <img src="https://cdn.discordapp.com/avatars/{{ discord_user.id }}/{{ discord_user.avatar }}.png?size=128"
                                         alt="User Avatar"
                                         class="rounded-circle mb-3"
                                         style="width: 80px; height: 80px; object-fit: cover;">
                                    {% else %}
                                    <div class="d-flex align-items-center justify-content-center bg-secondary rounded-circle mx-auto mb-3"
                                         style="width: 80px; height: 80px;">
                                        <i class="fas fa-user fa-2x text-white"></i>
                                    </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-10">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6 class="mb-2"><i class="fas fa-user me-2 text-primary"></i>Username</h6>
                                            <p class="mb-3">{{ discord_user.username }}</p>

                                            <h6 class="mb-2"><i class="fas fa-id-card me-2 text-info"></i>Discord ID</h6>
                                            <p class="mb-3"><code>{{ discord_user.id }}</code></p>

                                            {% if discord_user.email %}
                                            <h6 class="mb-2"><i class="fas fa-envelope me-2 text-secondary"></i>Email</h6>
                                            <p class="mb-3">{{ discord_user.email }}</p>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6">
                                            <h6 class="mb-2"><i class="fas fa-crown me-2 text-warning"></i>Subscription</h6>
                                            <p class="mb-3">
                                                {% if user_subscription %}
                                                    <span class="badge bg-success">{{ user_subscription.subscription_tier|title }} Plan</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">No Active Subscription</span>
                                                {% endif %}
                                            </p>

                                            <h6 class="mb-2"><i class="fas fa-server me-2 text-success"></i>Owned Servers</h6>
                                            <p class="mb-3">{{ user_owned_servers|length }} server(s) you own</p>

                                            <h6 class="mb-2"><i class="fas fa-clock me-2 text-muted"></i>Account Status</h6>
                                            <p class="mb-3"><span class="badge bg-success">Active</span></p>
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <div class="alert alert-info mb-0">
                                            <i class="fas fa-info-circle me-2"></i>
                                            Profile information is synced from your Discord account. Premium features work on servers you own with an active subscription.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Subscription Section -->
            <div class="row g-4 mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-crown me-2 text-warning"></i>Ryzuo Premium Subscription</h5>
                        </div>
                        <div class="card-body">
                            {% if user_subscription %}
                            <!-- Active Subscription -->
                            <div class="row g-3">
                                <div class="col-md-8">
                                    <div class="subscription-status-card p-4 border rounded">
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="subscription-icon me-3">
                                                <i class="fas fa-crown fa-2x text-warning"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-1">{{ user_subscription.subscription_tier|title }} Plan</h6>
                                                <span class="badge bg-success">{{ user_subscription.status|title }}</span>
                                            </div>
                                        </div>
                                        <div class="subscription-details">
                                            <div class="row">
                                                <div class="col-sm-6">
                                                    <small class="text-muted">Started:</small>
                                                    <div>{{ user_subscription.created_at.strftime('%B %d, %Y') if user_subscription.created_at else 'Unknown' }}</div>
                                                </div>
                                                <div class="col-sm-6">
                                                    <small class="text-muted">Status:</small>
                                                    <div>
                                                        {% if user_subscription.status == 'active' %}
                                                        <span class="text-success">Active & Working</span>
                                                        {% elif user_subscription.status == 'past_due' %}
                                                        <span class="text-warning">Payment Issue</span>
                                                        {% else %}
                                                        <span class="text-danger">{{ user_subscription.status|title }}</span>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="subscription-actions">
                                        <h6 class="mb-3">Manage Subscription</h6>
                                        <div class="d-grid gap-2">
                                            <button class="btn btn-outline-primary" onclick="manageSubscription()">
                                                <i class="fas fa-cog me-2"></i>Manage Billing
                                            </button>
                                            <a href="{{ url_for('select_server') }}" class="btn btn-primary">
                                                <i class="fas fa-server me-2"></i>Configure Servers
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Owned Servers List -->
                            <div class="mt-4">
                                <h6 class="mb-3">
                                    <i class="fas fa-server me-2"></i>Your Servers (Premium Enabled)
                                </h6>
                                <div id="ownedServersList">
                                    {% if user_owned_servers %}
                                        <div class="row g-3">
                                            {% for server in user_owned_servers %}
                                            <div class="col-md-6">
                                                <div class="server-item p-3 border rounded">
                                                    <div class="d-flex align-items-center justify-content-between">
                                                        <div class="d-flex align-items-center">
                                                            {% if server.icon %}
                                                            <img src="https://cdn.discordapp.com/icons/{{ server.id }}/{{ server.icon }}.png?size=64"
                                                                 alt="Server Icon" class="rounded me-3" style="width: 40px; height: 40px;">
                                                            {% else %}
                                                            <div class="bg-secondary rounded me-3 d-flex align-items-center justify-content-center"
                                                                 style="width: 40px; height: 40px;">
                                                                <i class="fas fa-server text-white"></i>
                                                            </div>
                                                            {% endif %}
                                                            <div>
                                                                <div class="fw-bold">{{ server.name }}</div>
                                                                <small class="text-muted">ID: {{ server.id }}</small>
                                                            </div>
                                                        </div>
                                                        <button class="btn btn-outline-primary btn-sm" onclick="goToServerSettings('{{ server.id }}')">
                                                            <i class="fas fa-cog"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            {% endfor %}
                                        </div>
                                    {% else %}
                                        <div class="text-center py-4">
                                            <i class="fas fa-server fa-3x text-muted mb-3"></i>
                                            <p class="text-muted">No owned servers found</p>
                                            <small class="text-muted">You need to own a Discord server to use premium features</small>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            {% else %}
                            <!-- No Subscription -->
                            <div class="text-center py-5">
                                <div class="subscription-cta">
                                    <i class="fas fa-crown fa-4x text-muted mb-4"></i>
                                    <h4 class="mb-3">No Active Subscription</h4>
                                    <p class="text-muted mb-4">Subscribe to Ryzuo Premium to unlock all features for unlimited servers you own.</p>
                                    <div class="row justify-content-center">
                                        <div class="col-md-8">
                                            <div class="features-preview mb-4">
                                                <div class="row g-3">
                                                    <div class="col-6 col-md-3">
                                                        <div class="feature-preview text-center">
                                                            <i class="fas fa-robot fa-2x text-primary mb-2"></i>
                                                            <small>Auto-Role</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-6 col-md-3">
                                                        <div class="feature-preview text-center">
                                                            <i class="fas fa-music fa-2x text-primary mb-2"></i>
                                                            <small>Music Bot</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-6 col-md-3">
                                                        <div class="feature-preview text-center">
                                                            <i class="fas fa-microphone fa-2x text-primary mb-2"></i>
                                                            <small>Temp Voice</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-6 col-md-3">
                                                        <div class="feature-preview text-center">
                                                            <i class="fas fa-gift fa-2x text-primary mb-2"></i>
                                                            <small>Giveaways</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <a href="{{ url_for('shop') }}" class="btn btn-primary btn-lg">
                                                <i class="fas fa-crown me-2"></i>Get Ryzuo Premium
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Account Actions -->
            <div class="row g-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>Account Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{{ url_for('select_server') }}" class="btn btn-primary">
                                    <i class="fas fa-server me-2"></i>Manage Servers
                                </a>
                                <a href="{{ url_for('logout') }}" class="btn btn-outline-danger">
                                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>About</h5>
                        </div>
                        <div class="card-body">
                            <p class="mb-2"><strong>ryzuo Bot Dashboard</strong></p>
                            <p class="text-muted mb-0">Manage your Discord bot settings and configurations.</p>
                            <hr>
                            <small class="text-muted">
                                <i class="fas fa-shield-alt me-1"></i>
                                Your data is secure and only used for bot functionality.
                            </small>
                        </div>
                    </div>
                </div>
        </div>
    </div>
</div>

<style>
.card {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.3);
}

.card-header {
    background-color: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
    color: var(--text-primary);
}

.card-body {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

.list-group-item {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

.alert-info {
    background-color: rgba(59, 130, 246, 0.1);
    border-color: var(--info-color);
    color: var(--text-primary);
}

.alert-warning {
    background-color: rgba(245, 158, 11, 0.1);
    border-color: var(--warning-color);
    color: var(--text-primary);
}

.text-muted {
    color: var(--text-secondary) !important;
}

.btn {
    transition: all 0.15s ease-in-out;
}

.license-key-item {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid var(--border-color) !important;
    transition: all 0.3s ease;
}

.license-key-item:hover {
    background: rgba(255, 255, 255, 0.05);
    transform: translateY(-2px);
}
</style>

<script>
// Load user data when page loads
document.addEventListener('DOMContentLoaded', function() {
    loadUserData();
});

async function loadUserData() {
    try {
        // Load owned servers if user has subscription
        const response = await fetch('/api/user-owned-servers');
        const data = await response.json();

        if (response.ok && data.success) {
            updateOwnedServersList(data.servers || []);
        } else {
            console.error('Failed to load owned servers:', data.error || 'Unknown error');
        }
    } catch (error) {
        console.error('Error loading user data:', error);
    }
}

function updateOwnedServersList(servers) {
    const serversList = document.getElementById('ownedServersList');
    if (!serversList) return;

    if (servers.length === 0) {
        serversList.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-server fa-3x text-muted mb-3"></i>
                <p class="text-muted">No owned servers found</p>
                <small class="text-muted">You need to own a Discord server to use premium features</small>
            </div>
        `;
        return;
    }

    const serversHtml = servers.map(server => `
        <div class="col-md-6">
            <div class="server-item p-3 border rounded">
                <div class="d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        ${server.icon ?
                            `<img src="https://cdn.discordapp.com/icons/${server.id}/${server.icon}.png?size=64"
                                 alt="Server Icon" class="rounded me-3" style="width: 40px; height: 40px;">` :
                            `<div class="bg-secondary rounded me-3 d-flex align-items-center justify-content-center"
                                 style="width: 40px; height: 40px;">
                                <i class="fas fa-server text-white"></i>
                            </div>`
                        }
                        <div>
                            <div class="fw-bold">${server.name}</div>
                            <small class="text-muted">ID: ${server.id}</small>
                        </div>
                    </div>
                    <button class="btn btn-outline-primary btn-sm" onclick="goToServerSettings('${server.id}')">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>
            </div>
        </div>
    `).join('');

    serversList.innerHTML = `<div class="row g-3">${serversHtml}</div>`;
}

function manageSubscription() {
    // This would typically redirect to Stripe customer portal
    // For now, show an alert with instructions
    alert('Subscription management will redirect to Stripe customer portal. This feature will be implemented with actual Stripe integration.');

    // In production, this would be:
    // window.open('https://billing.stripe.com/p/login/...', '_blank');
}



function getCsrfToken() {
    return document.querySelector('meta[name=csrf-token]')?.getAttribute('content') || '';
}

function goToServerSettings(serverId) {
    // Store the server ID in session and redirect to server settings
    fetch('/select-server/' + serverId, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Redirect to server settings page
            window.location.href = '/settings';
        } else {
            alert('Error: ' + (data.error || 'Failed to select server'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while selecting the server');
    });
}
</script>

{% endblock %}
