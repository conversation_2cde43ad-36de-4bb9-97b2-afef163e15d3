"""
Views for Temporary Voice Channels System
Contains Discord UI components for temp voice management
"""

import discord
from discord import ui, ButtonStyle, Interaction
from discord.ext import commands
import logging
from database import DatabaseManager

logger = logging.getLogger(__name__)

class TempVoiceView(ui.View):
    """Persistent view for TempVoice channel management"""
    
    def __init__(self, db: DatabaseManager):
        super().__init__(timeout=None)
        self.db = db

    @ui.button(
        emoji="🎤",
        label="Create Voice Channel",
        style=ButtonStyle.primary,
        custom_id="tempvoice_create_channel"
    )
    async def create_channel(self, interaction: Interaction, button: ui.Button):
        """Create a temporary voice channel"""
        try:
            # Check if server has a license
            if not self.db.is_server_licensed(interaction.guild.id):
                await interaction.response.send_message("❌ This server does not have a valid license.", ephemeral=True)
                return
                
            # Check if tempvoice system is enabled
            if not self.db.is_system_enabled(interaction.guild.id, "tempvoice"):
                await interaction.response.send_message("❌ The TempVoice system is currently disabled on this server.", ephemeral=True)
                return

            # Check if user already has a temp channel
            existing_channel = self.db.get_user_temp_channel(interaction.guild.id, interaction.user.id)
            if existing_channel:
                channel = interaction.guild.get_channel(existing_channel['channel_id'])
                if channel:
                    await interaction.response.send_message(f"❌ You already have a temporary voice channel: {channel.mention}", ephemeral=True)
                    return
                else:
                    # Channel was deleted but not cleaned up in database
                    self.db.delete_temp_channel(existing_channel['channel_id'])

            await interaction.response.defer(ephemeral=True)

            # Get TempVoice settings
            settings = self.db.get_tempvoice_settings(interaction.guild.id)
            if not settings:
                await interaction.followup.send("❌ TempVoice system is not configured for this server.", ephemeral=True)
                return

            # Get the creator channel to copy permissions from
            creator_channel = interaction.guild.get_channel(settings['creator_channel_id'])
            if not creator_channel or not isinstance(creator_channel, discord.VoiceChannel):
                await interaction.followup.send("❌ Creator voice channel not found or invalid.", ephemeral=True)
                return

            await interaction.followup.send(f"💡 **Tip:** Join {creator_channel.mention} to automatically create your temporary voice channel!\n\nAlternatively, I can create one for you now if you prefer.", ephemeral=True)

            # Create the temporary voice channel as fallback
            try:
                # Copy permissions from creator channel
                overwrites = creator_channel.overwrites.copy()

                # Ensure @everyone permissions are copied from creator channel
                if interaction.guild.default_role in creator_channel.overwrites:
                    overwrites[interaction.guild.default_role] = creator_channel.overwrites[interaction.guild.default_role]

                # Get default user limit
                default_limit = settings.get('default_user_limit')

                channel_name = f"{interaction.user.display_name}'s Channel"
                temp_channel = await interaction.guild.create_voice_channel(
                    name=channel_name[:100],  # Ensure channel name is within Discord's limit
                    category=creator_channel.category,
                    overwrites=overwrites,
                    user_limit=default_limit  # Apply default user limit
                )

                # Save to database
                success = self.db.create_temp_channel(interaction.guild_id, interaction.user.id, temp_channel.id)
                if not success:
                    await temp_channel.delete(reason="Failed to save to database")
                    await interaction.followup.send("❌ Failed to create temporary channel.", ephemeral=True)
                    return

                # Move user to their channel if they're in voice
                try:
                    if interaction.user.voice:
                        await interaction.user.move_to(temp_channel)
                except discord.HTTPException:
                    pass  # User might not be in a voice channel

                await interaction.followup.send(f"✅ Created your temporary voice channel: {temp_channel.mention}", ephemeral=True)

            except Exception as e:
                logger.error(f"Error creating temp channel: {e}")
                await interaction.followup.send("❌ Failed to create temporary voice channel. Please check bot permissions.", ephemeral=True)

        except Exception as e:
            logger.error(f"Error in create_channel: {e}")
            if not interaction.response.is_done():
                await interaction.response.send_message("❌ An error occurred while creating the channel.", ephemeral=True)

    @ui.button(
        emoji="⚙️",
        label="Manage Channel",
        style=ButtonStyle.secondary,
        custom_id="tempvoice_manage_channel"
    )
    async def manage_channel(self, interaction: Interaction, button: ui.Button):
        """Manage temporary voice channel"""
        try:
            # Check if user has a temp channel
            temp_channel_data = self.db.get_user_temp_channel(interaction.guild.id, interaction.user.id)
            if not temp_channel_data:
                await interaction.response.send_message("❌ You don't have a temporary voice channel.", ephemeral=True)
                return

            # Check if channel still exists
            channel = interaction.guild.get_channel(temp_channel_data['channel_id'])
            if not channel:
                # Channel was deleted, clean up database
                self.db.delete_temp_channel(temp_channel_data['channel_id'])
                await interaction.response.send_message("❌ Your temporary voice channel no longer exists.", ephemeral=True)
                return

            # Create management view
            view = TempVoiceManagementView(self.db, temp_channel_data['channel_id'])
            await interaction.response.send_message(f"🎤 **Managing:** {channel.mention}\n\nUse the buttons below to manage your temporary voice channel:", view=view, ephemeral=True)

        except Exception as e:
            logger.error(f"Error in manage_channel: {e}")
            await interaction.response.send_message("❌ An error occurred while accessing channel management.", ephemeral=True)


class TempVoiceManagementView(ui.View):
    """View for managing temporary voice channels"""
    
    def __init__(self, db: DatabaseManager, channel_id: int):
        super().__init__(timeout=300)  # 5 minute timeout
        self.db = db
        self.channel_id = channel_id

    @ui.button(
        emoji="👢",
        style=ButtonStyle.secondary,
        custom_id="tempvoice_kick_user"
    )
    async def kick_user(self, interaction: Interaction, button: ui.Button):
        """Kick a user from the voice channel"""
        # Verify ownership
        temp_channel_data = self.db.get_temp_channel(self.channel_id)
        if not temp_channel_data or temp_channel_data['owner_id'] != interaction.user.id:
            await interaction.response.send_message("❌ You don't own this temporary voice channel.", ephemeral=True)
            return

        # Show user select menu
        view = UserSelectView(self.db, temp_channel_data['channel_id'], "kick")
        await interaction.response.send_message("Select a user to kick from your voice channel:", view=view, ephemeral=True)

    @ui.button(
        emoji="⛔",
        style=ButtonStyle.secondary,
        custom_id="tempvoice_block_user"
    )
    async def block_user(self, interaction: Interaction, button: ui.Button):
        """Block a user from the voice channel"""
        # Verify ownership
        temp_channel_data = self.db.get_temp_channel(self.channel_id)
        if not temp_channel_data or temp_channel_data['owner_id'] != interaction.user.id:
            await interaction.response.send_message("❌ You don't own this temporary voice channel.", ephemeral=True)
            return

        # Show user select menu
        view = UserSelectView(self.db, temp_channel_data['channel_id'], "block")
        await interaction.response.send_message("Select a user to block from your voice channel:", view=view, ephemeral=True)

    @ui.button(
        emoji="🔓",
        style=ButtonStyle.secondary,
        custom_id="tempvoice_unblock_user"
    )
    async def unblock_user(self, interaction: Interaction, button: ui.Button):
        """Unblock a user from the voice channel"""
        # Verify ownership
        temp_channel_data = self.db.get_temp_channel(self.channel_id)
        if not temp_channel_data or temp_channel_data['owner_id'] != interaction.user.id:
            await interaction.response.send_message("❌ You don't own this temporary voice channel.", ephemeral=True)
            return

        # Show user select menu
        view = UserSelectView(self.db, temp_channel_data['channel_id'], "unblock")
        await interaction.response.send_message("Select a user to unblock from your voice channel:", view=view, ephemeral=True)

    @ui.button(
        emoji="🗑️",
        style=ButtonStyle.danger,
        custom_id="tempvoice_delete_channel"
    )
    async def delete_channel(self, interaction: Interaction, button: ui.Button):
        """Delete the temporary voice channel"""
        try:
            # Verify ownership
            temp_channel_data = self.db.get_temp_channel(self.channel_id)
            if not temp_channel_data or temp_channel_data['owner_id'] != interaction.user.id:
                await interaction.response.send_message("❌ You don't own this temporary voice channel.", ephemeral=True)
                return

            # Get the channel
            channel = interaction.guild.get_channel(self.channel_id)
            if not channel:
                # Channel already deleted, clean up database
                self.db.delete_temp_channel(self.channel_id)
                await interaction.response.send_message("❌ Channel no longer exists.", ephemeral=True)
                return

            # Delete the channel
            await channel.delete(reason=f"Deleted by owner {interaction.user}")
            self.db.delete_temp_channel(self.channel_id)
            
            await interaction.response.send_message("✅ Your temporary voice channel has been deleted.", ephemeral=True)

        except Exception as e:
            logger.error(f"Error deleting temp channel: {e}")
            await interaction.response.send_message("❌ Failed to delete the channel.", ephemeral=True)


class UserSelectView(ui.View):
    """View for selecting users for temp voice actions"""
    
    def __init__(self, db: DatabaseManager, channel_id: int, action: str):
        super().__init__(timeout=60)
        self.db = db
        self.channel_id = channel_id
        self.action = action

        # Add the user select component
        user_select = ui.UserSelect(
            placeholder="Select a user",
            min_values=1,
            max_values=1,
            custom_id=f"{action}_user_select"
        )
        user_select.callback = self.user_select_callback
        self.add_item(user_select)

    async def user_select_callback(self, interaction: Interaction):
        """Handle user selection"""
        try:
            selected_user = interaction.data['values'][0]
            user = interaction.guild.get_member(int(selected_user))
            
            if not user:
                await interaction.response.send_message("❌ User not found.", ephemeral=True)
                return

            # Get channel
            channel = interaction.guild.get_channel(self.channel_id)
            if not channel:
                await interaction.response.send_message("❌ Channel no longer exists.", ephemeral=True)
                return

            if self.action == "kick":
                if user in channel.members:
                    try:
                        await user.move_to(None)
                        await interaction.response.send_message(f"✅ Kicked {user.mention} from your voice channel.", ephemeral=True)
                    except Exception as e:
                        await interaction.response.send_message(f"❌ Failed to kick {user.mention}.", ephemeral=True)
                else:
                    await interaction.response.send_message(f"❌ {user.mention} is not in your voice channel.", ephemeral=True)
                    
            elif self.action == "block":
                success = self.db.block_user_from_temp_channel(self.channel_id, user.id)
                if success:
                    # Also kick them if they're currently in the channel
                    if user in channel.members:
                        try:
                            await user.move_to(None)
                        except:
                            pass
                    await interaction.response.send_message(f"✅ Blocked {user.mention} from your voice channel.", ephemeral=True)
                else:
                    await interaction.response.send_message(f"❌ Failed to block {user.mention}.", ephemeral=True)
                    
            elif self.action == "unblock":
                success = self.db.unblock_user_from_temp_channel(self.channel_id, user.id)
                if success:
                    await interaction.response.send_message(f"✅ Unblocked {user.mention} from your voice channel.", ephemeral=True)
                else:
                    await interaction.response.send_message(f"❌ Failed to unblock {user.mention}.", ephemeral=True)

        except Exception as e:
            logger.error(f"Error in user select callback: {e}")
            await interaction.response.send_message("❌ An error occurred.", ephemeral=True)
