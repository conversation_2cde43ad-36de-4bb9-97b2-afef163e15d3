﻿{% extends "base.html" %}

{% block title %}Configure DM Support - {{ server_info.name }}{% endblock %}

{% block styles %}
<style>
    .config-header {
        background: linear-gradient(135deg, rgba(88, 101, 242, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(20px);
        border: 1px solid var(--border-color);
    }

    .config-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #5865f2, #8b5cf6);
    }

    .config-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .config-subtitle {
        color: var(--text-secondary);
        font-size: 1.1rem;
        margin-bottom: 0;
        opacity: 0.9;
    }

    .feature-card {
        background: rgba(255, 255, 255, 0.03);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        backdrop-filter: blur(20px);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        position: relative;
    }

    .feature-card:hover {
        transform: translateY(-5px);
        border-color: #5865f2;
        box-shadow: 0 15px 40px rgba(88, 101, 242, 0.15);
    }

    .feature-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #5865f2, #8b5cf6);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .feature-card:hover::before {
        opacity: 1;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mt-3">
    <!-- Sidebar -->
    <div class="col-md-3">
        {% include 'sidebar.html' %}
    </div>

    <!-- Main Content -->
    <div class="col-md-9">
        <!-- Configuration Header -->
        <div class="config-header">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <h1 class="config-title">
                        <i class="fas fa-ticket-alt text-primary"></i>
                        DM Support Configuration
                    </h1>
                    <p class="config-subtitle">Configure direct message support tickets for your server</p>
                </div>
                <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                </a>
            </div>
        </div>



        <div class="row">
            <div class="col-lg-8">
                <div class="feature-card">
                    <div class="card-header">
                        <h5 class="mb-0">Configure DM-Based Support System</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                            <div class="mb-3">
                                <label for="category_id" class="form-label">Support Category</label>
                                <select class="form-select" id="category_id" name="category_id" required
                                        {% if not config or not config.log_channel_id %}disabled{% endif %}>
                                    <option value="">Select a category...</option>
                                </select>
                                <div class="form-text">
                                    Category where support ticket channels will be created
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="support_role_id" class="form-label">Support Role</label>
                                <select class="form-select" id="support_role_id" name="support_role_id" required
                                        {% if not config or not config.log_channel_id %}disabled{% endif %}>
                                    <option value="">Select a role...</option>
                                </select>
                                <div class="form-text">
                                    Role that can respond to and manage support tickets
                                </div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary" 
                                        {% if not config or not config.log_channel_id %}disabled{% endif %}>
                                    <i class="fas fa-save me-2"></i>Save Configuration
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                {% if dm_support_settings %}
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">Current Configuration</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Category ID:</strong>
                                <br><code>{{ dm_support_settings.category_id }}</code>
                            </div>
                            <div class="col-md-6">
                                <strong>Support Role ID:</strong>
                                <br><code>{{ dm_support_settings.support_role_id }}</code>
                            </div>
                        </div>
                        {% if config and config.log_channel_id %}
                        <hr>
                        <div class="row">
                            <div class="col-md-12">
                                <strong>Log Channel ID:</strong>
                                <br><code>{{ config.log_channel_id }}</code>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}
            </div>

            <div class="col-lg-4">
                <div class="feature-card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>How It Works</h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-2">The DM support system:</p>
                        <ul class="mb-0">
                            <li>Users DM the bot to create tickets</li>
                            <li>Bot creates private channels in the category</li>
                            <li>Support team responds via the channel</li>
                            <li>Messages are forwarded to user DMs</li>
                            <li>One ticket per user globally</li>
                            <li>Automatic transcript logging</li>
                        </ul>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-cogs me-2"></i>Features</h6>
                    </div>
                    <div class="card-body">
                        <ul class="mb-0">
                            <li>Server selection for multi-server setups</li>
                            <li>Confirmation flow for ticket creation</li>
                            <li>Admin responses via embeds</li>
                            <li>Automatic cleanup when resolved</li>
                            <li>Complete transcript logging</li>
                            <li>Close tickets with <code>=close reason</code></li>
                        </ul>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-shield-alt me-2"></i>Requirements</h6>
                    </div>
                    <div class="card-body">
                        <ul class="mb-0">
                            <li>Log channel is recommended for moderation</li>
                            <li>Bot needs manage channels permission</li>
                            <li>Support role should have appropriate permissions</li>
                            <li>Category should have proper permissions</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Load Discord data for dropdowns
document.addEventListener('DOMContentLoaded', function() {
    loadCategories();
    loadRoles();
});

function loadCategories() {
    fetch('{{ url_for('api_channels') }}')
        .then(response => response.json())
        .then(channels => {
            const categorySelect = document.getElementById('category_id');
            categorySelect.innerHTML = '<option value="">Select a category...</option>';
            
            // Filter to category channels only
            const categories = channels.filter(channel => channel.type === 'category');
            
            categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                
                // Select current category if editing
                {% if dm_support_settings and dm_support_settings.category_id %}
                if (category.id === '{{ dm_support_settings.category_id }}') {
                    option.selected = true;
                }
                {% endif %}
                
                categorySelect.appendChild(option);
            });
        })
        .catch(error => {
            console.error('Error loading categories:', error);
        });
}

function loadRoles() {
    fetch('{{ url_for('api_roles') }}')
        .then(response => response.json())
        .then(roles => {
            const roleSelect = document.getElementById('support_role_id');
            roleSelect.innerHTML = '<option value="">Select a role...</option>';
            
            roles.forEach(role => {
                const option = document.createElement('option');
                option.value = role.id;
                option.textContent = role.name;
                
                // Select current role if editing
                {% if dm_support_settings and dm_support_settings.support_role_id %}
                if (role.id === '{{ dm_support_settings.support_role_id }}') {
                    option.selected = true;
                }
                {% endif %}
                
                roleSelect.appendChild(option);
            });
        })
        .catch(error => {
            console.error('Error loading roles:', error);
        });
}
</script>
{% endblock %}
