{% extends "base.html" %}

{% block title %}Logs Configuration - {{ server_info.name }}{% endblock %}

{% block styles %}
<style>
    .config-header {
        background: linear-gradient(135deg, rgba(6, 182, 212, 0.1) 0%, rgba(14, 165, 233, 0.1) 100%);
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(20px);
        border: 1px solid var(--border-color);
    }

    .config-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #06b6d4, #0ea5e9);
    }

    .config-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .config-subtitle {
        color: var(--text-secondary);
        font-size: 1.1rem;
        margin-bottom: 0;
        opacity: 0.9;
    }

    .feature-card {
        background: rgba(255, 255, 255, 0.03);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        backdrop-filter: blur(20px);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        position: relative;
    }

    .feature-card:hover {
        transform: translateY(-5px);
        border-color: #06b6d4;
        box-shadow: 0 15px 40px rgba(6, 182, 212, 0.15);
    }

    .feature-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #06b6d4, #0ea5e9);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .feature-card:hover::before {
        opacity: 1;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mt-3">
    <!-- Sidebar -->
    <div class="col-md-3">
        {% include 'sidebar.html' %}
    </div>

    <!-- Main Content -->
    <div class="col-md-9">
        <!-- Configuration Header -->
        <div class="config-header">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <h1 class="config-title">
                        <i class="fas fa-clipboard-list text-info"></i>
                        Logs Configuration
                    </h1>
                    <p class="config-subtitle">Configure logging settings for your server</p>
                </div>
                <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                </a>
            </div>
        </div>

        <!-- Global Logging Configuration -->
        <div class="feature-card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-cog me-2"></i>Global Logging Settings</h6>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('configure_logs') }}">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <input type="hidden" name="action" value="set_global_settings">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="enable_dashboard_logging"
                                       name="enable_dashboard_logging" {% if logging_config.get('dashboard_enabled', True) %}checked{% endif %}>
                                <label class="form-check-label" for="enable_dashboard_logging">
                                    <strong>Enable Dashboard Logging</strong>
                                </label>
                            </div>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                Log events to the dashboard for viewing in the web interface
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="enable_discord_logging"
                                       name="enable_discord_logging" {% if logging_config.get('discord_enabled', False) %}checked{% endif %}>
                                <label class="form-check-label" for="enable_discord_logging">
                                    <strong>Enable Discord Channel Logging</strong>
                                </label>
                            </div>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                Send log messages to a Discord channel
                            </div>
                        </div>
                        <div class="col-12" id="discord_channel_section" style="{% if not logging_config.get('discord_enabled', False) %}display: none;{% endif %}">
                            <label for="log_channel_id" class="form-label">Discord Log Channel</label>
                            <select class="form-select" id="log_channel_id" name="log_channel_id">
                                <option value="">Select a channel...</option>
                            </select>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                Choose which Discord channel will receive log messages
                            </div>
                        </div>
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Save Global Settings
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Migration Info Card -->
        <div class="card mb-4 border-info">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Enhanced Logging Available</h6>
            </div>
            <div class="card-body">
                <p class="mb-3">
                    <strong>New comprehensive Discord event logging is now available!</strong>
                    The bot can now log channel updates, role changes, permission modifications, and much more.
                </p>
                <p class="mb-3">
                    If you're seeing mostly empty logs, it's likely because many event types are disabled by default.
                    Click <strong>"Enable New Events"</strong> below to automatically enable the most commonly used Discord events
                    while preserving your existing settings.
                </p>
                <div class="alert alert-light mb-0">
                    <small>
                        <i class="fas fa-lightbulb me-1"></i>
                        <strong>What gets enabled:</strong> Server updates, channel changes, role modifications, member events,
                        permission changes, message edits/deletes, invites, emojis, stickers, scheduled events, and more.
                    </small>
                </div>
            </div>
        </div>

        <!-- Logging Configuration Form -->
        <form method="POST" action="{{ url_for('configure_logs') }}">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
            <input type="hidden" name="action" value="update_config">
            
            <!-- Bot Activity Logs -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-robot me-2"></i>Bot Activity Logs</h6>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        {% set bot_logs = [
                            ('dashboard_updates', 'Dashboard Updates', 'Configuration changes made through the web dashboard'),
                            ('license_key_updates', 'License Key Updates', 'License key redemptions, transfers, and status changes'),
                            ('moderator_logs', 'Moderator Logs', 'Moderation actions performed by staff members'),
                            ('ryzuo_logs', 'ryzuo Logs', 'General bot activity and system events'),
                            ('vent_logs', 'Vent Logs', 'Anonymous vent messages and related activity'),
                            ('ticket_logs', 'Ticket Logs', 'DM support ticket creation, responses, and closures'),
                            ('sticky_created', 'Sticky Messages Created', 'When sticky messages are created'),
                            ('sticky_deleted', 'Sticky Messages Deleted', 'When sticky messages are removed')
                        ] %}
                        
                        {% for log_key, log_name, log_desc in bot_logs %}
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="card-title mb-0">{{ log_name }}</h6>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" 
                                                   id="enabled_{{ log_key }}" name="enabled_{{ log_key }}"
                                                   {% if logging_config.get('log_types', {}).get(log_key, {}).get('enabled', False) %}checked{% endif %}>
                                        </div>
                                    </div>
                                    <p class="card-text small text-muted mb-3">{{ log_desc }}</p>
                                    
                                    <div class="log-options" id="options_{{ log_key }}" 
                                         style="{% if not logging_config.get('log_types', {}).get(log_key, {}).get('enabled', False) %}display: none;{% endif %}">
                                        <div class="row g-2">
                                            <div class="col-6">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" 
                                                           id="dashboard_{{ log_key }}" name="dashboard_{{ log_key }}"
                                                           {% if logging_config.get('log_types', {}).get(log_key, {}).get('dashboard', False) %}checked{% endif %}>
                                                    <label class="form-check-label small" for="dashboard_{{ log_key }}">
                                                        Dashboard
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" 
                                                           id="discord_{{ log_key }}" name="discord_{{ log_key }}"
                                                           {% if logging_config.get('log_types', {}).get(log_key, {}).get('discord', False) %}checked{% endif %}>
                                                    <label class="form-check-label small" for="discord_{{ log_key }}">
                                                        Discord
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-12">
                                                <label for="color_{{ log_key }}" class="form-label small">Color</label>
                                                <input type="color" class="form-control form-control-color" 
                                                       id="color_{{ log_key }}" name="color_{{ log_key }}"
                                                       value="{{ logging_config.get('log_types', {}).get(log_key, {}).get('color', '#007bff') }}">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- Discord Server Events -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fab fa-discord me-2"></i>Discord Server Events</h6>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        {% set discord_events = [
                            ('guild_update', 'Guild Update', 'Server settings changes'),
                            ('channel_create', 'Channel Create', 'New channels created'),
                            ('channel_update', 'Channel Update', 'Channel settings modified'),
                            ('channel_delete', 'Channel Delete', 'Channels deleted'),
                            ('channel_overwrite_create', 'Channel Overwrite Create', 'Permission overwrites added'),
                            ('channel_overwrite_update', 'Channel Overwrite Update', 'Permission overwrites modified'),
                            ('channel_overwrite_delete', 'Channel Overwrite Delete', 'Permission overwrites removed'),
                            ('message_pin', 'Message Pin', 'Messages pinned'),
                            ('message_unpin', 'Message Unpin', 'Messages unpinned'),
                            ('message_delete', 'Message Delete', 'Individual messages deleted'),
                            ('message_delete_bulk', 'Message Delete Bulk', 'Multiple messages deleted at once'),
                            ('message_update', 'Message Update', 'Messages edited'),
                            ('member_join', 'Member Join', 'Members joining the server'),
                            ('member_leave', 'Member Leave', 'Members leaving the server'),
                            ('member_kick', 'Member Kick', 'Members kicked from server'),
                            ('member_prune', 'Member Prune', 'Inactive members pruned'),
                            ('member_ban_add', 'Member Ban Add', 'Members banned'),
                            ('member_ban_remove', 'Member Ban Remove', 'Members unbanned'),
                            ('member_update', 'Member Update', 'Member profile changes'),
                            ('member_role_update', 'Member Role Update', 'Member role changes'),
                            ('member_move', 'Member Move', 'Members moved between voice channels'),
                            ('member_disconnect', 'Member Disconnect', 'Members disconnected from voice'),
                            ('bot_add', 'Bot Add', 'Bots added to server'),
                            ('role_create', 'Role Create', 'New roles created'),
                            ('role_update', 'Role Update', 'Role settings modified'),
                            ('role_delete', 'Role Delete', 'Roles deleted'),
                            ('invite_create', 'Invite Create', 'Server invites created'),
                            ('invite_update', 'Invite Update', 'Server invites modified'),
                            ('invite_delete', 'Invite Delete', 'Server invites deleted'),
                            ('webhook_create', 'Webhook Create', 'Webhooks created'),
                            ('webhook_update', 'Webhook Update', 'Webhooks modified'),
                            ('webhook_delete', 'Webhook Delete', 'Webhooks deleted'),
                            ('emoji_create', 'Emoji Create', 'Custom emojis added'),
                            ('emoji_update', 'Emoji Update', 'Custom emojis modified'),
                            ('emoji_delete', 'Emoji Delete', 'Custom emojis removed'),
                            ('sticker_create', 'Sticker Create', 'Custom stickers added'),
                            ('sticker_update', 'Sticker Update', 'Custom stickers modified'),
                            ('sticker_delete', 'Sticker Delete', 'Custom stickers removed'),
                            ('scheduled_event_create', 'Scheduled Event Create', 'Events scheduled'),
                            ('scheduled_event_update', 'Scheduled Event Update', 'Events modified'),
                            ('scheduled_event_delete', 'Scheduled Event Delete', 'Events cancelled'),
                            ('thread_create', 'Thread Create', 'Threads created'),
                            ('thread_update', 'Thread Update', 'Threads modified'),
                            ('thread_delete', 'Thread Delete', 'Threads deleted')
                        ] %}
                        
                        {% for log_key, log_name, log_desc in discord_events %}
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-body p-3">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="card-title mb-0 small">{{ log_name }}</h6>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" 
                                                   id="enabled_{{ log_key }}" name="enabled_{{ log_key }}"
                                                   {% if logging_config.get('log_types', {}).get(log_key, {}).get('enabled', False) %}checked{% endif %}>
                                        </div>
                                    </div>
                                    <p class="card-text small text-muted mb-2">{{ log_desc }}</p>
                                    
                                    <div class="log-options" id="options_{{ log_key }}" 
                                         style="{% if not logging_config.get('log_types', {}).get(log_key, {}).get('enabled', False) %}display: none;{% endif %}">
                                        <div class="row g-1">
                                            <div class="col-6">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" 
                                                           id="dashboard_{{ log_key }}" name="dashboard_{{ log_key }}"
                                                           {% if logging_config.get('log_types', {}).get(log_key, {}).get('dashboard', False) %}checked{% endif %}>
                                                    <label class="form-check-label small" for="dashboard_{{ log_key }}">
                                                        Dashboard
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" 
                                                           id="discord_{{ log_key }}" name="discord_{{ log_key }}"
                                                           {% if logging_config.get('log_types', {}).get(log_key, {}).get('discord', False) %}checked{% endif %}>
                                                    <label class="form-check-label small" for="discord_{{ log_key }}">
                                                        Discord
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-12">
                                                <input type="color" class="form-control form-control-color w-100" 
                                                       id="color_{{ log_key }}" name="color_{{ log_key }}"
                                                       value="{{ logging_config.get('log_types', {}).get(log_key, {}).get('color', '#007bff') }}">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="d-flex justify-content-between mb-4">
                <div>
                    <button type="button" class="btn btn-info me-2" onclick="enableAllLogs()">
                        <i class="fas fa-check-double me-2"></i>Enable All
                    </button>
                    <button type="button" class="btn btn-warning" onclick="migrateToNewDefaults()">
                        <i class="fas fa-sync-alt me-2"></i>Enable New Events
                    </button>
                </div>
                <button type="submit" class="btn btn-success btn-lg">
                    <i class="fas fa-save me-2"></i>Save Logging Configuration
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Load Discord channels for log channel dropdown
document.addEventListener('DOMContentLoaded', function() {
    loadChannels();

    // Add event listeners for enable/disable toggles
    document.querySelectorAll('input[id^="enabled_"]').forEach(function(checkbox) {
        checkbox.addEventListener('change', function() {
            const logKey = this.id.replace('enabled_', '');
            const optionsDiv = document.getElementById('options_' + logKey);
            if (this.checked) {
                optionsDiv.style.display = 'block';
            } else {
                optionsDiv.style.display = 'none';
            }
        });
    });

    // Add event listener for Discord logging toggle
    const discordLoggingToggle = document.getElementById('enable_discord_logging');
    const discordChannelSection = document.getElementById('discord_channel_section');

    if (discordLoggingToggle && discordChannelSection) {
        discordLoggingToggle.addEventListener('change', function() {
            if (this.checked) {
                discordChannelSection.style.display = 'block';
            } else {
                discordChannelSection.style.display = 'none';
            }
        });
    }
});

async function loadChannels() {
    const channelSelect = document.getElementById('log_channel_id');
    
    try {
        const response = await fetch('{{ url_for("api_channels") }}');
        const data = await response.json();

        if (!response.ok || data.fallback) {
            throw new Error(data.message || data.error || 'Failed to load channels');
        }

        // Keep the "No Discord logging" option
        channelSelect.innerHTML = '<option value="">No Discord logging (Dashboard only)</option>';

        // Filter to text channels only
        const textChannels = Array.isArray(data) ? data.filter(channel => {
            return channel.type === 'text' || channel.type === 0 || channel.type === '0';
        }) : [];

        if (textChannels.length === 0) {
            const option = document.createElement('option');
            option.value = '';
            option.textContent = 'No text channels found';
            option.disabled = true;
            channelSelect.appendChild(option);
            return;
        }

        // Group channels by category
        const categorized = {};
        textChannels.forEach(channel => {
            const category = channel.category || 'No Category';
            if (!categorized[category]) {
                categorized[category] = [];
            }
            categorized[category].push(channel);
        });

        // Add channels grouped by category
        Object.keys(categorized).sort().forEach(category => {
            const optgroup = document.createElement('optgroup');
            optgroup.label = category;

            categorized[category].sort((a, b) => a.name.localeCompare(b.name)).forEach(channel => {
                const option = document.createElement('option');
                option.value = channel.id;
                option.textContent = `#${channel.name}`;

                // Select current channel if set
                {% if logging_config and logging_config.get('log_channel_id') %}
                if (channel.id === '{{ logging_config.get('log_channel_id') }}') {
                    option.selected = true;
                }
                {% endif %}

                optgroup.appendChild(option);
            });

            channelSelect.appendChild(optgroup);
        });

    } catch (error) {
        console.error('Error loading channels:', error);
        
        // Fallback to text input with current value
        const currentValue = '{{ logging_config.get('log_channel_id', '') if logging_config else "" }}';
        channelSelect.outerHTML = `
            <input type="text" class="form-control" id="log_channel_id" name="log_channel_id"
                   value="${currentValue}"
                   placeholder="Channel ID (e.g., 123456789012345678)">
        `;

        // Add help text
        const helpDiv = document.createElement('div');
        helpDiv.className = 'form-text';
        helpDiv.innerHTML = '<i class="fas fa-info-circle me-1"></i>Enter the Discord channel ID manually. Right-click a channel → Copy ID (Developer Mode required)';
        document.getElementById('log_channel_id').parentNode.appendChild(helpDiv);
    }
}

function enableAllLogs() {
    // Enable all log type toggles
    document.querySelectorAll('input[id^="enabled_"]').forEach(function(checkbox) {
        checkbox.checked = true;

        // Show the options for this log type
        const logKey = checkbox.id.replace('enabled_', '');
        const optionsDiv = document.getElementById('options_' + logKey);
        if (optionsDiv) {
            optionsDiv.style.display = 'block';

            // Enable both dashboard and discord for each log type
            const dashboardCheckbox = document.getElementById('dashboard_' + logKey);
            const discordCheckbox = document.getElementById('discord_' + logKey);

            if (dashboardCheckbox) dashboardCheckbox.checked = true;
            if (discordCheckbox) discordCheckbox.checked = true;
        }
    });

    // Also enable global settings
    const dashboardGlobal = document.getElementById('enable_dashboard_logging');
    const discordGlobal = document.getElementById('enable_discord_logging');

    if (dashboardGlobal) dashboardGlobal.checked = true;
    if (discordGlobal) {
        discordGlobal.checked = true;
        // Show the Discord channel section
        const discordChannelSection = document.getElementById('discord_channel_section');
        if (discordChannelSection) {
            discordChannelSection.style.display = 'block';
        }
    }

    alert('All logging options have been enabled! Don\'t forget to save your changes.');
}

function migrateToNewDefaults() {
    if (confirm('This will enable many additional Discord event types that are now available by default. This action will preserve your existing settings but enable new event types. Continue?')) {
        // Create a form to submit the migration request
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ url_for("configure_logs") }}';

        // Add CSRF token
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = '{{ csrf_token() }}';
        form.appendChild(csrfInput);

        // Add action input
        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = 'migrate_to_new_defaults';
        form.appendChild(actionInput);

        // Submit the form
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}
