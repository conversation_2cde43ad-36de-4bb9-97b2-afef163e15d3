"""
Giveaways System Cog for Discord Bot
Handles giveaway creation, management, and winner selection
"""

import discord
from discord.ext import commands, tasks
from discord import ui, ButtonStyle
import asyncio
import logging
import random
from datetime import datetime, timezone
# Views will be imported when needed to avoid circular imports

logger = logging.getLogger(__name__)

class GiveawaysSystem(commands.Cog):
    """Cog for handling the giveaways system functionality"""
    
    def __init__(self, bot):
        self.bot = bot
        self.db = bot.db
        
    async def cog_load(self):
        """Called when the cog is loaded"""
        logger.info("Loading Giveaways System cog...")
        
        # Add persistent views
        from .views import PersistentGiveawayView
        self.bot.add_view(PersistentGiveawayView(self.db))
        
        # Start the giveaway checking task
        if not self.check_giveaways.is_running():
            self.check_giveaways.start()
            
        # Update existing giveaway messages to use persistent views
        await self.update_existing_giveaway_messages()
        
        logger.info("Giveaways System cog loaded successfully")
        
    async def cog_unload(self):
        """Called when the cog is unloaded"""
        logger.info("Unloading Giveaways System cog...")
        
        # Stop the giveaway checking task
        if self.check_giveaways.is_running():
            self.check_giveaways.cancel()
            
        logger.info("Giveaways System cog unloaded")

    @tasks.loop(seconds=30)
    async def check_giveaways(self):
        """Check for expired giveaways every 30 seconds"""
        try:
            expired_giveaways = self.db.get_expired_giveaways()

            for giveaway in expired_giveaways:
                try:
                    await self.end_giveaway(giveaway)
                except Exception as e:
                    logger.error(f"Error ending giveaway {giveaway['_id']}: {e}")

        except Exception as e:
            logger.error(f"Error in check_giveaways: {e}", exc_info=True)

    async def end_giveaway(self, giveaway: dict):
        """End a giveaway and announce winners"""
        try:
            giveaway_id = giveaway['_id']
            entries = giveaway.get('entries', [])
            winner_count = giveaway['winners']

            logger.info(f"Starting end_giveaway for {giveaway_id}")
            logger.info(f"Giveaway data: channel_id={giveaway.get('channel_id')}, message_id={giveaway.get('message_id')}, entries={len(entries)}")

            # Check if admin has pre-selected winners
            admin_winner_ids = giveaway.get('admin_winner_ids', [])
            winners = []

            if admin_winner_ids:
                # Use admin-selected winners
                winners = admin_winner_ids[:winner_count]
                logger.info(f"Using admin-selected winners: {winners}")
            elif entries:
                # Randomly select winners from entries
                available_entries = list(entries)
                winners = random.sample(available_entries, min(winner_count, len(available_entries)))
                logger.info(f"Randomly selected winners: {winners}")
            else:
                logger.info("No entries found for giveaway")

            # Mark giveaway as ended in database
            self.db.end_giveaway(giveaway_id, winners)

            # Get the channel
            channel = self.bot.get_channel(giveaway['channel_id'])
            if not channel:
                logger.error(f"Channel {giveaway['channel_id']} not found")
                return

            logger.info(f"Found channel: {channel.name} ({channel.id}) in guild: {channel.guild.name}")

            # Check bot permissions
            permissions = channel.permissions_for(channel.guild.me)
            logger.info(f"Bot permissions in {channel.name}: send_messages={permissions.send_messages}, view_channel={permissions.view_channel}")

            if not permissions.send_messages:
                logger.error(f"Bot doesn't have permission to send messages in {channel.name}")
                return

            # Create winner announcement embed
            if winners:
                embed = discord.Embed(
                    title="🎉 Giveaway Ended!",
                    description=f"**{giveaway['item']}**",
                    color=discord.Color.gold()
                )
            else:
                embed = discord.Embed(
                    title="😔 Giveaway Ended - No Winners",
                    description=f"**{giveaway['item']}**",
                    color=discord.Color.orange()
                )

            # Add winner information
            if winners:
                winner_mentions = [f"<@{winner_id}>" for winner_id in winners]
                embed.add_field(
                    name="🏆 Winners",
                    value=", ".join(winner_mentions),
                    inline=False
                )
                embed.add_field(
                    name="📊 Stats",
                    value=f"Total Entries: {len(entries)}",
                    inline=False
                )
            else:
                embed.add_field(
                    name="📊 Stats",
                    value=f"Total Entries: {len(entries)}\nNo one entered this giveaway.",
                    inline=False
                )

            # Add host information
            embed.add_field(
                name="👤 Host",
                value=f"<@{giveaway['host_user_id']}>",
                inline=True
            )

            # Send winner announcement
            try:
                await channel.send(embed=embed)
                logger.info(f"Winner announcement sent to {channel.name}")
            except Exception as e:
                logger.error(f"Error sending winner announcement: {e}")

            # Update the original giveaway message to show it ended
            if giveaway.get('message_id'):
                logger.info(f"Updating original giveaway message {giveaway['message_id']}")
                try:
                    original_message = await channel.fetch_message(giveaway['message_id'])
                    logger.info(f"Found original message, updating...")

                    # Create ended embed
                    ended_embed = self.create_ended_giveaway_embed(giveaway, winners, entries)

                    # Remove the button
                    await original_message.edit(embed=ended_embed, view=None)
                    logger.info(f"Original giveaway message updated successfully")

                except discord.NotFound:
                    logger.warning(f"Original giveaway message {giveaway['message_id']} not found")
                except Exception as e:
                    logger.error(f"Error updating original giveaway message: {e}")
            else:
                logger.warning(f"No message_id found for giveaway {giveaway_id}")

        except Exception as e:
            logger.error(f"Error ending giveaway: {e}", exc_info=True)

    async def reroll_giveaway_winners(self, giveaway: dict):
        """Reroll winners for an ended giveaway"""
        try:
            giveaway_id = giveaway['_id']
            entries = giveaway.get('entries', [])
            winner_count = giveaway['winners']
            old_winners = giveaway.get('winners_selected', [])

            logger.info(f"Starting reroll for giveaway {giveaway_id}")
            logger.info(f"Giveaway server_id: {giveaway.get('server_id')}, channel_id: {giveaway.get('channel_id')}")
            logger.info(f"Entries: {len(entries)}, Winner count: {winner_count}, Old winners: {old_winners}")
            logger.info(f"Bot can see guilds: {[(guild.id, guild.name) for guild in self.bot.guilds]}")

            if not entries:
                logger.warning(f"No entries to reroll from for giveaway {giveaway_id}")
                return

            # Filter out previous winners from available entries
            available_entries = [entry for entry in entries if entry not in old_winners]
            
            if not available_entries:
                logger.warning(f"No new entries available for reroll (all entries were previous winners)")
                return

            # Select new winners
            new_winners = random.sample(available_entries, min(winner_count, len(available_entries)))
            logger.info(f"New winners selected: {new_winners}")

            # Update database with new winners
            self.db.update_giveaway_winners(giveaway_id, new_winners)

            # Get the channel
            channel = self.bot.get_channel(giveaway['channel_id'])
            if not channel:
                logger.error(f"Channel {giveaway['channel_id']} not found for reroll")
                return

            # Send reroll announcement
            embed = discord.Embed(
                title="🔄 Giveaway Rerolled!",
                description=f"**{giveaway['item']}**",
                color=discord.Color.blue()
            )

            winner_mentions = [f"<@{winner_id}>" for winner_id in new_winners]
            embed.add_field(
                name="🏆 New Winners",
                value=", ".join(winner_mentions),
                inline=False
            )

            embed.add_field(
                name="📊 Stats",
                value=f"Total Entries: {len(entries)}\nPrevious Winners: {len(old_winners)}",
                inline=False
            )

            embed.add_field(
                name="👤 Host",
                value=f"<@{giveaway['host_user_id']}>",
                inline=True
            )

            try:
                await channel.send(embed=embed)
                logger.info(f"Reroll announcement sent to {channel.name}")
            except Exception as e:
                logger.error(f"Error sending reroll announcement: {e}")

            # Update the original giveaway message to show new winners
            if giveaway.get('message_id'):
                logger.info(f"Updating original giveaway message {giveaway['message_id']} with new winners")
                try:
                    original_message = await channel.fetch_message(giveaway['message_id'])
                    logger.info(f"Found original message, updating with new winners...")

                    # Create updated ended embed with new winners
                    updated_giveaway = giveaway.copy()
                    updated_giveaway['winners_selected'] = new_winners
                    ended_embed = self.create_ended_giveaway_embed(updated_giveaway, new_winners, entries)

                    # Update the message
                    await original_message.edit(embed=ended_embed, view=None)
                    logger.info(f"Original giveaway message updated with new winners")

                except discord.NotFound:
                    logger.warning(f"Original giveaway message {giveaway['message_id']} not found")
                except Exception as e:
                    logger.error(f"Error updating original giveaway message: {e}")
            else:
                logger.warning(f"No message_id found for giveaway {giveaway_id}")

        except Exception as e:
            logger.error(f"Error rerolling giveaway winners: {e}")

    def create_giveaway_embed(self, giveaway: dict) -> discord.Embed:
        """Create a simple and clean embed for a giveaway"""
        # Format requirements with bullet points
        requirements = [req.strip() for req in giveaway.get('requirements', '').split('\n') if req.strip()]
        formatted_requirements = '\n'.join(f"- {req}" for req in requirements)
        
        # Get end time and format it
        end_time = giveaway['end_time']
        
        # Get entries and winners count
        entries_count = len(giveaway.get('entries', []))
        winners_count = giveaway.get('winners', 1)
        
        # Build the description
        description_parts = []
        if formatted_requirements:
            description_parts.append(formatted_requirements)
        
        # Add end time with Discord timestamp for automatic updating
        timestamp = int(end_time.timestamp())
        description_parts.append(f"\nEnds: <t:{timestamp}:R> (<t:{timestamp}:f>)")
        
        # Add host, entries, and winners
        description_parts.append(f"\nHosted by: <@{giveaway['host_user_id']}>")
        description_parts.append(f"Entries: {entries_count}")
        description_parts.append(f"Winners: {winners_count}")
        
        # Join all parts with newlines
        description = '\n'.join(description_parts)
        
        # Create the embed
        embed = discord.Embed(
            title=f"{giveaway['item']}",
            description=description,
            color=discord.Color.gold()
        )
        
        # Add footer with date, giveaway ID, and server invite
        footer_date = end_time.strftime("%m/%d/%Y")  # 7/7/2025
        giveaway_id = str(giveaway.get('_id', ''))
        embed.set_footer(text=f"{footer_date} | ID: {giveaway_id} | ryzuo.com")
        
        return embed

    def create_ended_giveaway_embed(self, giveaway: dict, winners: list, entries: list) -> discord.Embed:
        """Create an embed for an ended giveaway"""
        # Format requirements with bullet points
        requirements = [req.strip() for req in giveaway.get('requirements', '').split('\n') if req.strip()]
        formatted_requirements = '\n'.join(f"- {req}" for req in requirements)

        # Get end time and format it
        end_time = giveaway['end_time']

        # Build the description
        description_parts = []
        if formatted_requirements:
            description_parts.append(formatted_requirements)

        # Add end time with Discord timestamp
        timestamp = int(end_time.timestamp())
        description_parts.append(f"\nEnded: <t:{timestamp}:R> (<t:{timestamp}:f>)")

        # Add host, entries, and winners info
        description_parts.append(f"\nHosted by: <@{giveaway['host_user_id']}>")
        description_parts.append(f"Total Entries: {len(entries)}")

        # Add winner information
        if winners:
            winner_mentions = [f"<@{winner_id}>" for winner_id in winners]
            description_parts.append(f"🏆 Winners: {', '.join(winner_mentions)}")
        else:
            description_parts.append("😔 No Winners: No one entered this giveaway")

        # Join all parts with newlines
        description = '\n'.join(description_parts)

        # Create the embed
        embed = discord.Embed(
            title=f"🔒 ENDED - {giveaway['item']}",
            description=description,
            color=discord.Color.red()
        )

        # Add footer with date and server invite
        footer_date = end_time.strftime("%m/%d/%Y")
        embed.set_footer(text=f"{footer_date} - ryzuo.com")

        return embed

    async def update_existing_giveaway_messages(self):
        """Update all existing giveaway messages to use the new persistent view"""
        try:
            logger.info("Updating existing giveaway messages to use persistent views...")

            # Get all active giveaways
            active_giveaways = self.db.get_all_active_giveaways()

            for giveaway in active_giveaways:
                try:
                    # Skip if no message_id
                    if not giveaway.get('message_id'):
                        continue

                    # Get the guild and channel
                    guild = self.bot.get_guild(giveaway['server_id'])
                    if not guild:
                        logger.warning(f"Guild {giveaway['server_id']} not found for giveaway {giveaway['_id']}")
                        continue

                    channel = guild.get_channel(giveaway['channel_id'])
                    if not channel:
                        logger.warning(f"Channel {giveaway['channel_id']} not found for giveaway {giveaway['_id']}")
                        continue

                    # Get the message
                    try:
                        message = await channel.fetch_message(giveaway['message_id'])

                        # Create updated embed and view
                        embed = self.create_giveaway_embed(giveaway)
                        from .views import PersistentGiveawayView
                        view = PersistentGiveawayView(self.db)

                        # Update the message
                        await message.edit(embed=embed, view=view)
                        logger.info(f"Updated giveaway message {giveaway['message_id']} for giveaway {giveaway['_id']}")

                    except discord.NotFound:
                        logger.warning(f"Message {giveaway['message_id']} not found for giveaway {giveaway['_id']}")
                    except discord.Forbidden:
                        logger.warning(f"No permission to edit message {giveaway['message_id']} for giveaway {giveaway['_id']}")
                    except Exception as e:
                        logger.error(f"Error updating giveaway message {giveaway['message_id']}: {e}")

                except Exception as e:
                    logger.error(f"Error processing giveaway {giveaway.get('_id', 'unknown')}: {e}")

            logger.info("Finished updating existing giveaway messages")

        except Exception as e:
            logger.error(f"Error during giveaway message update: {e}")


async def setup(bot):
    """Setup function for the cog"""
    await bot.add_cog(GiveawaysSystem(bot))
