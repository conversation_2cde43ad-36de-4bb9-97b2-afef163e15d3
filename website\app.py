#!/usr/bin/env python3
"""
ryzuo <PERSON>t Website - Main Entry Point
Separate from the Discord bot for better architecture
"""

import os
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import the Flask app
from flask import Flask
from webhook_listener import handle_stripe_webhook
from web_dashboard import app

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Define the Stripe webhook route
@app.route('/stripe_webhook_listener', methods=['POST'])
def stripe_webhook_listener():
    return handle_stripe_webhook()

if __name__ == "__main__":
    # Get configuration from environment
    host = os.getenv('WEBSITE_HOST', '0.0.0.0')
    port = int(os.getenv('WEBSITE_PORT', 5000))
    debug = os.getenv('WEBSITE_DEBUG', 'False').lower() == 'true'
    
    logger.info(f"Starting ryzuo Bot Website on {host}:{port}")
    logger.info(f"Debug mode: {debug}")
    
    try:
        app.run(
            host=host,
            port=port,
            debug=debug,
            use_reloader=False,
            threaded=True
        )
    except KeyboardInterrupt:
        logger.info("Website shutdown requested")
    except Exception as e:
        logger.error(f"Error starting website: {e}")
