"""
Admin Database Manager - Handles admin-specific database operations
"""

import logging
from datetime import datetime, timezone
from typing import Optional, Dict, Any, List
from pymongo import MongoClient
from bson import ObjectId

logger = logging.getLogger(__name__)

class AdminDatabaseManager:
    def __init__(self, mongo_url: str):
        self.mongo_url = mongo_url
        self.client = None
        self.db = None
        self._ensure_connected()
    
    def _ensure_connected(self):
        """Ensure database connection is active"""
        try:
            if self.client is None:
                self.client = MongoClient(self.mongo_url)
                self.db = self.client['ryzuo']
                # Test connection
                self.client.admin.command('ping')
                logger.info("Admin database connection established")
        except Exception as e:
            logger.error(f"Failed to connect to admin database: {e}")
            raise
    
    # ========== ADMIN LOGGING ==========
    
    def log_admin_action(self, admin_id: str, action: str, details: str = None, 
                        ip_address: str = None, target_user_id: str = None, 
                        target_server_id: str = None) -> bool:
        """Log admin actions for audit trail"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-admin-logs']
            
            log_entry = {
                'admin_id': str(admin_id),
                'action': action,
                'details': details,
                'ip_address': ip_address,
                'target_user_id': str(target_user_id) if target_user_id else None,
                'target_server_id': str(target_server_id) if target_server_id else None,
                'timestamp': datetime.now(timezone.utc)
            }
            
            collection.insert_one(log_entry)
            logger.info(f"Admin action logged: {action} by {admin_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error logging admin action: {e}")
            return False
    
    def get_admin_logs(self, limit: int = 100, skip: int = 0, 
                      admin_id: str = None, action: str = None) -> List[Dict]:
        """Get admin action logs with optional filtering"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-admin-logs']
            
            query = {}
            if admin_id:
                query['admin_id'] = str(admin_id)
            if action:
                query['action'] = action
            
            logs = list(collection.find(query)
                       .sort('timestamp', -1)
                       .skip(skip)
                       .limit(limit))
            
            # Convert ObjectId to string for JSON serialization
            for log in logs:
                log['_id'] = str(log['_id'])
            
            return logs
            
        except Exception as e:
            logger.error(f"Error getting admin logs: {e}")
            return []
    
    # ========== ENVIRONMENT VARIABLE MANAGEMENT ==========
    
    def get_env_config(self, config_type: str) -> Optional[Dict]:
        """Get environment configuration (website or bot)"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-env-configs']
            
            config = collection.find_one({'config_type': config_type})
            if config:
                config['_id'] = str(config['_id'])
            
            return config
            
        except Exception as e:
            logger.error(f"Error getting env config: {e}")
            return None
    
    def update_env_config(self, config_type: str, env_vars: Dict[str, str], 
                         admin_id: str) -> bool:
        """Update environment configuration"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-env-configs']
            
            config_doc = {
                'config_type': config_type,
                'env_vars': env_vars,
                'updated_by': str(admin_id),
                'updated_at': datetime.now(timezone.utc)
            }
            
            result = collection.update_one(
                {'config_type': config_type},
                {'$set': config_doc},
                upsert=True
            )
            
            # Log the action
            self.log_admin_action(
                admin_id=admin_id,
                action='env_config_update',
                details=f"Updated {config_type} environment configuration"
            )
            
            return result.upserted_id is not None or result.modified_count > 0
            
        except Exception as e:
            logger.error(f"Error updating env config: {e}")
            return False
    
    # ========== FEATURE TOGGLE MANAGEMENT ==========
    
    def get_feature_toggles(self) -> Dict[str, bool]:
        """Get current feature toggle settings"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-feature-toggles']
            
            toggles = collection.find_one({'_id': 'feature_toggles'})
            if toggles:
                return toggles.get('features', {})
            
            # Return default feature settings if none exist
            return {
                'repping': False,
                'auto_roling': False,
                'vent': False,
                'temp_voice': False,
                'music': True,
                'sticky_messages': False,
                'giveaways': True,
                'dm_support': False
            }
            
        except Exception as e:
            logger.error(f"Error getting feature toggles: {e}")
            return {}
    
    def update_feature_toggles(self, features: Dict[str, bool], admin_id: str) -> bool:
        """Update feature toggle settings"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-feature-toggles']
            
            toggle_doc = {
                '_id': 'feature_toggles',
                'features': features,
                'updated_by': str(admin_id),
                'updated_at': datetime.now(timezone.utc)
            }
            
            result = collection.update_one(
                {'_id': 'feature_toggles'},
                {'$set': toggle_doc},
                upsert=True
            )
            
            # Log the action
            self.log_admin_action(
                admin_id=admin_id,
                action='feature_toggles_update',
                details=f"Updated feature toggles: {features}"
            )
            
            return result.upserted_id is not None or result.modified_count > 0
            
        except Exception as e:
            logger.error(f"Error updating feature toggles: {e}")
            return False
    
    # ========== PRICING MANAGEMENT ==========
    
    def get_pricing_config(self) -> Dict[str, Any]:
        """Get current pricing configuration"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-pricing-config']
            
            pricing = collection.find_one({'_id': 'pricing_config'})
            if pricing:
                pricing['_id'] = str(pricing['_id'])
                return pricing
            
            # Return default pricing if none exists
            return {
                '_id': 'pricing_config',
                'tiers': {
                    'weekly': {'price': 2.99, 'stripe_price_id': 'price_weekly'},
                    'monthly': {'price': 9.99, 'stripe_price_id': 'price_monthly'},
                    'yearly': {'price': 99.99, 'stripe_price_id': 'price_yearly'}
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting pricing config: {e}")
            return {}
    
    def update_pricing_config(self, pricing_data: Dict[str, Any], admin_id: str) -> bool:
        """Update pricing configuration"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-pricing-config']
            
            pricing_doc = {
                '_id': 'pricing_config',
                'tiers': pricing_data,
                'updated_by': str(admin_id),
                'updated_at': datetime.now(timezone.utc)
            }
            
            result = collection.update_one(
                {'_id': 'pricing_config'},
                {'$set': pricing_doc},
                upsert=True
            )
            
            # Log the action
            self.log_admin_action(
                admin_id=admin_id,
                action='pricing_update',
                details=f"Updated pricing configuration"
            )
            
            return result.upserted_id is not None or result.modified_count > 0
            
        except Exception as e:
            logger.error(f"Error updating pricing config: {e}")
            return False
    
    # ========== IP BLOCKING ==========
    
    def get_blocked_ips(self) -> List[str]:
        """Get list of blocked IP addresses"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-blocked-ips']
            
            blocked_ips = list(collection.find({}, {'ip_address': 1}))
            return [ip['ip_address'] for ip in blocked_ips]
            
        except Exception as e:
            logger.error(f"Error getting blocked IPs: {e}")
            return []
    
    def block_ip(self, ip_address: str, reason: str, admin_id: str) -> bool:
        """Block an IP address"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-blocked-ips']
            
            block_doc = {
                'ip_address': ip_address,
                'reason': reason,
                'blocked_by': str(admin_id),
                'blocked_at': datetime.now(timezone.utc)
            }
            
            result = collection.update_one(
                {'ip_address': ip_address},
                {'$set': block_doc},
                upsert=True
            )
            
            # Log the action
            self.log_admin_action(
                admin_id=admin_id,
                action='ip_block',
                details=f"Blocked IP {ip_address}: {reason}"
            )
            
            return result.upserted_id is not None or result.modified_count > 0
            
        except Exception as e:
            logger.error(f"Error blocking IP: {e}")
            return False
    
    def unblock_ip(self, ip_address: str, admin_id: str) -> bool:
        """Unblock an IP address"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-blocked-ips']

            result = collection.delete_one({'ip_address': ip_address})

            if result.deleted_count > 0:
                # Log the action
                self.log_admin_action(
                    admin_id=admin_id,
                    action='ip_unblock',
                    details=f"Unblocked IP {ip_address}"
                )
                return True

            return False

        except Exception as e:
            logger.error(f"Error unblocking IP: {e}")
            return False

    # ========== USER MANAGEMENT ==========

    def create_manual_subscription(self, user_id: int, tier: str, admin_id: str) -> bool:
        """Create a manual subscription for a user"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-subscriptions']

            # Check if user already has an active subscription
            existing = collection.find_one({
                'user_id': int(user_id),
                'status': {'$in': ['active', 'past_due']},
                'disabled_by_admin': False
            })

            if existing:
                # Update existing subscription
                result = collection.update_one(
                    {'_id': existing['_id']},
                    {
                        '$set': {
                            'subscription_tier': tier,
                            'status': 'active',
                            'disabled_by_admin': False,
                            'manual_subscription': True,
                            'created_by_admin': str(admin_id),
                            'updated_at': datetime.now(timezone.utc)
                        }
                    }
                )
            else:
                # Create new subscription
                subscription_doc = {
                    'user_id': int(user_id),
                    'stripe_customer_id': f'manual_{user_id}_{int(datetime.now().timestamp())}',
                    'stripe_subscription_id': f'manual_sub_{user_id}_{int(datetime.now().timestamp())}',
                    'subscription_tier': tier,
                    'status': 'active',
                    'manual_subscription': True,
                    'created_by_admin': str(admin_id),
                    'created_at': datetime.now(timezone.utc),
                    'updated_at': datetime.now(timezone.utc),
                    'disabled_by_admin': False
                }

                result = collection.insert_one(subscription_doc)

            return True

        except Exception as e:
            logger.error(f"Error creating manual subscription: {e}")
            return False

    def disable_user_account(self, user_id: int, reason: str, admin_id: str) -> bool:
        """Disable a user account"""
        try:
            self._ensure_connected()

            # Disable user in users collection
            users_collection = self.db['ryzuo-users']
            users_collection.update_one(
                {'user_id': str(user_id)},
                {
                    '$set': {
                        'disabled_by_admin': True,
                        'disabled_reason': reason,
                        'disabled_by': str(admin_id),
                        'disabled_at': datetime.now(timezone.utc)
                    }
                },
                upsert=True
            )

            # Disable their subscription if they have one
            subscriptions_collection = self.db['ryzuo-subscriptions']
            subscriptions_collection.update_many(
                {'user_id': int(user_id)},
                {
                    '$set': {
                        'disabled_by_admin': True,
                        'disabled_reason': reason,
                        'disabled_by': str(admin_id),
                        'disabled_at': datetime.now(timezone.utc)
                    }
                }
            )

            # Log the action
            self.log_admin_action(
                admin_id=admin_id,
                action='user_disable',
                details=f"Disabled user {user_id}: {reason}",
                target_user_id=str(user_id)
            )

            return True

        except Exception as e:
            logger.error(f"Error disabling user account: {e}")
            return False

    def enable_user_account(self, user_id: int, admin_id: str) -> bool:
        """Enable a user account"""
        try:
            self._ensure_connected()

            # Enable user in users collection
            users_collection = self.db['ryzuo-users']
            users_collection.update_one(
                {'user_id': str(user_id)},
                {
                    '$set': {
                        'disabled_by_admin': False,
                        'enabled_by': str(admin_id),
                        'enabled_at': datetime.now(timezone.utc)
                    },
                    '$unset': {
                        'disabled_reason': '',
                        'disabled_by': '',
                        'disabled_at': ''
                    }
                }
            )

            # Enable their subscription if they have one
            subscriptions_collection = self.db['ryzuo-subscriptions']
            subscriptions_collection.update_many(
                {'user_id': int(user_id)},
                {
                    '$set': {
                        'disabled_by_admin': False,
                        'enabled_by': str(admin_id),
                        'enabled_at': datetime.now(timezone.utc)
                    },
                    '$unset': {
                        'disabled_reason': '',
                        'disabled_by': '',
                        'disabled_at': ''
                    }
                }
            )

            # Log the action
            self.log_admin_action(
                admin_id=admin_id,
                action='user_enable',
                details=f"Enabled user {user_id}",
                target_user_id=str(user_id)
            )

            return True

        except Exception as e:
            logger.error(f"Error enabling user account: {e}")
            return False

    def delete_user_completely(self, user_id: int, admin_id: str) -> bool:
        """Completely delete a user and all their data"""
        try:
            self._ensure_connected()

            # Collections to clean up
            collections_to_clean = [
                'ryzuo-users',
                'ryzuo-subscriptions',
                'ryzuo-license-keys',
                'ryzuo-notifications'
            ]

            # Delete from all collections
            for collection_name in collections_to_clean:
                collection = self.db[collection_name]
                if collection_name == 'ryzuo-subscriptions':
                    collection.delete_many({'user_id': int(user_id)})
                else:
                    collection.delete_many({'user_id': str(user_id)})

            # Delete servers owned by this user
            servers_collection = self.db['ryzuo-server-configs']
            owned_servers = list(servers_collection.find({'owner_id': str(user_id)}))

            for server in owned_servers:
                self._delete_server_data(server['server_id'])

            # Log the action
            self.log_admin_action(
                admin_id=admin_id,
                action='user_delete',
                details=f"Completely deleted user {user_id} and {len(owned_servers)} owned servers",
                target_user_id=str(user_id)
            )

            return True

        except Exception as e:
            logger.error(f"Error deleting user completely: {e}")
            return False

    def _delete_server_data(self, server_id: str):
        """Helper method to delete all data for a server"""
        try:
            server_collections = [
                'ryzuo-server-configs',
                'ryzuo-bot-logs',
                'ryzuo-temp-channels',
                'ryzuo-giveaways',
                'ryzuo-sticky-messages'
            ]

            for collection_name in server_collections:
                collection = self.db[collection_name]
                if collection_name == 'ryzuo-server-configs':
                    collection.delete_one({'server_id': server_id})
                else:
                    collection.delete_many({'server_id': int(server_id)})

        except Exception as e:
            logger.error(f"Error deleting server data for {server_id}: {e}")

    # ========== COMMAND BLOCKING ==========

    def block_user_commands(self, user_id: int, commands: List[str], reason: str, admin_id: str) -> bool:
        """Block specific commands for a user"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-command-blocks']

            block_doc = {
                'user_id': int(user_id),
                'blocked_commands': commands,
                'reason': reason,
                'blocked_by': str(admin_id),
                'blocked_at': datetime.now(timezone.utc)
            }

            result = collection.update_one(
                {'user_id': int(user_id)},
                {'$set': block_doc},
                upsert=True
            )

            # Log the action
            self.log_admin_action(
                admin_id=admin_id,
                action='commands_block',
                details=f"Blocked commands {commands} for user {user_id}: {reason}",
                target_user_id=str(user_id)
            )

            return result.upserted_id is not None or result.modified_count > 0

        except Exception as e:
            logger.error(f"Error blocking user commands: {e}")
            return False

    def unblock_user_commands(self, user_id: int, admin_id: str) -> bool:
        """Unblock all commands for a user"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-command-blocks']

            result = collection.delete_one({'user_id': int(user_id)})

            if result.deleted_count > 0:
                # Log the action
                self.log_admin_action(
                    admin_id=admin_id,
                    action='commands_unblock',
                    details=f"Unblocked all commands for user {user_id}",
                    target_user_id=str(user_id)
                )
                return True

            return False

        except Exception as e:
            logger.error(f"Error unblocking user commands: {e}")
            return False

    def get_blocked_commands(self, user_id: int) -> Optional[Dict]:
        """Get blocked commands for a user"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-command-blocks']

            block_info = collection.find_one({'user_id': int(user_id)})
            if block_info:
                block_info['_id'] = str(block_info['_id'])

            return block_info

        except Exception as e:
            logger.error(f"Error getting blocked commands: {e}")
            return None

    # ========== GLOBAL NOTIFICATIONS ==========

    def create_global_notification(self, title: str, message: str,
                                 notification_type: str, admin_id: str) -> bool:
        """Create a global notification for all users"""
        try:
            self._ensure_connected()

            # Get all users
            users_collection = self.db['ryzuo-users']
            all_users = list(users_collection.find({}, {'user_id': 1}))

            # Create notification for each user
            notifications_collection = self.db['ryzuo-user-notifications']
            notifications = []

            for user in all_users:
                notification_doc = {
                    'user_id': str(user['user_id']),
                    'title': title,
                    'message': message,
                    'type': notification_type,
                    'read': False,
                    'global_notification': True,
                    'created_by_admin': str(admin_id),
                    'created_at': datetime.now(timezone.utc)
                }
                notifications.append(notification_doc)

            if notifications:
                notifications_collection.insert_many(notifications)
                
                # Clean up old notifications for each user (keep only last 5)
                for user in all_users:
                    self._cleanup_user_notifications(str(user['user_id']))

            # Log the action
            self.log_admin_action(
                admin_id=admin_id,
                action='global_notification',
                details=f"Sent global notification '{title}' to {len(notifications)} users"
            )

            return True

        except Exception as e:
            logger.error(f"Error creating global notification: {e}")
            return False

    def _cleanup_user_notifications(self, user_id: str) -> None:
        """Keep only the last 5 notifications for a user"""
        try:
            collection = self.db['ryzuo-user-notifications']
            
            # Get all notifications for user, sorted by creation date (newest first)
            notifications = list(collection.find(
                {"user_id": str(user_id)}
            ).sort("created_at", -1))
            
            # If more than 5 notifications, delete the oldest ones
            if len(notifications) > 5:
                notifications_to_delete = notifications[5:]  # Keep first 5, delete rest
                notification_ids = [notif['_id'] for notif in notifications_to_delete]
                
                collection.delete_many({
                    "_id": {"$in": notification_ids}
                })
                
                logger.debug(f"Cleaned up {len(notification_ids)} old notifications for user {user_id}")
                
        except Exception as e:
            logger.error(f"Error cleaning up notifications for user {user_id}: {e}")

    # ========== PROCESS MANAGEMENT ==========

    def get_process_status(self) -> Dict[str, Any]:
        """Get status of all processes (shards and website)"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-process-status']

            # Get latest status for each process
            processes = {}

            # Get shard statuses
            shard_statuses = list(collection.find({'process_type': 'shard'})
                                .sort('last_heartbeat', -1))

            for shard in shard_statuses:
                shard_id = shard.get('shard_id', 'unknown')
                processes[f'shard_{shard_id}'] = {
                    'type': 'shard',
                    'status': shard.get('status', 'unknown'),
                    'last_heartbeat': shard.get('last_heartbeat'),
                    'uptime': shard.get('uptime', 0),
                    'guild_count': shard.get('guild_count', 0),
                    'user_count': shard.get('user_count', 0)
                }

            # Get website status
            website_status = collection.find_one({'process_type': 'website'})
            if website_status:
                processes['website'] = {
                    'type': 'website',
                    'status': website_status.get('status', 'unknown'),
                    'last_heartbeat': website_status.get('last_heartbeat'),
                    'uptime': website_status.get('uptime', 0)
                }

            return processes

        except Exception as e:
            logger.error(f"Error getting process status: {e}")
            return {}

    def restart_process(self, process_name: str, admin_id: str) -> bool:
        """Request a process restart"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-process-commands']

            command_doc = {
                'process_name': process_name,
                'command': 'restart',
                'requested_by': str(admin_id),
                'requested_at': datetime.now(timezone.utc),
                'status': 'pending'
            }

            collection.insert_one(command_doc)

            # Log the action
            self.log_admin_action(
                admin_id=admin_id,
                action='process_restart',
                details=f"Requested restart for process: {process_name}"
            )

            return True

        except Exception as e:
            logger.error(f"Error requesting process restart: {e}")
            return False

    def get_process_logs(self, process_name: str, lines: int = 100) -> List[str]:
        """Get recent logs for a process"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-process-logs']

            logs = list(collection.find({'process_name': process_name})
                       .sort('timestamp', -1)
                       .limit(lines))

            return [log.get('message', '') for log in logs]

        except Exception as e:
            logger.error(f"Error getting process logs: {e}")
            return []

    # ========== SYSTEM STATISTICS ==========

    def get_system_statistics(self) -> Dict[str, Any]:
        """Get comprehensive system statistics"""
        try:
            self._ensure_connected()

            stats = {}

            # User statistics
            users_collection = self.db['ryzuo-users']
            stats['total_users'] = users_collection.count_documents({})
            stats['disabled_users'] = users_collection.count_documents({'disabled_by_admin': True})

            # Server statistics
            servers_collection = self.db['ryzuo-server-configs']
            stats['total_servers'] = servers_collection.count_documents({})

            # Subscription statistics
            subscriptions_collection = self.db['ryzuo-subscriptions']
            stats['active_subscriptions'] = subscriptions_collection.count_documents({
                'status': {'$in': ['active', 'past_due']},
                'disabled_by_admin': False
            })
            stats['disabled_subscriptions'] = subscriptions_collection.count_documents({
                'disabled_by_admin': True
            })

            # Giveaway statistics
            giveaways_collection = self.db['ryzuo-giveaways']
            stats['total_giveaways'] = giveaways_collection.count_documents({})
            stats['active_giveaways'] = giveaways_collection.count_documents({'ended': False})

            # Security statistics
            stats['blocked_ips'] = len(self.get_blocked_ips())

            command_blocks_collection = self.db['ryzuo-command-blocks']
            stats['blocked_users'] = command_blocks_collection.count_documents({})

            # Recent activity
            logs_collection = self.db['ryzuo-bot-logs']
            today = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
            stats['today_activities'] = logs_collection.count_documents({
                'timestamp': {'$gte': today}
            })

            return stats

        except Exception as e:
            logger.error(f"Error getting system statistics: {e}")
            return {}

    # ========== SERVER MANAGEMENT ==========

    def get_server_details(self, server_id: str) -> Optional[Dict]:
        """Get detailed server information"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-server-configs']
            
            server = collection.find_one({'server_id': server_id})
            if server:
                server['_id'] = str(server['_id'])
                
                # Get additional server data
                # Get recent logs for this server
                logs_collection = self.db['ryzuo-bot-logs']
                recent_logs = list(logs_collection.find({'server_id': int(server_id)})
                                 .sort('timestamp', -1)
                                 .limit(10))
                
                for log in recent_logs:
                    log['_id'] = str(log['_id'])
                
                server['recent_logs'] = recent_logs
                
                # Get giveaway count
                giveaways_collection = self.db['ryzuo-giveaways']
                server['giveaway_count'] = giveaways_collection.count_documents({'server_id': int(server_id)})
                
                # Get temp channel count
                temp_channels_collection = self.db['ryzuo-temp-channels']
                server['temp_channel_count'] = temp_channels_collection.count_documents({'server_id': int(server_id)})
            
            return server
            
        except Exception as e:
            logger.error(f"Error getting server details: {e}")
            return None

    def reset_server_config(self, server_id: str, admin_id: str) -> bool:
        """Reset server configuration to defaults"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-server-configs']
            
            # Default server configuration
            default_config = {
                'server_id': server_id,
                'features': {
                    'repping': False,
                    'auto_roling': False,
                    'vent': False,
                    'temp_voice': False,
                    'music': True,
                    'sticky_messages': False,
                    'giveaways': True
                },
                'reset_by_admin': str(admin_id),
                'reset_at': datetime.now(timezone.utc),
                'updated_at': datetime.now(timezone.utc)
            }
            
            result = collection.update_one(
                {'server_id': server_id},
                {'$set': default_config}
            )
            
            if result.modified_count > 0:
                # Log the action
                self.log_admin_action(
                    admin_id=admin_id,
                    action='server_config_reset',
                    details=f"Reset configuration for server {server_id}",
                    target_server_id=server_id
                )
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error resetting server config: {e}")
            return False

    def delete_server_completely(self, server_id: str, admin_id: str) -> bool:
        """Completely delete a server and all its data"""
        try:
            self._ensure_connected()
            
            # Delete all server data
            self._delete_server_data(server_id)
            
            # Log the action
            self.log_admin_action(
                admin_id=admin_id,
                action='server_delete',
                details=f"Completely deleted server {server_id}",
                target_server_id=server_id
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Error deleting server completely: {e}")
            return False
