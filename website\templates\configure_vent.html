﻿{% extends "base.html" %}

{% block title %}Configure Vent System - {{ server_info.name }}{% endblock %}

{% block styles %}
<style>
    .config-header {
        background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(248, 113, 113, 0.1) 100%);
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(20px);
        border: 1px solid var(--border-color);
    }

    .config-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #ef4444, #f87171);
    }

    .config-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .config-subtitle {
        color: var(--text-secondary);
        font-size: 1.1rem;
        margin-bottom: 0;
        opacity: 0.9;
    }

    .feature-card {
        background: rgba(255, 255, 255, 0.03);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        backdrop-filter: blur(20px);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        position: relative;
    }

    .feature-card:hover {
        transform: translateY(-5px);
        border-color: #ef4444;
        box-shadow: 0 15px 40px rgba(239, 68, 68, 0.15);
    }

    .feature-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #ef4444, #f87171);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .feature-card:hover::before {
        opacity: 1;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mt-3">
    <!-- Sidebar -->
    <div class="col-md-3">
        {% include 'sidebar.html' %}
    </div>

    <!-- Main Content -->
    <div class="col-md-9">
        <!-- Configuration Header -->
        <div class="config-header">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <h1 class="config-title">
                        <i class="fas fa-heart text-danger"></i>
                        Vent System Configuration
                    </h1>
                    <p class="config-subtitle">Configure anonymous venting for your server members</p>
                </div>
                <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                </a>
            </div>
        </div>



        <div class="row">
            <div class="col-lg-8">
                <div class="feature-card">
                    <div class="card-header">
                        <h5 class="mb-0">Configure Anonymous Venting</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                            <div class="mb-3">
                                <label for="vent_channel_id" class="form-label">Vent Channel</label>
                                <select class="form-select" id="vent_channel_id" name="vent_channel_id" required>
                                    <option value="">Select a channel...</option>
                                </select>
                                <div class="form-text">
                                    The Discord channel where anonymous vent messages will be posted
                                </div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Save Configuration
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                {% if vent_settings %}
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">Current Configuration</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Vent Channel ID:</strong>
                                <br><code>{{ vent_settings.vent_channel_id }}</code>
                            </div>
                            {% if logging_config and logging_config.get('discord_enabled') and logging_config.get('log_channel_id') %}
                            <div class="col-md-6">
                                <strong>Log Channel ID:</strong>
                                <br><code>{{ logging_config.log_channel_id }}</code>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>

            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>How to Get Channel ID</h6>
                    </div>
                    <div class="card-body">
                        <ol class="mb-0">
                            <li>Enable Developer Mode in Discord settings</li>
                            <li>Right-click on the channel</li>
                            <li>Select "Copy ID"</li>
                            <li>Paste the ID in the form</li>
                        </ol>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-lightbulb me-2"></i>How It Works</h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-2">The vent system allows users to:</p>
                        <ul class="mb-3">
                            <li>Send anonymous messages using <code>/vent</code></li>
                            <li>Messages appear anonymous to other users</li>
                            <li>All messages are logged for moderation</li>
                            <li>Requires premium license validation</li>
                        </ul>
                        <p class="text-warning mb-0">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            <strong>Privacy Note:</strong> While messages appear anonymous to users, 
                            all vent messages are logged with user information for moderation purposes.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>

// Load Discord data for dropdowns
document.addEventListener('DOMContentLoaded', function() {
    {% if config and config.log_channel_id %}
    loadChannels();
    {% endif %}
});

function loadChannels() {
    fetch('{{ url_for('api_channels') }}')
        .then(response => response.json())
        .then(channels => {
            const channelSelect = document.getElementById('vent_channel_id');
            channelSelect.innerHTML = '<option value="">Select a channel...</option>';

            // Filter to text channels only
            const textChannels = channels.filter(channel => channel.type === 'text');

            // Group by category
            const categorized = {};
            textChannels.forEach(channel => {
                const category = channel.category || 'No Category';
                if (!categorized[category]) {
                    categorized[category] = [];
                }
                categorized[category].push(channel);
            });

            // Add channels grouped by category
            Object.keys(categorized).sort().forEach(category => {
                if (category !== 'No Category') {
                    const optgroup = document.createElement('optgroup');
                    optgroup.label = category;
                    channelSelect.appendChild(optgroup);

                    categorized[category].forEach(channel => {
                        const option = document.createElement('option');
                        option.value = channel.id;
                        option.textContent = `# ${channel.name}`;

                        // Select current channel if editing
                        {% if vent_settings and vent_settings.vent_channel_id %}
                        if (channel.id === '{{ vent_settings.vent_channel_id }}') {
                            option.selected = true;
                        }
                        {% endif %}

                        optgroup.appendChild(option);
                    });
                }
            });

            // Add uncategorized channels
            if (categorized['No Category']) {
                categorized['No Category'].forEach(channel => {
                    const option = document.createElement('option');
                    option.value = channel.id;
                    option.textContent = `# ${channel.name}`;

                    // Select current channel if editing
                    {% if vent_settings and vent_settings.vent_channel_id %}
                    if (channel.id === '{{ vent_settings.vent_channel_id }}') {
                        option.selected = true;
                    }
                    {% endif %}

                    channelSelect.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('Error loading channels:', error);
        });
}
</script>
{% endblock %}
