"""
Music System Cog for Discord Bot
Handles music playback, queue management, and voice connections
"""

import discord
from discord.ext import commands, tasks
import logging
from .music_system import MusicManager
from .music_commands import setup_music_commands

logger = logging.getLogger(__name__)

class MusicSystem(commands.Cog):
    """Cog for handling the music system functionality"""
    
    def __init__(self, bot):
        self.bot = bot
        self.db = bot.db
        
        # Initialize music manager
        self.music_manager = MusicManager(bot)
        bot.music_manager = self.music_manager
        
    async def cog_load(self):
        """Called when the cog is loaded"""
        logger.info("Loading Music System cog...")
        
        # Setup music commands
        setup_music_commands(self.bot)
        
        # Start music system cleanup task
        if not self.cleanup_music_connections.is_running():
            self.cleanup_music_connections.start()
            
        logger.info("Music System cog loaded successfully")
        
    async def cog_unload(self):
        """Called when the cog is unloaded"""
        logger.info("Unloading Music System cog...")
        
        # Stop the cleanup task
        if self.cleanup_music_connections.is_running():
            self.cleanup_music_connections.cancel()
            
        # Disconnect from all voice channels
        if hasattr(self, 'music_manager'):
            for guild_id in list(self.music_manager.voice_clients.keys()):
                try:
                    voice_client = self.music_manager.voice_clients[guild_id]
                    if voice_client and voice_client.is_connected():
                        await voice_client.disconnect()
                except Exception as e:
                    logger.error(f"Error disconnecting from voice in guild {guild_id}: {e}")
                    
        logger.info("Music System cog unloaded")

    @commands.Cog.listener()
    async def on_voice_state_update(self, member, before, after):
        """Handle voice state updates for music system auto-disconnect"""
        try:
            # Skip if member is a bot
            if member.bot:
                return

            # Check if music system is enabled for this guild
            if not self.db.is_system_enabled(member.guild.id, "music"):
                return

            # Handle music system voice state changes
            if hasattr(self.bot, 'music_manager'):
                await self.bot.music_manager.handle_voice_state_update(member, before, after)

        except Exception as e:
            logger.error(f"Error in music voice state update handler: {e}")

    @tasks.loop(minutes=5)
    async def cleanup_music_connections(self):
        """Cleanup inactive music connections every 5 minutes"""
        try:
            if not hasattr(self.bot, 'music_manager'):
                return
                
            for guild_id in list(self.music_manager.voice_clients.keys()):
                try:
                    voice_client = self.music_manager.voice_clients[guild_id]
                    
                    # Check if voice client is still connected
                    if not voice_client or not voice_client.is_connected():
                        # Clean up disconnected voice client
                        if guild_id in self.music_manager.voice_clients:
                            del self.music_manager.voice_clients[guild_id]
                        if guild_id in self.music_manager.playlists:
                            del self.music_manager.playlists[guild_id]
                        if guild_id in self.music_manager.current_songs:
                            del self.music_manager.current_songs[guild_id]
                        if guild_id in self.music_manager.loop_modes:
                            del self.music_manager.loop_modes[guild_id]
                        if guild_id in self.music_manager.is_playing:
                            del self.music_manager.is_playing[guild_id]
                        if guild_id in self.music_manager.is_paused:
                            del self.music_manager.is_paused[guild_id]
                        continue
                    
                    # Check if channel is empty (no human members)
                    channel = voice_client.channel
                    if channel:
                        human_members = [m for m in channel.members if not m.bot]
                        if len(human_members) == 0:
                            # No humans in channel, disconnect after delay
                            logger.info(f"No humans in voice channel {channel.name}, scheduling disconnect")
                            await self.music_manager.schedule_disconnect(guild_id, delay=30)
                            
                except Exception as e:
                    logger.error(f"Error cleaning up music connection for guild {guild_id}: {e}")
                    
        except Exception as e:
            logger.error(f"Error in cleanup_music_connections task: {e}")

    @cleanup_music_connections.before_loop
    async def before_cleanup_music_connections(self):
        """Wait for bot to be ready before starting cleanup task"""
        await self.bot.wait_until_ready()


async def setup(bot):
    """Setup function for the cog"""
    await bot.add_cog(MusicSystem(bot))
