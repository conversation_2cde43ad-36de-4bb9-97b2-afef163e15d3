{% extends "admin/base.html" %}

{% block title %}User Management{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-users me-2"></i>
        User Management
    </h1>
    <div>
        <button class="btn btn-admin" onclick="showAddSubscriptionModal()">
            <i class="fas fa-crown me-2"></i>
            Add Premium
        </button>
    </div>
</div>

<!-- Search and Filter -->
<div class="card admin-card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-6">
                <label for="search" class="form-label">Search Users</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ search }}" placeholder="Search by username or user ID">
            </div>
            <div class="col-md-3">
                <label for="filter" class="form-label">Filter</label>
                <select class="form-select" id="filter" name="filter">
                    <option value="all" {{ 'selected' if filter_type == 'all' }}>All Users</option>
                    <option value="premium" {{ 'selected' if filter_type == 'premium' }}>Premium Users</option>
                    <option value="disabled" {{ 'selected' if filter_type == 'disabled' }}>Disabled Users</option>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-admin me-2">
                    <i class="fas fa-search me-1"></i>Search
                </button>
                <a href="{{ url_for('admin_users') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>Clear
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Users Table -->
<div class="card admin-card">
    <div class="card-header admin-card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            Users ({{ users|length }} of {{ total_users if total_users else 0 }})
        </h5>
    </div>
    <div class="card-body p-0">
        {% if users %}
        <div class="table-responsive">
            <table class="table table-admin mb-0">
                <thead>
                    <tr>
                        <th>User</th>
                        <th>User ID</th>
                        <th>Subscription</th>
                        <th>Status</th>
                        <th>Joined</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                    <tr id="user-{{ user.user_id }}">
                        <td>
                            <div class="d-flex align-items-center">
                                {% if user.avatar %}
                                <img src="https://cdn.discordapp.com/avatars/{{ user.user_id }}/{{ user.avatar }}.png?size=64" 
                                     class="user-avatar me-2" alt="Avatar">
                                {% else %}
                                <div class="user-avatar me-2 bg-secondary d-flex align-items-center justify-content-center">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                                {% endif %}
                                <div>
                                    <strong>{{ user.username or 'Unknown' }}</strong>
                                    {% if user.discriminator and user.discriminator != '0' %}
                                    <small class="text-muted">#{{ user.discriminator }}</small>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td>
                            <code>{{ user.user_id }}</code>
                        </td>
                        <td>
                            {% if user.subscription %}
                            <span class="status-badge status-premium">
                                <i class="fas fa-crown me-1"></i>
                                {{ user.subscription.subscription_tier.title() }}
                            </span>
                            {% else %}
                            <span class="status-badge status-disabled">
                                <i class="fas fa-times me-1"></i>
                                None
                            </span>
                            {% endif %}
                        </td>
                        <td>
                            {% if user.get('disabled_by_admin') %}
                            <span class="status-badge status-disabled">
                                <i class="fas fa-ban me-1"></i>
                                Disabled
                            </span>
                            {% else %}
                            <span class="status-badge status-active">
                                <i class="fas fa-check-circle me-1"></i>
                                Active
                            </span>
                            {% endif %}
                        </td>
                        <td>
                            <small class="text-muted">
                                {{ user.created_at.strftime('%Y-%m-%d') if user.created_at else 'Unknown' }}
                            </small>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <button class="btn btn-sm btn-outline-primary" 
                                        onclick="viewUserDetails('{{ user.user_id }}')">
                                    <i class="fas fa-eye"></i>
                                </button>
                                
                                {% if not user.subscription %}
                                <button class="btn btn-sm btn-outline-success" 
                                        onclick="addSubscription('{{ user.user_id }}')">
                                    <i class="fas fa-crown"></i>
                                </button>
                                {% else %}
                                <button class="btn btn-sm btn-outline-warning" 
                                        onclick="removeSubscription('{{ user.user_id }}')">
                                    <i class="fas fa-crown"></i>
                                </button>
                                {% endif %}
                                
                                {% if user.get('disabled_by_admin') %}
                                <button class="btn btn-sm btn-outline-success" 
                                        onclick="enableUser('{{ user.user_id }}')">
                                    <i class="fas fa-unlock"></i>
                                </button>
                                {% else %}
                                <button class="btn btn-sm btn-outline-warning" 
                                        onclick="disableUser('{{ user.user_id }}')">
                                    <i class="fas fa-lock"></i>
                                </button>
                                {% endif %}
                                
                                <button class="btn btn-sm btn-outline-danger" 
                                        onclick="deleteUser('{{ user.user_id }}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if total_pages > 1 %}
        <div class="card-footer">
            <nav aria-label="User pagination">
                <ul class="pagination justify-content-center mb-0">
                    {% if page > 1 %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page - 1 }}&search={{ search }}&filter={{ filter_type }}">Previous</a>
                    </li>
                    {% endif %}
                    
                    {% for p in range(1, total_pages + 1) %}
                    {% if p == page %}
                    <li class="page-item active">
                        <span class="page-link">{{ p }}</span>
                    </li>
                    {% elif p <= 3 or p >= total_pages - 2 or (p >= page - 2 and p <= page + 2) %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ p }}&search={{ search }}&filter={{ filter_type }}">{{ p }}</a>
                    </li>
                    {% elif p == 4 or p == total_pages - 3 %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                    {% endif %}
                    {% endfor %}
                    
                    {% if page < total_pages %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page + 1 }}&search={{ search }}&filter={{ filter_type }}">Next</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No users found</h5>
            <p class="text-muted">Try adjusting your search criteria</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Add Subscription Modal -->
<div class="modal fade" id="addSubscriptionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-crown me-2"></i>
                    Add Premium Subscription
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addSubscriptionForm">
                    <div class="mb-3">
                        <label for="subscriptionUserId" class="form-label">User ID</label>
                        <input type="text" class="form-control" id="subscriptionUserId" required>
                    </div>
                    <div class="mb-3">
                        <label for="subscriptionTier" class="form-label">Subscription Tier</label>
                        <select class="form-select" id="subscriptionTier" required>
                            <option value="">Select tier...</option>
                            <option value="weekly">Weekly</option>
                            <option value="monthly">Monthly</option>
                            <option value="yearly">Yearly</option>
                            <option value="lifetime">Lifetime</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-admin" onclick="submitAddSubscription()">Add Subscription</button>
            </div>
        </div>
    </div>
</div>

<!-- User Details Modal -->
<div class="modal fade" id="userDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user me-2"></i>
                    User Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="userDetailsContent">
                <!-- Content loaded dynamically -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function showAddSubscriptionModal() {
    new bootstrap.Modal(document.getElementById('addSubscriptionModal')).show();
}

function addSubscription(userId) {
    document.getElementById('subscriptionUserId').value = userId;
    showAddSubscriptionModal();
}

function submitAddSubscription() {
    const userId = document.getElementById('subscriptionUserId').value;
    const tier = document.getElementById('subscriptionTier').value;
    
    if (!userId || !tier) {
        alert('Please fill in all fields');
        return;
    }
    
    fetch(`/api/user/${userId}/subscription`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        },
        body: JSON.stringify({
            action: 'add',
            tier: tier
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            bootstrap.Modal.getInstance(document.getElementById('addSubscriptionModal')).hide();
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while adding subscription');
    });
}

function removeSubscription(userId) {
    if (!confirm('Are you sure you want to remove this user\'s subscription?')) {
        return;
    }
    
    fetch(`/api/user/${userId}/subscription`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        },
        body: JSON.stringify({
            action: 'remove'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while removing subscription');
    });
}

function disableUser(userId) {
    const reason = prompt('Enter reason for disabling this user:');
    if (!reason) return;
    
    fetch(`/api/user/${userId}/disable`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        },
        body: JSON.stringify({
            reason: reason
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while disabling user');
    });
}

function enableUser(userId) {
    if (!confirm('Are you sure you want to enable this user?')) {
        return;
    }
    
    fetch(`/api/user/${userId}/enable`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while enabling user');
    });
}

function deleteUser(userId) {
    if (!confirm('Are you sure you want to PERMANENTLY DELETE this user and ALL their data? This action cannot be undone!')) {
        return;
    }
    
    if (!confirm('This will delete the user, their servers, and all associated data. Are you absolutely sure?')) {
        return;
    }
    
    fetch(`/api/user/${userId}/delete`, {
        method: 'DELETE',
        headers: {
            'X-CSRFToken': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById(`user-${userId}`).remove();
            alert('User deleted successfully');
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting user');
    });
}

function viewUserDetails(userId) {
    // This would load detailed user information
    document.getElementById('userDetailsContent').innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</div>';
    new bootstrap.Modal(document.getElementById('userDetailsModal')).show();
    
    // Implement user details loading here
    setTimeout(() => {
        document.getElementById('userDetailsContent').innerHTML = `
            <p>Detailed user information for User ID: ${userId}</p>
            <p>This feature would show comprehensive user data, activity logs, owned servers, etc.</p>
        `;
    }, 1000);
}
</script>
{% endblock %}
