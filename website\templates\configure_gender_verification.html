﻿{% extends "base.html" %}

{% block title %}Configure Gender Verification - {{ server_info.name }}{% endblock %}

{% block styles %}
<style>
    .config-header {
        background: linear-gradient(135deg, rgba(107, 114, 128, 0.1) 0%, rgba(156, 163, 175, 0.1) 100%);
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(20px);
        border: 1px solid var(--border-color);
    }

    .config-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #6b7280, #9ca3af);
    }

    .config-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .config-subtitle {
        color: var(--text-secondary);
        font-size: 1.1rem;
        margin-bottom: 0;
        opacity: 0.9;
    }

    .feature-card {
        background: rgba(255, 255, 255, 0.03);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        backdrop-filter: blur(20px);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        position: relative;
    }

    .feature-card:hover {
        transform: translateY(-5px);
        border-color: #6b7280;
        box-shadow: 0 15px 40px rgba(107, 114, 128, 0.15);
    }

    .feature-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #6b7280, #9ca3af);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .feature-card:hover::before {
        opacity: 1;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mt-3">
    <!-- Sidebar -->
    <div class="col-md-3">
        {% include 'sidebar.html' %}
    </div>

    <!-- Main Content -->
    <div class="col-md-9">
        <!-- Configuration Header -->
        <div class="config-header">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <h1 class="config-title">
                        <i class="fas fa-shield-alt text-secondary"></i>
                        Gender Verification Configuration
                    </h1>
                    <p class="config-subtitle">Configure gender verification system for your server</p>
                </div>
                <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                </a>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <div class="feature-card">
                    <div class="card-header">
                        <h5 class="mb-0">Configure Gender Verification System</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                            <div class="mb-3">
                                <label for="channel_id" class="form-label">Verification Channel</label>
                                <select class="form-select" id="channel_id" name="channel_id" required>
                                    <option value="">Select a channel...</option>
                                </select>
                                <div class="form-text">
                                    Channel where the verification message will be posted
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="category_id" class="form-label">Ticket Category</label>
                                <select class="form-select" id="category_id" name="category_id" required>
                                    <option value="">Select a category...</option>
                                </select>
                                <div class="form-text">
                                    Category where verification tickets will be created
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="support_role_id" class="form-label">Support Role</label>
                                <select class="form-select" id="support_role_id" name="support_role_id" required>
                                    <option value="">Select a role...</option>
                                </select>
                                <div class="form-text">
                                    Role that can manage verification tickets
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="paper_text" class="form-label">Paper Text</label>
                                <input type="text" class="form-control" id="paper_text" name="paper_text" 
                                       value="{{ gender_verification_settings.paper_text if gender_verification_settings else '' }}" 
                                       placeholder="e.g., /ryzuo" required>
                                <div class="form-text">
                                    Text that users must write on paper for verification photos
                                </div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Save Configuration
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                {% if gender_verification_settings %}
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">Current Configuration</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Channel ID:</strong>
                                <br><code>{{ gender_verification_settings.channel_id }}</code>
                            </div>
                            <div class="col-md-6">
                                <strong>Category ID:</strong>
                                <br><code>{{ gender_verification_settings.category_id }}</code>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Support Role ID:</strong>
                                <br><code>{{ gender_verification_settings.support_role_id }}</code>
                            </div>
                            <div class="col-md-6">
                                <strong>Paper Text:</strong>
                                <br><code>{{ gender_verification_settings.paper_text }}</code>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>

            <div class="col-lg-4">
                <div class="feature-card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>How It Works</h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-2">The gender verification system:</p>
                        <ul class="mb-0">
                            <li>Posts verification message in channel</li>
                            <li>Users click button to start verification</li>
                            <li>Creates private ticket for each user</li>
                            <li>Users submit verification photos</li>
                            <li>Support team reviews and approves</li>
                            <li>Manual verification with ticket support</li>
                        </ul>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-camera me-2"></i>Verification Process</h6>
                    </div>
                    <div class="card-body">
                        <ol class="mb-0">
                            <li>User clicks verification button</li>
                            <li>Private ticket channel is created</li>
                            <li>User submits photo with paper text</li>
                            <li>Support team reviews submission</li>
                            <li>Ticket is closed when verified</li>
                        </ol>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-shield-alt me-2"></i>Security Features</h6>
                    </div>
                    <div class="card-body">
                        <ul class="mb-0">
                            <li>Private ticket channels</li>
                            <li>Manual review process</li>
                            <li>Custom paper text requirement</li>
                            <li>Support team oversight</li>
                            <li>Secure verification workflow</li>
                        </ul>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-cogs me-2"></i>Requirements</h6>
                    </div>
                    <div class="card-body">
                        <ul class="mb-0">
                            <li>Bot needs manage channels permission</li>
                            <li>Support role should have ticket access</li>
                            <li>Category should have proper permissions</li>
                            <li>Channel should allow bot to post</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Load Discord data for dropdowns
document.addEventListener('DOMContentLoaded', function() {
    loadChannels();
    loadCategories();
    loadRoles();
});

function loadChannels() {
    fetch('{{ url_for('api_channels') }}')
        .then(response => response.json())
        .then(channels => {
            const channelSelect = document.getElementById('channel_id');
            channelSelect.innerHTML = '<option value="">Select a channel...</option>';
            
            // Filter to text channels only
            const textChannels = channels.filter(channel => channel.type === 'text');
            
            // Group by category
            const categorized = {};
            textChannels.forEach(channel => {
                const category = channel.category || 'No Category';
                if (!categorized[category]) {
                    categorized[category] = [];
                }
                categorized[category].push(channel);
            });
            
            // Add channels grouped by category
            Object.keys(categorized).sort().forEach(category => {
                if (category !== 'No Category') {
                    const optgroup = document.createElement('optgroup');
                    optgroup.label = category;
                    channelSelect.appendChild(optgroup);
                    
                    categorized[category].forEach(channel => {
                        const option = document.createElement('option');
                        option.value = channel.id;
                        option.textContent = `# ${channel.name}`;
                        
                        // Select current channel if editing
                        {% if gender_verification_settings and gender_verification_settings.channel_id %}
                        if (channel.id === '{{ gender_verification_settings.channel_id }}') {
                            option.selected = true;
                        }
                        {% endif %}
                        
                        optgroup.appendChild(option);
                    });
                }
            });
            
            // Add uncategorized channels
            if (categorized['No Category']) {
                categorized['No Category'].forEach(channel => {
                    const option = document.createElement('option');
                    option.value = channel.id;
                    option.textContent = `# ${channel.name}`;
                    
                    // Select current channel if editing
                    {% if gender_verification_settings and gender_verification_settings.channel_id %}
                    if (channel.id === '{{ gender_verification_settings.channel_id }}') {
                        option.selected = true;
                    }
                    {% endif %}
                    
                    channelSelect.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('Error loading channels:', error);
        });
}

function loadCategories() {
    fetch('{{ url_for('api_channels') }}')
        .then(response => response.json())
        .then(channels => {
            const categorySelect = document.getElementById('category_id');
            categorySelect.innerHTML = '<option value="">Select a category...</option>';
            
            // Filter to category channels only
            const categories = channels.filter(channel => channel.type === 'category');
            
            categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                
                // Select current category if editing
                {% if gender_verification_settings and gender_verification_settings.category_id %}
                if (category.id === '{{ gender_verification_settings.category_id }}') {
                    option.selected = true;
                }
                {% endif %}
                
                categorySelect.appendChild(option);
            });
        })
        .catch(error => {
            console.error('Error loading categories:', error);
        });
}

function loadRoles() {
    fetch('{{ url_for('api_roles') }}')
        .then(response => response.json())
        .then(roles => {
            const roleSelect = document.getElementById('support_role_id');
            roleSelect.innerHTML = '<option value="">Select a role...</option>';
            
            roles.forEach(role => {
                const option = document.createElement('option');
                option.value = role.id;
                option.textContent = role.name;
                
                // Select current role if editing
                {% if gender_verification_settings and gender_verification_settings.support_role_id %}
                if (role.id === '{{ gender_verification_settings.support_role_id }}') {
                    option.selected = true;
                }
                {% endif %}
                
                roleSelect.appendChild(option);
            });
        })
        .catch(error => {
            console.error('Error loading roles:', error);
        });
}
</script>
{% endblock %}
