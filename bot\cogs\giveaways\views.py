"""
Views for Giveaways System
Contains Discord UI components for giveaway management
"""

import discord
from discord import ui, ButtonStyle, Interaction
import logging
from datetime import datetime, timezone
from database import DatabaseManager

logger = logging.getLogger(__name__)

class GiveawayView(ui.View):
    """View for giveaway entry button"""

    def __init__(self, giveaway_id: str):
        super().__init__(timeout=None)
        self.giveaway_id = giveaway_id

    @ui.button(emoji="🎉", style=ButtonStyle.primary, custom_id="giveaway_enter")
    async def enter_giveaway(self, interaction: discord.Interaction, button: ui.Button):
        """Handle giveaway entry"""
        try:
            # Get giveaway data
            from main import db
            giveaway = db.get_giveaway(self.giveaway_id)
            
            if not giveaway:
                logger.warning(f"Giveaway {self.giveaway_id} not found")
                await interaction.response.send_message("❌ This giveaway no longer exists.", ephemeral=True)
                return

            if not giveaway.get('active') or giveaway.get('ended'):
                await interaction.response.send_message("❌ This giveaway has ended.", ephemeral=True)
                return

            # Check if giveaway has expired
            if not giveaway.get('end_time'):
                await interaction.response.send_message("❌ This giveaway has an invalid end time.", ephemeral=True)
                return

            current_time = datetime.now(timezone.utc)
            if current_time >= giveaway['end_time']:
                await interaction.response.send_message("❌ This giveaway has expired.", ephemeral=True)
                return

            # Check if user is already entered
            user_id = interaction.user.id
            entries = giveaway.get('entries', [])

            if user_id in entries:
                await interaction.response.send_message("❌ You are already entered in this giveaway!", ephemeral=True)
                return

            # Add user to giveaway
            success = db.add_giveaway_entry(self.giveaway_id, user_id)
            if success:
                await interaction.response.send_message("✅ You have been entered into the giveaway! Good luck!", ephemeral=True)
                
                # Update the giveaway message with new entry count
                try:
                    from main import update_giveaway_message
                    await update_giveaway_message(self.giveaway_id)
                except Exception as e:
                    logger.error(f"Error updating giveaway message after entry: {e}")
            else:
                await interaction.response.send_message("❌ Failed to enter the giveaway. Please try again.", ephemeral=True)

        except Exception as e:
            logger.error(f"Error in giveaway entry: {e}")
            await interaction.response.send_message("❌ An error occurred while entering the giveaway.", ephemeral=True)


class PersistentGiveawayView(ui.View):
    """Persistent view for giveaway entry buttons that works across bot restarts"""

    def __init__(self, db: DatabaseManager):
        super().__init__(timeout=None)
        self.db = db

    @ui.button(emoji="🎉", style=ButtonStyle.primary, custom_id="giveaway_enter")
    async def enter_giveaway(self, interaction: discord.Interaction, button: ui.Button):
        """Handle giveaway entry for persistent views"""
        await self._handle_giveaway_interaction(interaction)

    async def _handle_giveaway_interaction(self, interaction: discord.Interaction):
        """Handle giveaway interaction for persistent views"""
        try:
            # Get the giveaway ID from the message embed footer
            if not interaction.message.embeds:
                await interaction.response.send_message("❌ This giveaway message is invalid.", ephemeral=True)
                return

            embed = interaction.message.embeds[0]
            footer_text = embed.footer.text if embed.footer else ""
            
            # Extract giveaway ID from footer (format: "MM/DD/YYYY | ID: giveaway_id | .gg/ryzuo")
            giveaway_id = None
            if "ID:" in footer_text:
                try:
                    id_part = footer_text.split("ID:")[1].split("|")[0].strip()
                    giveaway_id = id_part
                except:
                    pass

            if not giveaway_id:
                await interaction.response.send_message("❌ Could not find giveaway ID.", ephemeral=True)
                return

            # Get giveaway data
            giveaway = self.db.get_giveaway(giveaway_id)
            
            if not giveaway:
                logger.warning(f"Giveaway {giveaway_id} not found")
                await interaction.response.send_message("❌ This giveaway no longer exists.", ephemeral=True)
                return

            if not giveaway.get('active') or giveaway.get('ended'):
                await interaction.response.send_message("❌ This giveaway has ended.", ephemeral=True)
                return

            # Check if giveaway has expired
            if not giveaway.get('end_time'):
                await interaction.response.send_message("❌ This giveaway has an invalid end time.", ephemeral=True)
                return

            current_time = datetime.now(timezone.utc)
            if current_time >= giveaway['end_time']:
                await interaction.response.send_message("❌ This giveaway has expired.", ephemeral=True)
                return

            # Check if user is already entered
            user_id = interaction.user.id
            entries = giveaway.get('entries', [])

            if user_id in entries:
                await interaction.response.send_message("❌ You are already entered in this giveaway!", ephemeral=True)
                return

            # Add user to giveaway
            success = self.db.add_giveaway_entry(giveaway_id, user_id)
            if success:
                await interaction.response.send_message("✅ You have been entered into the giveaway! Good luck!", ephemeral=True)
                
                # Update the giveaway message with new entry count
                try:
                    # Get updated giveaway data
                    updated_giveaway = self.db.get_giveaway(giveaway_id)
                    if updated_giveaway:
                        # Import the embed creation function
                        from .giveaways import GiveawaysSystem
                        
                        # Create a temporary instance to access the method
                        temp_system = GiveawaysSystem(interaction.client)
                        updated_embed = temp_system.create_giveaway_embed(updated_giveaway)
                        
                        # Update the message
                        await interaction.message.edit(embed=updated_embed, view=self)
                        logger.info(f"Updated giveaway message after entry by {interaction.user}")
                        
                except Exception as e:
                    logger.error(f"Error updating giveaway message after entry: {e}")
            else:
                await interaction.response.send_message("❌ Failed to enter the giveaway. Please try again.", ephemeral=True)

        except Exception as e:
            logger.error(f"Error in persistent giveaway entry: {e}")
            if not interaction.response.is_done():
                await interaction.response.send_message("❌ An error occurred while entering the giveaway.", ephemeral=True)
