{% extends "admin/base.html" %}

{% block title %}Security & Blocking{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-shield-alt me-2"></i>
        Security & Blocking Management
    </h1>
    <div>
        <button class="btn btn-admin" onclick="showBlockIPModal()">
            <i class="fas fa-ban me-2"></i>
            Block IP
        </button>
        <button class="btn btn-admin" onclick="showBlockCommandsModal()">
            <i class="fas fa-terminal me-2"></i>
            Block Commands
        </button>
    </div>
</div>

<!-- Blocked IPs -->
<div class="card admin-card mb-4">
    <div class="card-header admin-card-header">
        <h5 class="mb-0">
            <i class="fas fa-ban me-2"></i>
            Blocked IP Addresses ({{ blocked_ips|length }})
        </h5>
    </div>
    <div class="card-body">
        {% if blocked_ips %}
        <div class="table-responsive">
            <table class="table table-admin">
                <thead>
                    <tr>
                        <th>IP Address</th>
                        <th>Reason</th>
                        <th>Blocked By</th>
                        <th>Blocked At</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for ip in blocked_ips %}
                    <tr id="ip-{{ loop.index }}">
                        <td><code>{{ ip.ip_address if ip.ip_address else ip }}</code></td>
                        <td>{{ ip.reason if ip.reason else 'No reason provided' }}</td>
                        <td>{{ ip.blocked_by if ip.blocked_by else 'Unknown' }}</td>
                        <td>
                            <small class="text-muted">
                                {{ ip.blocked_at.strftime('%Y-%m-%d %H:%M:%S') if ip.blocked_at else 'Unknown' }}
                            </small>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-outline-success" 
                                    onclick="unblockIP('{{ ip.ip_address if ip.ip_address else ip }}')">
                                <i class="fas fa-unlock me-1"></i>
                                Unblock
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-4">
            <i class="fas fa-shield-alt fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No blocked IP addresses</h5>
            <p class="text-muted">IP addresses blocked from accessing the dashboard will appear here</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Blocked Commands -->
<div class="card admin-card">
    <div class="card-header admin-card-header">
        <h5 class="mb-0">
            <i class="fas fa-terminal me-2"></i>
            Users with Blocked Commands ({{ blocked_users|length }})
        </h5>
    </div>
    <div class="card-body">
        {% if blocked_users %}
        <div class="table-responsive">
            <table class="table table-admin">
                <thead>
                    <tr>
                        <th>User ID</th>
                        <th>Blocked Commands</th>
                        <th>Reason</th>
                        <th>Blocked By</th>
                        <th>Blocked At</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in blocked_users %}
                    <tr id="user-commands-{{ user.user_id }}">
                        <td><code>{{ user.user_id }}</code></td>
                        <td>
                            {% for command in user.blocked_commands %}
                            <span class="badge bg-danger me-1">{{ command }}</span>
                            {% endfor %}
                        </td>
                        <td>{{ user.reason or 'No reason provided' }}</td>
                        <td>{{ user.blocked_by or 'Unknown' }}</td>
                        <td>
                            <small class="text-muted">
                                {{ user.blocked_at.strftime('%Y-%m-%d %H:%M:%S') if user.blocked_at else 'Unknown' }}
                            </small>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-outline-success" 
                                    onclick="unblockCommands('{{ user.user_id }}')">
                                <i class="fas fa-unlock me-1"></i>
                                Unblock
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-4">
            <i class="fas fa-terminal fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No users with blocked commands</h5>
            <p class="text-muted">Users with command restrictions will appear here</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Block IP Modal -->
<div class="modal fade" id="blockIPModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-ban me-2"></i>
                    Block IP Address
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="blockIPForm">
                    <div class="mb-3">
                        <label for="ipAddress" class="form-label">IP Address</label>
                        <input type="text" class="form-control" id="ipAddress" required 
                               placeholder="***********">
                        <div class="form-text">Enter the IP address to block from accessing the dashboard</div>
                    </div>
                    <div class="mb-3">
                        <label for="ipReason" class="form-label">Reason</label>
                        <textarea class="form-control" id="ipReason" rows="3" required 
                                  placeholder="Reason for blocking this IP address"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-admin-danger" onclick="submitBlockIP()">Block IP</button>
            </div>
        </div>
    </div>
</div>

<!-- Block Commands Modal -->
<div class="modal fade" id="blockCommandsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-terminal me-2"></i>
                    Block User Commands
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="blockCommandsForm">
                    <div class="mb-3">
                        <label for="commandUserId" class="form-label">User ID</label>
                        <input type="text" class="form-control" id="commandUserId" required 
                               placeholder="Discord User ID">
                    </div>
                    <div class="mb-3">
                        <label for="commandsList" class="form-label">Commands to Block</label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="vent" id="cmd_vent">
                                    <label class="form-check-label" for="cmd_vent">Vent Commands</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="rep" id="cmd_rep">
                                    <label class="form-check-label" for="cmd_rep">Rep Commands</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="music" id="cmd_music">
                                    <label class="form-check-label" for="cmd_music">Music Commands</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="giveaway" id="cmd_giveaway">
                                    <label class="form-check-label" for="cmd_giveaway">Giveaway Commands</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="tempvoice" id="cmd_tempvoice">
                                    <label class="form-check-label" for="cmd_tempvoice">TempVoice Commands</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="dm-support" id="cmd_dm_support">
                                    <label class="form-check-label" for="cmd_dm_support">DM Support Commands</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="stick" id="cmd_stick">
                                    <label class="form-check-label" for="cmd_stick">Sticky Commands</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="all" id="cmd_all">
                                    <label class="form-check-label" for="cmd_all">All Commands</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="commandReason" class="form-label">Reason</label>
                        <textarea class="form-control" id="commandReason" rows="3" required 
                                  placeholder="Reason for blocking these commands"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-admin-danger" onclick="submitBlockCommands()">Block Commands</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function showBlockIPModal() {
    new bootstrap.Modal(document.getElementById('blockIPModal')).show();
}

function showBlockCommandsModal() {
    new bootstrap.Modal(document.getElementById('blockCommandsModal')).show();
}

function submitBlockIP() {
    const ipAddress = document.getElementById('ipAddress').value;
    const reason = document.getElementById('ipReason').value;
    
    if (!ipAddress || !reason) {
        alert('Please fill in all fields');
        return;
    }
    
    fetch('/api/admin/block-ip', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        },
        body: JSON.stringify({
            ip_address: ipAddress,
            reason: reason
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            bootstrap.Modal.getInstance(document.getElementById('blockIPModal')).hide();
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while blocking IP');
    });
}

function unblockIP(ipAddress) {
    if (!confirm(`Are you sure you want to unblock IP ${ipAddress}?`)) {
        return;
    }
    
    fetch('/api/admin/unblock-ip', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        },
        body: JSON.stringify({
            ip_address: ipAddress
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while unblocking IP');
    });
}

function submitBlockCommands() {
    const userId = document.getElementById('commandUserId').value;
    const reason = document.getElementById('commandReason').value;
    
    // Get selected commands
    const commands = [];
    document.querySelectorAll('#blockCommandsForm input[type="checkbox"]:checked').forEach(checkbox => {
        commands.push(checkbox.value);
    });
    
    if (!userId || !reason || commands.length === 0) {
        alert('Please fill in all fields and select at least one command');
        return;
    }
    
    fetch('/api/admin/block-commands', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        },
        body: JSON.stringify({
            user_id: userId,
            commands: commands,
            reason: reason
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            bootstrap.Modal.getInstance(document.getElementById('blockCommandsModal')).hide();
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while blocking commands');
    });
}

function unblockCommands(userId) {
    if (!confirm(`Are you sure you want to unblock all commands for user ${userId}?`)) {
        return;
    }
    
    fetch('/api/admin/unblock-commands', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        },
        body: JSON.stringify({
            user_id: userId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById(`user-commands-${userId}`).remove();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while unblocking commands');
    });
}
</script>
{% endblock %}
