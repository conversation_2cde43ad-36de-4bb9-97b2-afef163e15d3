{% extends "admin/base.html" %}

{% block title %}Process Management{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-microchip me-2"></i>
        Process Management
    </h1>
    <div>
        <button class="btn btn-admin" onclick="refreshProcesses()">
            <i class="fas fa-sync me-2"></i>
            Refresh
        </button>
        <button class="btn btn-admin-danger" onclick="restartAllShards()">
            <i class="fas fa-power-off me-2"></i>
            Restart All Shards
        </button>
    </div>
</div>

<!-- System Overview -->
<div class="row mb-4">
    <div class="col-md-3 col-sm-6">
        <div class="stat-card">
            <div class="stat-number">{{ processes.shards|length or 0 }}</div>
            <div class="stat-label">
                <i class="fas fa-layer-group me-1"></i>
                Total Shards
            </div>
        </div>
    </div>
    <div class="col-md-3 col-sm-6">
        <div class="stat-card success">
            <div class="stat-number">{{ online_shards or 0 }}</div>
            <div class="stat-label">
                <i class="fas fa-check-circle me-1"></i>
                Online Shards
            </div>
        </div>
    </div>
    <div class="col-md-3 col-sm-6">
        <div class="stat-card warning">
            <div class="stat-number">{{ total_guilds or 0 }}</div>
            <div class="stat-label">
                <i class="fas fa-server me-1"></i>
                Total Guilds
            </div>
        </div>
    </div>
    <div class="col-md-3 col-sm-6">
        <div class="stat-card danger">
            <div class="stat-number">{{ total_users or 0 }}</div>
            <div class="stat-label">
                <i class="fas fa-users me-1"></i>
                Total Users
            </div>
        </div>
    </div>
</div>

<!-- Website Process -->
<div class="card admin-card mb-4">
    <div class="card-header admin-card-header">
        <h5 class="mb-0">
            <i class="fas fa-globe me-2"></i>
            Website Dashboard
        </h5>
    </div>
    <div class="card-body">
        {% if processes.website %}
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="d-flex align-items-center">
                    <span class="status-badge {{ 'status-active' if processes.website.status == 'online' else 'status-disabled' }}">
                        <i class="fas fa-{{ 'check-circle' if processes.website.status == 'online' else 'times-circle' }} me-1"></i>
                        {{ processes.website.status.title() }}
                    </span>
                    <div class="ms-3">
                        <div><strong>Uptime:</strong> {{ processes.website.uptime_formatted or 'Unknown' }}</div>
                        <div><strong>Last Heartbeat:</strong> {{ processes.website.last_heartbeat_formatted or 'Never' }}</div>
                    </div>
                </div>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-sm btn-outline-info me-2" onclick="viewProcessConsole('website')">
                    <i class="fas fa-terminal me-1"></i>
                    Console
                </button>
                <button class="btn btn-sm btn-outline-warning" onclick="restartProcess('website')">
                    <i class="fas fa-redo me-1"></i>
                    Restart
                </button>
            </div>
        </div>
        {% else %}
        <div class="text-center text-muted">
            <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
            <p>Website status unavailable</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Bot Shards -->
<div class="card admin-card">
    <div class="card-header admin-card-header">
        <h5 class="mb-0">
            <i class="fas fa-layer-group me-2"></i>
            Discord Bot Shards
        </h5>
    </div>
    <div class="card-body p-0">
        {% if processes.shards %}
        <div class="table-responsive">
            <table class="table table-admin mb-0">
                <thead>
                    <tr>
                        <th>Shard ID</th>
                        <th>Status</th>
                        <th>Guilds</th>
                        <th>Users</th>
                        <th>Uptime</th>
                        <th>Last Heartbeat</th>
                        <th>Memory</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for shard in processes.shards %}
                    <tr id="shard-{{ shard.shard_id }}">
                        <td>
                            <span class="badge bg-primary">Shard {{ shard.shard_id }}</span>
                        </td>
                        <td>
                            <span class="status-badge {{ 'status-active' if shard.status == 'online' else 'status-disabled' }}">
                                <i class="fas fa-{{ 'check-circle' if shard.status == 'online' else 'times-circle' }} me-1"></i>
                                {{ shard.status.title() }}
                            </span>
                        </td>
                        <td>
                            <span class="badge bg-info">{{ shard.guild_count or 0 }}</span>
                        </td>
                        <td>
                            <span class="badge bg-success">{{ shard.user_count or 0 }}</span>
                        </td>
                        <td>
                            <small class="text-muted">{{ shard.uptime_formatted or 'Unknown' }}</small>
                        </td>
                        <td>
                            <small class="text-muted">{{ shard.last_heartbeat_formatted or 'Never' }}</small>
                        </td>
                        <td>
                            <small class="text-muted">{{ shard.memory_usage or 'Unknown' }} MB</small>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <button class="btn btn-sm btn-outline-info" 
                                        onclick="viewProcessConsole('shard_{{ shard.shard_id }}')">
                                    <i class="fas fa-terminal"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-success" 
                                        onclick="startShard('{{ shard.shard_id }}')"
                                        {{ 'disabled' if shard.status == 'online' else '' }}>
                                    <i class="fas fa-play"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-warning" 
                                        onclick="restartShard('{{ shard.shard_id }}')">
                                    <i class="fas fa-redo"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" 
                                        onclick="stopShard('{{ shard.shard_id }}')"
                                        {{ 'disabled' if shard.status == 'offline' else '' }}>
                                    <i class="fas fa-stop"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-layer-group fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No shards detected</h5>
            <p class="text-muted">Shard information will appear here when the bot is running</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Console Modal -->
<div class="modal fade" id="consoleModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-terminal me-2"></i>
                    <span id="consoleTitle">Process Console</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <button class="btn btn-sm btn-outline-secondary" onclick="refreshConsole()">
                            <i class="fas fa-sync me-1"></i>
                            Refresh
                        </button>
                        <button class="btn btn-sm btn-outline-warning" onclick="clearConsole()">
                            <i class="fas fa-trash me-1"></i>
                            Clear
                        </button>
                    </div>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="autoRefresh" checked>
                        <label class="form-check-label" for="autoRefresh">
                            Auto-refresh (5s)
                        </label>
                    </div>
                </div>
                <div class="console-container">
                    <pre id="consoleOutput" class="console-output">Loading console...</pre>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-admin" onclick="downloadLogs()">
                    <i class="fas fa-download me-1"></i>
                    Download Logs
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.console-container {
    background: #1e1e1e;
    border-radius: 8px;
    padding: 0;
    max-height: 500px;
    overflow: hidden;
}

.console-output {
    background: #1e1e1e;
    color: #00ff00;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    margin: 0;
    padding: 15px;
    max-height: 500px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.console-output::-webkit-scrollbar {
    width: 8px;
}

.console-output::-webkit-scrollbar-track {
    background: #2d2d2d;
}

.console-output::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 4px;
}

.console-output::-webkit-scrollbar-thumb:hover {
    background: #777;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let currentProcess = null;
let autoRefreshInterval = null;

function refreshProcesses() {
    location.reload();
}

function viewProcessConsole(processName) {
    currentProcess = processName;
    document.getElementById('consoleTitle').textContent = `${processName.replace('_', ' ').toUpperCase()} Console`;
    document.getElementById('consoleOutput').textContent = 'Loading console...';
    
    const modal = new bootstrap.Modal(document.getElementById('consoleModal'));
    modal.show();
    
    refreshConsole();
    startAutoRefresh();
}

function refreshConsole() {
    if (!currentProcess) return;
    
    fetch(`/api/admin/process/${currentProcess}/console`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('consoleOutput').textContent = data.logs.join('\n') || 'No logs available';
            // Auto-scroll to bottom
            const consoleOutput = document.getElementById('consoleOutput');
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        } else {
            document.getElementById('consoleOutput').textContent = 'Error loading console: ' + data.error;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        document.getElementById('consoleOutput').textContent = 'Error loading console';
    });
}

function clearConsole() {
    document.getElementById('consoleOutput').textContent = '';
}

function startAutoRefresh() {
    stopAutoRefresh();
    const checkbox = document.getElementById('autoRefresh');
    
    if (checkbox.checked) {
        autoRefreshInterval = setInterval(refreshConsole, 5000);
    }
}

function stopAutoRefresh() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
        autoRefreshInterval = null;
    }
}

function downloadLogs() {
    if (!currentProcess) return;
    
    fetch(`/api/admin/process/${currentProcess}/logs/download`)
    .then(response => response.blob())
    .then(blob => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `${currentProcess}_logs.txt`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error downloading logs');
    });
}

function restartProcess(processName) {
    if (!confirm(`Are you sure you want to restart ${processName}?`)) {
        return;
    }
    
    fetch(`/api/admin/process/${processName}/restart`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(`${processName} restart requested`);
            setTimeout(() => location.reload(), 2000);
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while restarting process');
    });
}

function startShard(shardId) {
    fetch(`/api/admin/shard/${shardId}/start`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(`Shard ${shardId} start requested`);
            setTimeout(() => location.reload(), 2000);
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while starting shard');
    });
}

function restartShard(shardId) {
    if (!confirm(`Are you sure you want to restart Shard ${shardId}?`)) {
        return;
    }
    
    fetch(`/api/admin/shard/${shardId}/restart`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(`Shard ${shardId} restart requested`);
            setTimeout(() => location.reload(), 2000);
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while restarting shard');
    });
}

function stopShard(shardId) {
    if (!confirm(`Are you sure you want to stop Shard ${shardId}?`)) {
        return;
    }
    
    fetch(`/api/admin/shard/${shardId}/stop`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(`Shard ${shardId} stop requested`);
            setTimeout(() => location.reload(), 2000);
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while stopping shard');
    });
}

function restartAllShards() {
    if (!confirm('Are you sure you want to restart ALL shards? This will cause temporary service disruption.')) {
        return;
    }
    
    fetch('/api/admin/shards/restart-all', {
        method: 'POST',
        headers: {
            'X-CSRFToken': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('All shards restart requested');
            setTimeout(() => location.reload(), 5000);
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while restarting all shards');
    });
}

// Handle auto-refresh toggle
document.getElementById('autoRefresh').addEventListener('change', function() {
    if (this.checked) {
        startAutoRefresh();
    } else {
        stopAutoRefresh();
    }
});

// Clean up intervals when modal is closed
document.getElementById('consoleModal').addEventListener('hidden.bs.modal', function() {
    stopAutoRefresh();
    currentProcess = null;
});
</script>
{% endblock %}