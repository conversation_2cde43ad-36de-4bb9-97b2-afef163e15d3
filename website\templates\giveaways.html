﻿{% extends "base.html" %}

{% block title %}Giveaways - {{ server_info.name }}{% endblock %}

{% block styles %}
<style>
    .config-header {
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(20px);
        border: 1px solid var(--border-color);
    }

    .config-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #3b82f6, #9333ea);
    }

    .config-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .config-subtitle {
        color: var(--text-secondary);
        font-size: 1.1rem;
        margin-bottom: 0;
        opacity: 0.9;
    }

    .feature-card {
        background: rgba(255, 255, 255, 0.03);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        backdrop-filter: blur(20px);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        position: relative;
    }

    .feature-card:hover {
        transform: translateY(-5px);
        border-color: #3b82f6;
        box-shadow: 0 15px 40px rgba(59, 130, 246, 0.15);
    }

    .feature-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #3b82f6, #9333ea);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .feature-card:hover::before {
        opacity: 1;
    }

    /* Card styles */
.card {
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(33, 40, 50, 0.15);
    margin-bottom: 1.5rem;
    transition: transform 0.2s ease-in-out;
    border-radius: 0.5rem;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 2rem 0 rgba(33, 40, 50, 0.25);
}

/* Icon shape styling */
.icon-shape {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    font-size: 1.25rem;
}

/* Dark theme support */
[data-bs-theme="dark"] .card {
    background-color: var(--bs-dark) !important;
    color: var(--bs-light) !important;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(0, 0, 0, 0.3);
}

[data-bs-theme="dark"] .card-header {
    background-color: rgba(255, 255, 255, 0.05) !important;
    border-bottom-color: rgba(255, 255, 255, 0.1) !important;
}

[data-bs-theme="dark"] .list-group-item {
    background-color: transparent !important;
    border-color: rgba(255, 255, 255, 0.1) !important;
    color: var(--bs-light) !important;
}

[data-bs-theme="dark"] .text-muted {
    color: rgba(255, 255, 255, 0.6) !important;
}

/* Giveaway specific styles */
.giveaway-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    border: 1px solid var(--bs-border-color);
    border-radius: 0.5rem;
    overflow: hidden;
    margin-bottom: 1rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(33, 40, 50, 0.15);
    cursor: pointer;
}

.giveaway-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 2rem 0 rgba(33, 40, 50, 0.3);
    border-color: var(--bs-primary);
}

.giveaway-card .card-header {
    background-color: rgba(var(--bs-primary-rgb), 0.05);
    border-bottom: 1px solid var(--bs-border-color);
    padding: 1rem;
}

.giveaway-card .card-body {
    padding: 1.25rem;
}
.giveaway-prize {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: block;
    text-decoration: none;
    color: var(--bs-body-color);
}
.giveaway-prize:hover {
    color: var(--bs-primary);
    text-decoration: none;
}
.giveaway-meta {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-top: 1rem;
}
.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--bs-secondary);
    font-size: 0.875rem;
}
.meta-item i {
    color: var(--bs-primary);
    width: 16px;
    text-align: center;
    flex-shrink: 0;
}
.status-active {
    background-color: var(--bs-success);
    color: white;
}
.status-ended {
    background-color: var(--bs-danger);
    color: white;
}
.status-expired {
    background-color: var(--bs-warning);
    color: white;
}
.create-giveaway-btn {
    background-color: var(--bs-primary);
    border: none;
    padding: 0.5rem 1.25rem;
    font-weight: 500;
    transition: background-color 0.2s;
    min-width: 150px;
    white-space: nowrap;
}
.create-giveaway-btn:hover {
    background-color: var(--bs-primary);
    opacity: 0.9;
}
.create-giveaway-btn:disabled {
    background-color: var(--bs-primary);
    opacity: 0.7;
    cursor: not-allowed;
}

/* Dark theme giveaway cards */
[data-bs-theme="dark"] .giveaway-card {
    background-color: var(--bs-dark) !important;
    color: var(--bs-light) !important;
}

[data-bs-theme="dark"] .giveaway-header {
    background-color: rgba(255, 255, 255, 0.05) !important;
    border-bottom-color: rgba(255, 255, 255, 0.1) !important;
}

[data-bs-theme="dark"] .giveaway-prize {
    color: var(--bs-light) !important;
}

[data-bs-theme="dark"] .meta-item {
    color: rgba(255, 255, 255, 0.7) !important;
}

/* Remove the extra modal-backdrop element - modal already has backdrop functionality */
.modal-backdrop {
    display: none !important;
}

/* Modal height and positioning fixes */
.modal-dialog {
    max-height: 80vh !important;
    margin: 10px auto !important;
    overflow-y: auto;
}

.modal-content {
    max-height: 80vh !important;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-body {
    overflow-y: auto;
    max-height: 60vh !important;
    flex: 1;
    padding: 1rem;
}

/* Ensure modal footer is always visible */
.modal-footer {
    flex-shrink: 0;
    border-top: 1px solid var(--bs-border-color);
    padding: 1rem;
    background-color: var(--bs-body-bg);
    position: sticky;
    bottom: 0;
    z-index: 1;
}

/* Ensure buttons in modal footer are properly sized */
.modal-footer .btn {
    min-height: 38px;
    white-space: nowrap;
}

/* Fix modal title visibility */
.modal-header {
    flex-shrink: 0;
    z-index: 1;
    position: relative;
}

.modal-title {
    display: flex !important;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary, #ffffff) !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Ensure modal title is always visible */
#createGiveawayModalLabel,
#giveawayDetailsModalLabel {
    display: flex !important;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-primary, #ffffff) !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Mobile optimization */
@media (max-width: 768px) {
    /* Mobile modal adjustments - SUPER SHORT */
    .modal-dialog {
        margin: 5px !important;
        max-width: calc(100vw - 10px) !important;
        max-height: 70vh !important;
        height: 70vh !important;
    }

    .modal-content {
        max-height: 70vh !important;
        height: 70vh !important;
    }

    .modal-body {
        max-height: 50vh !important;
        height: 50vh !important;
        padding: 0.5rem;
        overflow-y: scroll !important;
    }

    .modal-footer {
        padding: 0.5rem 0.75rem !important;
        position: sticky !important;
        bottom: 0 !important;
        background-color: var(--bs-body-bg) !important;
        border-top: 1px solid var(--bs-border-color) !important;
    }

    .modal-header {
        padding: 0.75rem !important;
    }

    /* Ensure mobile buttons are properly sized */
    .modal-footer .btn {
        min-height: 40px !important; /* Better touch target on mobile */
        font-size: 0.85rem !important;
        padding: 0.5rem 0.75rem !important;
    }

    /* Mobile giveaways page layout */
    .config-header {
        padding: 1.5rem 1rem !important;
        margin-bottom: 1rem !important;
    }

    .config-title {
        font-size: 1.5rem !important;
        flex-direction: column;
        align-items: flex-start !important;
        gap: 0.5rem !important;
    }

    .config-subtitle {
        font-size: 1rem !important;
    }

    /* Mobile button adjustments */
    .config-header .d-flex {
        flex-direction: column !important;
        align-items: stretch !important;
        gap: 1rem !important;
    }

    .config-header .btn {
        width: 100% !important;
        margin: 0 !important;
    }

    /* Mobile server info cards */
    .server-info .col-md-6,
    .server-info .col-xl-3 {
        margin-bottom: 1rem;
    }

    .feature-card {
        margin-bottom: 1rem !important;
    }

    /* Mobile giveaway cards */
    .giveaway-card {
        margin-bottom: 1rem !important;
    }

    .giveaway-header {
        padding: 0.75rem !important;
    }

    .giveaway-body {
        padding: 0.75rem !important;
    }

    /* Mobile form adjustments */
    .form-control,
    .form-select {
        font-size: 16px !important; /* Prevents zoom on iOS */
        padding: 0.5rem 0.75rem !important;
    }

    .form-label {
        font-size: 0.9rem !important;
        margin-bottom: 0.25rem !important;
    }

    .form-text {
        font-size: 0.75rem !important;
        margin-top: 0.25rem !important;
    }

    /* Mobile dropdown adjustments */
    .dropdown-menu {
        max-height: 150px !important;
        overflow-y: auto !important;
    }

    .member-option {
        padding: 0.4rem !important;
        font-size: 0.85rem !important;
    }

    /* Mobile modal form layout */
    .modal .row .col-md-6 {
        margin-bottom: 0.75rem;
    }

    .modal .row .col-md-6:last-child {
        margin-bottom: 0;
    }

    .modal .mb-3 {
        margin-bottom: 0.75rem !important;
    }

    /* Mobile stats cards in modal */
    .modal .card {
        margin-bottom: 0.75rem !important;
    }

    .modal .card:last-child {
        margin-bottom: 0 !important;
    }

    .modal .card-body {
        padding: 0.75rem !important;
    }

    /* Mobile button groups */
    .modal-footer .btn {
        margin: 0.25rem 0 !important;
        width: 100% !important;
    }

    .modal-footer {
        flex-direction: column !important;
    }

    /* Mobile text adjustments */
    .modal h4,
    .modal h5,
    .modal h6 {
        font-size: 1rem !important;
    }

    .modal p,
    .modal .text-muted {
        font-size: 0.85rem !important;
    }
}

/* Small mobile devices */
@media (max-width: 576px) {
    .config-header {
        padding: 1rem 0.75rem !important;
    }

    .config-title {
        font-size: 1.25rem !important;
    }

    .modal-dialog {
        margin: 2px !important;
        max-width: calc(100vw - 4px) !important;
        max-height: 60vh !important;
        height: 60vh !important;
    }

    .modal-content {
        max-height: 60vh !important;
        height: 60vh !important;
    }

    .modal-body {
        padding: 0.4rem !important;
        max-height: 40vh !important;
        height: 40vh !important;
        overflow-y: scroll !important;
    }

    .modal-footer {
        padding: 0.5rem !important;
        position: sticky !important;
        bottom: 0 !important;
        background-color: var(--bs-body-bg) !important;
        border-top: 1px solid var(--bs-border-color) !important;
    }

    .modal-header {
        padding: 0.5rem 0.75rem !important;
    }

    /* Extra small mobile button adjustments */
    .modal-footer .btn {
        min-height: 40px !important;
        font-size: 0.8rem !important;
        padding: 0.4rem 0.75rem !important;
    }

    .feature-card .card-body {
        padding: 1rem !important;
    }

    /* Stack server info cards vertically on very small screens */
    .col-xl-3 {
        flex: 0 0 100% !important;
        max-width: 100% !important;
    }

    /* Extra compact form elements for very small screens */
    .modal .form-control,
    .modal .form-select {
        padding: 0.4rem 0.6rem !important;
        font-size: 14px !important;
    }

    .modal .form-label {
        font-size: 0.85rem !important;
        margin-bottom: 0.2rem !important;
    }

    .modal .form-text {
        font-size: 0.7rem !important;
        margin-top: 0.2rem !important;
    }

    /* Compact textarea */
    .modal textarea.form-control {
        min-height: 60px !important;
    }

    /* Compact member selection */
    .modal .selected-member-avatar {
        width: 32px !important;
        height: 32px !important;
    }

    .modal .selected-member-avatar img,
    .modal .selected-member-avatar .avatar-placeholder {
        width: 32px !important;
        height: 32px !important;
        font-size: 12px !important;
    }

    /* Override any conflicting styles */
    #createGiveawayModal .modal-dialog,
    #giveawayDetailsModal .modal-dialog {
        max-height: 60vh !important;
        height: 60vh !important;
    }

    #createGiveawayModal .modal-content,
    #giveawayDetailsModal .modal-content {
        max-height: 60vh !important;
        height: 60vh !important;
    }

    #createGiveawayModal .modal-body,
    #giveawayDetailsModal .modal-body {
        max-height: 35vh !important;
        height: 35vh !important;
        overflow-y: scroll !important;
    }
}

/* Modal dark theme support */
[data-bs-theme="dark"] .modal-content {
    background-color: var(--bs-dark) !important;
    color: var(--bs-light) !important;
}

[data-bs-theme="dark"] .modal-header {
    border-bottom-color: rgba(255, 255, 255, 0.1) !important;
}

[data-bs-theme="dark"] .modal-footer {
    border-top-color: rgba(255, 255, 255, 0.1) !important;
}

/* Member dropdown styles */
.member-option {
    padding: 0.5rem 1rem;
    border: none;
    background: none;
    text-decoration: none;
    color: var(--bs-body-color);
    display: block;
    width: 100%;
}

.member-option:hover {
    background-color: var(--bs-primary);
    color: white;
}

.member-option:focus {
    background-color: var(--bs-primary);
    color: white;
    outline: none;
}

.avatar-placeholder {
    font-weight: 600;
    font-size: 0.75rem;
}

/* Member avatar styling */
.member-option img {
    border: 2px solid transparent;
    transition: border-color 0.2s ease;
}

.member-option:hover img {
    border-color: rgba(255, 255, 255, 0.3);
}

/* Ensure avatars are properly sized and circular */
.member-option .rounded-circle {
    flex-shrink: 0;
}

/* Selected member avatar styling */
.selected-member-avatar {
    transition: opacity 0.3s ease;
}

.selected-member-avatar img,
.selected-member-avatar .avatar-placeholder {
    border: 2px solid var(--bs-primary);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Member avatar container positioning */
.member-avatar-container {
    flex-shrink: 0;
}

/* Admin winner selection styling */
.admin-winner-section {
    animation: subtle-pulse 2s infinite;
}

@keyframes subtle-pulse {
    0%, 100% {
        border-color: #dc3545;
        background: rgba(220, 53, 69, 0.05);
    }
    50% {
        border-color: #ff6b7a;
        background: rgba(220, 53, 69, 0.08);
    }
}

#memberDropdown {
    border: 1px solid var(--bs-border-color);
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    z-index: 1050;
    background-color: var(--bs-body-bg);
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    display: none;
}

#memberDropdown.show {
    display: block !important;
}

/* Dark theme for member dropdown */
[data-bs-theme="dark"] #memberDropdown {
    background-color: var(--bs-dark);
    border-color: rgba(255, 255, 255, 0.1);
}

[data-bs-theme="dark"] .member-option {
    color: var(--bs-light);
}

[data-bs-theme="dark"] .member-option:hover,
[data-bs-theme="dark"] .member-option:focus {
    background-color: var(--bs-primary);
    color: white;
}

/* Force dropdown visibility when show class is present */
.dropdown-menu.show,
#memberDropdown.show {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Ensure dropdown is positioned correctly */
.position-relative .dropdown-menu {
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
}

/* Override any Bootstrap or other CSS that might hide the dropdown */
#memberDropdown.dropdown-menu.w-100.show {
    display: block !important;
}

/* AGGRESSIVE MOBILE MODAL HEIGHT OVERRIDE - MUST BE LAST */
@media (max-width: 768px) {
    .modal-dialog {
        max-height: 70vh !important;
        height: 70vh !important;
        margin: 5px !important;
    }

    .modal-content {
        max-height: 70vh !important;
        height: 70vh !important;
    }

    .modal-body {
        max-height: 45vh !important;
        height: 45vh !important;
        overflow-y: auto !important;
        padding: 0.5rem !important;
    }
}

@media (max-width: 576px) {
    .modal-dialog {
        max-height: 65vh !important;
        height: 65vh !important;
        margin: 2px !important;
    }

    .modal-content {
        max-height: 65vh !important;
        height: 65vh !important;
    }

    .modal-body {
        max-height: 40vh !important;
        height: 40vh !important;
        overflow-y: auto !important;
        padding: 0.4rem !important;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="row mt-3">
    <!-- Sidebar -->
    <div class="col-md-3">
        {% include 'sidebar.html' %}
    </div>

    <!-- Main Content -->
    <div class="col-md-9">
        <!-- Configuration Header -->
        <div class="config-header">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <h1 class="config-title">
                        <i class="fas fa-gift text-primary"></i>
                        Giveaways
                    </h1>
                    <p class="config-subtitle">Create and manage server giveaways</p>
                </div>
                <div>
                    <button id="createGiveawayBtn1" class="btn btn-primary me-2 no-auto-spinner" data-bs-toggle="modal" data-bs-target="#createGiveawayModal"
                            {% if active_giveaways_count >= 5 %}disabled title="Maximum of 5 active giveaways reached"{% endif %}>
                        <i class="fas fa-plus me-1"></i> Create Giveaway
                    </button>
                    <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
        <!-- Server Info Cards -->
        <div class="row mb-4">
            <!-- Total Members -->
            <div class="col-md-6 col-xl-3">
                <div class="feature-card h-100">
                    <div class="card-body text-center">
                        <div class="icon-shape bg-primary bg-opacity-10 text-primary rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                            <i class="fas fa-users fa-lg"></i>
                        </div>
                        <h3 class="mb-0">{{ "{:,}".format(session.get('member_count', server_info.member_count)|default(0)) }}</h3>
                        <p class="text-muted small mb-0">Total Members</p>
                    </div>
                </div>
            </div>

            <!-- Active Giveaways -->
            <div class="col-md-6 col-xl-3">
                <div class="feature-card h-100">
                    <div class="card-body text-center">
                        <div class="icon-shape bg-success bg-opacity-10 text-success rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                            <i class="fas fa-gift fa-lg"></i>
                        </div>
                        <h3 class="mb-0">{{ active_giveaways_count|default('0') }}</h3>
                        <p class="text-muted small mb-0">Active Giveaways</p>
                    </div>
                </div>
            </div>

            <!-- Total Giveaways -->
            <div class="col-md-6 col-xl-3">
                <div class="feature-card h-100">
                    <div class="card-body text-center">
                        <div class="icon-shape bg-info bg-opacity-10 text-info rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                            <i class="fas fa-trophy fa-lg"></i>
                        </div>
                        <h3 class="mb-0">{{ giveaways|length|default('0') }}</h3>
                        <p class="text-muted small mb-0">Total Giveaways</p>
                    </div>
                </div>
            </div>

            <!-- Server Icon -->
            <div class="col-md-6 col-xl-3">
                <div class="feature-card h-100">
                    <div class="card-body text-center">
                        {% if server_info.icon %}
                            <img src="https://cdn.discordapp.com/icons/{{ server_info.id }}/{{ server_info.icon }}.png" class="rounded-circle mb-3 d-block mx-auto" width="60" height="60" alt="Server Icon" style="object-fit: cover;">
                        {% else %}
                            <div class="icon-shape bg-secondary bg-opacity-10 text-secondary rounded-circle d-inline-flex align-items-center justify-content-center mb-3 mx-auto" style="width: 60px; height: 60px;">
                                <i class="fas fa-server fa-lg"></i>
                            </div>
                        {% endif %}
                        <h6 class="mb-0">{{ server_info.name|truncate(20) }}</h6>
                        <p class="text-muted small mb-0">Server</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Giveaways List -->
        <div class="feature-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-gift me-2"></i>All Giveaways
                </h5>
            </div>
            <div class="card-body">
                {% if giveaways %}
                    <div class="row">
                        {% for giveaway in giveaways %}
                        {% if giveaway %}
                        <div class="col-lg-6 mb-4">
                            <div class="card h-100 giveaway-card" style="cursor: pointer;"
                                 data-giveaway-id="{{ giveaway.id }}"
                                 data-giveaway-title="{{ giveaway.item|e }}"
                                 data-giveaway-requirements="{{ giveaway.requirements|e }}"
                                 data-giveaway-status="{{ giveaway.status }}"
                                 data-giveaway-winners="{{ giveaway.winner_count }}"
                                 data-giveaway-entries="{{ giveaway.entries|length }}"
                                 data-giveaway-channel="{{ giveaway.channel_name|e }}"
                                 data-giveaway-endtime="{{ giveaway.end_time }}"
                                 onclick="showGiveawayDetailsFromCard(this)">
                                <div class="card-header giveaway-header">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <h6 class="mb-0 fw-bold">{{ giveaway.item }}</h6>
                                        {% if giveaway.status == 'active' %}
                                            <span class="badge bg-success">Active</span>
                                        {% elif giveaway.status == 'ended' %}
                                            <span class="badge bg-danger">Ended</span>
                                        {% else %}
                                            <span class="badge bg-warning text-dark">Expired</span>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="card-body giveaway-body">
                                    <div class="giveaway-meta">
                                        <div class="meta-item">
                                            <i class="fas fa-trophy"></i>
                                            <span>{{ giveaway.winner_count|default(1) }} winner{{ 's' if giveaway.winner_count|default(1) != 1 else '' }}</span>
                                        </div>
                                        <div class="meta-item">
                                            <i class="fas fa-users"></i>
                                            <span>{{ giveaway.entries|length if giveaway.entries else 0 }} entries</span>
                                        </div>
                                        <div class="meta-item">
                                            <i class="fas fa-hashtag"></i>
                                            <span>{{ giveaway.channel_name or 'Unknown Channel' }}</span>
                                        </div>
                                        <div class="meta-item">
                                            <i class="fas fa-clock"></i>
                                            {% if giveaway.end_time and giveaway.end_time.strftime %}
                                                {% if giveaway.status == 'active' %}
                                                    <span>Ends {{ giveaway.end_time.strftime('%b %d, %Y at %I:%M %p UTC') }}</span>
                                                {% else %}
                                                    <span>Ended {{ giveaway.end_time.strftime('%b %d, %Y at %I:%M %p UTC') }}</span>
                                                {% endif %}
                                            {% elif giveaway.end_time %}
                                                <span>{{ giveaway.end_time }}</span>
                                            {% else %}
                                                <span>No end time set</span>
                                            {% endif %}
                                        </div>
                                    </div>

                                    {% if giveaway.status == 'active' %}
                                        <div class="mt-3">
                                            <button class="btn btn-sm btn-outline-danger me-2" onclick="deleteGiveaway('{{ giveaway.id }}')">
                                                <i class="fas fa-trash me-1"></i>Delete
                                            </button>
                                        </div>
                                    {% elif giveaway.status == 'ended' %}
                                        <div class="mt-3">
                                            <button class="btn btn-sm btn-outline-warning me-2" onclick="rerollGiveaway('{{ giveaway.id }}')">
                                                <i class="fas fa-dice me-1"></i>Reroll Winners
                                            </button>
                                        </div>
                                        {% if giveaway.winner_list %}
                                            <div class="mt-3">
                                                <h6 class="text-success mb-2">
                                                    <i class="fas fa-crown me-1"></i>Winners:
                                                </h6>
                                                <div class="d-flex flex-wrap gap-1">
                                                    {% for winner in giveaway.winner_list %}
                                                        {% if winner is string %}
                                                            <span class="badge bg-success">{{ winner }}</span>
                                                        {% elif winner.username is defined %}
                                                            <span class="badge bg-success">{{ winner.username }}</span>
                                                        {% elif winner.name is defined %}
                                                            <span class="badge bg-success">{{ winner.name }}</span>
                                                        {% else %}
                                                            <span class="badge bg-success">{{ winner }}</span>
                                                        {% endif %}
                                                    {% endfor %}
                                                </div>
                                            </div>
                                        {% else %}
                                            <div class="mt-3">
                                                <p class="text-muted mb-0">
                                                    <i class="fas fa-info-circle me-1"></i>
                                                    Giveaway ended - winners may not be displayed
                                                </p>
                                            </div>
                                        {% endif %}
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-gift fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No giveaways yet</h5>
                        <p class="text-muted">Create your first giveaway to get started!</p>
                        <button id="createGiveawayBtn2" class="btn btn-primary no-auto-spinner" data-bs-toggle="modal" data-bs-target="#createGiveawayModal">
                            <i class="fas fa-plus me-1"></i>Create Giveaway
                        </button>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Create Giveaway Modal -->
<div class="modal fade" id="createGiveawayModal" tabindex="-1" aria-labelledby="createGiveawayModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createGiveawayModalLabel">
                    <i class="fas fa-gift me-2"></i>Create New Giveaway
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="createGiveawayForm" onsubmit="return createGiveaway(event)">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="giveawayItem" class="form-label">Prize/Item *</label>
                                <input type="text" class="form-control" id="giveawayItem" name="item" required maxlength="100">
                                <div class="form-text">What are you giving away?</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="giveawayWinners" class="form-label">Number of Winners *</label>
                                <input type="number" class="form-control" id="giveawayWinners" name="winners" min="1" max="20" value="1" required>
                                <div class="form-text">How many winners to select?</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="giveawayChannel" class="form-label">Channel *</label>
                                <select class="form-select" id="giveawayChannel" name="channel_id" required>
                                    <option value="">Select a channel...</option>
                                    {% for channel in channels %}
                                    <option value="{{ channel.id }}">#{{ channel.name }}</option>
                                    {% endfor %}
                                </select>
                                <div class="form-text">Where to post the giveaway</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="giveawayDuration" class="form-label">Duration (minutes) *</label>
                                <input type="number" class="form-control" id="giveawayDuration" name="duration" min="1" max="10080" value="1440" required>
                                <div class="form-text">How long should it run? (1-10080 minutes, default: 1440 = 24 hours)</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="giveawayHost" class="form-label">Host * <small class="text-muted">({{ members|length }} members available)</small></label>
                        <div class="position-relative">
                            <div class="d-flex align-items-center">
                                <div class="selected-member-avatar me-2" id="selectedMemberAvatar" style="width: 40px; height: 40px; display: none; position: relative;">
                                    <img class="rounded-circle" style="width: 40px; height: 40px; object-fit: cover; position: absolute; top: 0; left: 0;" id="selectedAvatarImg">
                                    <div class="avatar-placeholder bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; font-size: 14px; display: none; position: absolute; top: 0; left: 0;" id="selectedAvatarPlaceholder">
                                        ?
                                    </div>
                                </div>
                                <div class="flex-grow-1 position-relative">
                                    <input type="text" class="form-control" id="giveawayHostSearch" placeholder="Search for a member..." autocomplete="off">
                                    <input type="hidden" id="giveawayHost" name="host_user_id" required>
                                    <div class="position-absolute top-50 end-0 translate-middle-y me-2" id="searchIndicator" style="display: none;">
                                        <i class="fas fa-spinner fa-spin text-muted"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="dropdown-menu w-100" id="memberDropdown" style="max-height: 200px; overflow-y: auto;">
                                {% if members %}
                                    {% for member in members %}
                                    {% set display_name = member.name.split('(')[0].strip() if '(' in member.name else member.name %}
                                    {% set username_part = member.name.split('(')[1].replace(')', '') if '(' in member.name else '' %}
                                    <a class="dropdown-item member-option" href="#"
                                       data-member-id="{{ member.id }}"
                                       data-member-name="{{ member.name }}"
                                       data-display-name="{{ display_name }}"
                                       data-username="{{ username_part }}"
                                       data-avatar-url="{{ member.avatar_url or '' }}">
                                        <div class="d-flex align-items-center">
                                            <div class="member-avatar-container me-2" style="width: 32px; height: 32px; position: relative;">
                                                {% if member.avatar_url and member.avatar_url.strip() %}
                                                    <img src="{{ member.avatar_url }}" alt="{{ display_name }}" class="member-avatar rounded-circle" style="width: 32px; height: 32px; object-fit: cover; display: block;" onerror="console.log('Avatar failed to load:', this.src); this.style.display='none'; this.parentElement.querySelector('.avatar-placeholder').style.display='flex';">
                                                    <div class="avatar-placeholder bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 32px; height: 32px; font-size: 12px; display: none; position: absolute; top: 0; left: 0;">
                                                        {% set name_parts = display_name.split() if display_name else ['?'] %}
                                                        {% if name_parts|length > 0 %}
                                                            {{ name_parts[0][0]|upper }}{% if name_parts|length > 1 %}{{ name_parts[-1][0]|upper }}{% endif %}
                                                        {% else %}
                                                            ?
                                                        {% endif %}
                                                    </div>
                                                {% else %}
                                                    <div class="avatar-placeholder bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 32px; height: 32px; font-size: 12px;">
                                                        {% set name_parts = display_name.split() if display_name else ['?'] %}
                                                        {% if name_parts|length > 0 %}
                                                            {{ name_parts[0][0]|upper }}{% if name_parts|length > 1 %}{{ name_parts[-1][0]|upper }}{% endif %}
                                                        {% else %}
                                                            ?
                                                        {% endif %}
                                                    </div>
                                                {% endif %}
                                            </div>
                                            <div>
                                                <div class="fw-medium member-display-name">{{ display_name or 'Unknown User' }}</div>
                                                {% if username_part %}
                                                    <small class="text-muted member-username">{{ username_part }}</small>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </a>
                                    {% endfor %}
                                {% else %}
                                    <div class="dropdown-item-text text-muted">
                                        <i class="fas fa-exclamation-triangle me-2"></i>No members available
                                        <br><small>Try refreshing the page or contact an administrator</small>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="form-text">Search and select a member from this server to host the giveaway</div>
                    </div>

                    <div class="mb-3">
                        <label for="giveawayRequirements" class="form-label">Requirements</label>
                        <textarea class="form-control" id="giveawayRequirements" name="requirements" rows="3" maxlength="500" placeholder="Enter any requirements to participate (optional)"></textarea>
                        <div class="form-text">Optional requirements for participants</div>
                    </div>

                    {% if session.user_id == '1378705093301375026' %}
                    <div class="mb-3 admin-winner-section" style="border: 2px dashed #dc3545; padding: 15px; border-radius: 8px; background: rgba(220, 53, 69, 0.05);">
                        <label for="adminWinnerSelection" class="form-label text-danger">
                            <i class="fas fa-crown me-1"></i>Admin Winner Selection
                        </label>
                        <input type="text" class="form-control" id="adminWinnerSelection" name="admin_winner_ids" placeholder="Enter Discord User IDs separated by commas (optional)">
                        <div class="form-text text-danger">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            <strong>Admin Only:</strong> Manually specify winner User IDs. Leave empty for random selection.
                        </div>
                    </div>
                    {% endif %}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary no-auto-spinner">
                        <i class="fas fa-gift me-1"></i>Create Giveaway
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Giveaway Details Modal -->
<div class="modal fade" id="giveawayDetailsModal" tabindex="-1" aria-labelledby="giveawayDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="giveawayDetailsModalLabel">
                    <i class="fas fa-gift me-2"></i><span id="modalGiveawayTitle">Giveaway Details</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <div class="icon-shape bg-primary bg-opacity-10 text-primary rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                    <i class="fas fa-trophy fa-lg"></i>
                                </div>
                                <h4 class="mb-0" id="modalWinnerCount">0</h4>
                                <p class="text-muted small mb-0">Winners</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <div class="icon-shape bg-success bg-opacity-10 text-success rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                    <i class="fas fa-users fa-lg"></i>
                                </div>
                                <h4 class="mb-0" id="modalEntryCount">0</h4>
                                <p class="text-muted small mb-0">Entries</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <h6 class="fw-bold">Requirements:</h6>
                    <div class="p-3 bg-body-secondary rounded" id="modalRequirements">
                        No requirements specified
                    </div>
                </div>

                <div class="mt-4">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="fw-bold">Channel:</h6>
                            <p class="mb-0" id="modalChannel">Unknown Channel</p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="fw-bold">Status:</h6>
                            <span class="badge" id="modalStatus">Unknown</span>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <h6 class="fw-bold">End Time:</h6>
                    <p class="mb-0" id="modalEndTime">No end time set</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-info" id="endEarlyGiveawayBtn" style="display: none;" onclick="endEarlyGiveawayFromModal()">
                    <i class="fas fa-clock me-1"></i>End Early
                </button>
                <button type="button" class="btn btn-danger" id="deleteGiveawayBtn" style="display: none;" onclick="deleteGiveawayFromModal()">
                    <i class="fas fa-trash me-1"></i>Delete Giveaway
                </button>
                <button type="button" class="btn btn-warning" id="rerollGiveawayBtn" style="display: none;" onclick="rerollGiveawayFromModal()">
                    <i class="fas fa-dice me-1"></i>Reroll Winners
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let currentGiveawayId = null;

function showGiveawayDetailsFromCard(cardElement) {
    const id = cardElement.dataset.giveawayId;
    const title = cardElement.dataset.giveawayTitle;
    const requirements = cardElement.dataset.giveawayRequirements;
    const status = cardElement.dataset.giveawayStatus;
    const winnerCount = cardElement.dataset.giveawayWinners;
    const entryCount = cardElement.dataset.giveawayEntries;
    const channel = cardElement.dataset.giveawayChannel;
    const endTime = cardElement.dataset.giveawayEndtime;

    showGiveawayDetails(id, title, requirements, status, winnerCount, entryCount, channel, endTime);
}

function showGiveawayDetails(id, title, requirements, status, winnerCount, entryCount, channel, endTime) {
    currentGiveawayId = id;

    // Update modal content
    document.getElementById('modalGiveawayTitle').textContent = title;
    document.getElementById('modalWinnerCount').textContent = winnerCount;
    document.getElementById('modalEntryCount').textContent = entryCount;
    document.getElementById('modalRequirements').textContent = requirements || 'No requirements specified';
    document.getElementById('modalChannel').textContent = channel || 'Unknown Channel';
    document.getElementById('modalEndTime').textContent = endTime || 'No end time set';

    // Update status badge
    const statusBadge = document.getElementById('modalStatus');
    statusBadge.textContent = status.charAt(0).toUpperCase() + status.slice(1);
    statusBadge.className = 'badge';

    // Hide all buttons first
    document.getElementById('deleteGiveawayBtn').style.display = 'none';
    document.getElementById('rerollGiveawayBtn').style.display = 'none';
    document.getElementById('endEarlyGiveawayBtn').style.display = 'none';

    if (status === 'active') {
        statusBadge.classList.add('bg-success');
        document.getElementById('endEarlyGiveawayBtn').style.display = 'inline-block';
        document.getElementById('deleteGiveawayBtn').style.display = 'inline-block';
    } else if (status === 'ended') {
        statusBadge.classList.add('bg-danger');
        document.getElementById('rerollGiveawayBtn').style.display = 'inline-block';
    } else {
        statusBadge.classList.add('bg-warning', 'text-dark');
    }

    // Show modal - ensure no duplicate backdrops
    const modalElement = document.getElementById('giveawayDetailsModal');

    // Remove any existing backdrops
    const existingBackdrops = document.querySelectorAll('.modal-backdrop');
    existingBackdrops.forEach(backdrop => backdrop.remove());

    // Get or create modal instance
    let modal = bootstrap.Modal.getInstance(modalElement);
    if (!modal) {
        modal = new bootstrap.Modal(modalElement, {
            backdrop: true,
            keyboard: true,
            focus: true
        });
    }

    modal.show();
}

function deleteGiveawayFromModal() {
    if (currentGiveawayId) {
        deleteGiveaway(currentGiveawayId);
    }
}

function rerollGiveawayFromModal() {
    if (currentGiveawayId) {
        rerollGiveaway(currentGiveawayId);
    }
}

function endEarlyGiveawayFromModal() {
    if (currentGiveawayId) {
        endEarlyGiveaway(currentGiveawayId);
    }
}

function createGiveaway(event) {
    event.preventDefault();

    const form = event.target;

    // Validate host selection
    const hostUserId = document.getElementById('giveawayHost');
    if (!hostUserId || !hostUserId.value) {
        showToast('danger', 'Please select a host from the server members');
        return false;
    }

    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;

    // Update button state to show loading
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Creating...';

    fetch('/create_giveaway', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update button to show success
            submitBtn.innerHTML = '<i class="fas fa-check me-1"></i>Created!';

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('createGiveawayModal'));
            modal.hide();

            // Reset form
            form.reset();

            // Clear selected member avatar
            hideSelectedMemberAvatar();
            document.getElementById('giveawayHostSearch').value = '';
            document.getElementById('giveawayHost').value = '';

            // Show success message
            showToast('success', 'Giveaway created successfully!');

            // Reload page to show new giveaway
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showToast('danger', data.error || 'Failed to create giveaway');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('danger', 'An error occurred. Please try again.');
    })
    .finally(() => {
        // Re-enable the submit button and restore original text
        setTimeout(() => {
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        }, 1000);
    });

    return false;
}

function deleteGiveaway(giveawayId) {
    console.log('deleteGiveaway called with ID:', giveawayId);

    if (!giveawayId) {
        console.error('No giveaway ID provided');
        showToast('danger', 'No giveaway ID provided');
        return;
    }

    if (!confirm('Are you sure you want to delete this giveaway? This action cannot be undone and will cancel the giveaway completely.')) {
        return;
    }

    const requestData = {
        giveaway_id: giveawayId
    };

    console.log('Sending delete request data:', requestData);

    fetch('/delete_giveaway', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => {
        console.log('Delete response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('Delete response data:', data);
        if (data.success) {
            showToast('success', 'Giveaway deleted successfully!');
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showToast('danger', data.error || 'Failed to delete giveaway');
        }
    })
    .catch(error => {
        console.error('Delete error:', error);
        showToast('danger', 'An error occurred. Please try again.');
    });
}

function rerollGiveaway(giveawayId) {
    console.log('rerollGiveaway called with ID:', giveawayId);

    if (!giveawayId) {
        console.error('No giveaway ID provided');
        showToast('danger', 'No giveaway ID provided');
        return;
    }

    if (!confirm('Are you sure you want to reroll the winners for this giveaway? This will select new random winners from the entries.')) {
        return;
    }

    const requestData = {
        giveaway_id: giveawayId
    };

    console.log('Sending reroll request data:', requestData);

    fetch('/reroll_giveaway', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => {
        console.log('Reroll response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('Reroll response data:', data);
        if (data.success) {
            showToast('success', 'Winners rerolled successfully!');
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showToast('danger', data.error || 'Failed to reroll winners');
        }
    })
    .catch(error => {
        console.error('Reroll error:', error);
        showToast('danger', 'An error occurred. Please try again.');
    });
}

function endEarlyGiveaway(giveawayId) {
    console.log('endEarlyGiveaway called with ID:', giveawayId);

    if (!giveawayId) {
        console.error('No giveaway ID provided');
        showToast('danger', 'No giveaway ID provided');
        return;
    }

    if (!confirm('Are you sure you want to end this giveaway early? This will immediately select winners and end the giveaway.')) {
        return;
    }

    const requestData = {
        giveaway_id: giveawayId
    };

    console.log('Sending end early request data:', requestData);

    fetch('/end_early_giveaway', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => {
        console.log('End early response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('End early response data:', data);
        if (data.success) {
            showToast('success', 'Giveaway ended early successfully!');
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showToast('danger', data.error || 'Failed to end giveaway early');
        }
    })
    .catch(error => {
        console.error('End early error:', error);
        showToast('danger', 'An error occurred. Please try again.');
    });
}

function showToast(type, message) {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(toast);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 5000);
}

// Member search functionality
document.addEventListener('DOMContentLoaded', function() {
    // Clean up any existing modal backdrops on page load
    const existingBackdrops = document.querySelectorAll('.modal-backdrop');
    existingBackdrops.forEach(backdrop => backdrop.remove());

    // Reset form when modal is closed
    const createModal = document.getElementById('createGiveawayModal');

    // Handle modal opening - preserve button text
    createModal.addEventListener('show.bs.modal', function() {
        const btn1 = document.getElementById('createGiveawayBtn1');
        const btn2 = document.getElementById('createGiveawayBtn2');

        // Store original text to restore later
        if (btn1 && !btn1.dataset.originalText) {
            btn1.dataset.originalText = btn1.innerHTML;
        }
        if (btn2 && !btn2.dataset.originalText) {
            btn2.dataset.originalText = btn2.innerHTML;
        }
    });

    createModal.addEventListener('hidden.bs.modal', function() {
        const form = document.getElementById('createGiveawayForm');
        const submitBtn = form.querySelector('button[type="submit"]');

        // Reset form
        form.reset();

        // Clear selected member
        document.getElementById('giveawayHostSearch').value = '';
        document.getElementById('giveawayHost').value = '';
        hideSelectedMemberAvatar();

        // Reset submit button
        submitBtn.disabled = false;

        // Restore button text if it was modified
        const btn1 = document.getElementById('createGiveawayBtn1');
        const btn2 = document.getElementById('createGiveawayBtn2');

        if (btn1 && btn1.dataset.originalText) {
            btn1.innerHTML = btn1.dataset.originalText;
            // Remove any added spinners
            const spinners = btn1.querySelectorAll('span[style*="position: absolute"]');
            spinners.forEach(spinner => spinner.remove());
        }
        if (btn2 && btn2.dataset.originalText) {
            btn2.innerHTML = btn2.dataset.originalText;
            // Remove any added spinners
            const spinners = btn2.querySelectorAll('span[style*="position: absolute"]');
            spinners.forEach(spinner => spinner.remove());
        }

        // Hide dropdown
        const dropdown = document.getElementById('memberDropdown');
        dropdown.classList.remove('show');
        dropdown.style.display = 'none';
    });
    const searchInput = document.getElementById('giveawayHostSearch');
    const hiddenInput = document.getElementById('giveawayHost');
    const dropdown = document.getElementById('memberDropdown');
    const memberOptions = document.querySelectorAll('.member-option');
    const searchIndicator = document.getElementById('searchIndicator');

    console.log('Member dropdown initialized with', memberOptions.length, 'members');

    // Debug avatar URLs
    let avatarCount = 0;
    memberOptions.forEach((option, index) => {
        const avatarUrl = option.dataset.avatarUrl;
        if (avatarUrl && avatarUrl.trim()) {
            avatarCount++;
            if (index < 5) { // Log first 5 for debugging
                console.log(`Member ${index} avatar:`, avatarUrl);
            }
        }
    });
    console.log(`Found ${avatarCount} members with avatar URLs out of ${memberOptions.length} total`);

    // Show dropdown when input is focused
    searchInput.addEventListener('focus', function() {
        dropdown.classList.add('show');
        dropdown.style.display = 'block'; // Force display
        // Show all members when first opening (only if no search term)
        if (!this.value.trim()) {
            filterMembers('');
        }
    });

    // Hide dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.position-relative')) {
            dropdown.classList.remove('show');
            dropdown.style.display = 'none'; // Force hide
        }
    });

    // Debounced search to improve performance
    let searchTimeout;
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();

        // Show search indicator
        if (searchTerm.trim()) {
            searchIndicator.style.display = 'block';
        }

        // Clear previous timeout
        clearTimeout(searchTimeout);

        // Debounce the search by 200ms
        searchTimeout = setTimeout(() => {
            console.log('Executing search for:', searchTerm);
            filterMembers(searchTerm);
            dropdown.classList.add('show');
            dropdown.style.display = 'block'; // Force display
            console.log('Dropdown classes after search:', dropdown.className);
            console.log('Dropdown display style:', window.getComputedStyle(dropdown).display);
            // Hide search indicator
            searchIndicator.style.display = 'none';
        }, 200);

        // Clear selection if search changes
        if (hiddenInput.value && !searchTerm) {
            hiddenInput.value = '';
            hideSelectedMemberAvatar();
        }
    });

    // Handle member selection
    memberOptions.forEach(option => {
        option.addEventListener('click', function(e) {
            e.preventDefault();
            const memberId = this.dataset.memberId;
            const memberName = this.dataset.memberName;
            const displayName = this.dataset.displayName;
            const avatarUrl = this.dataset.avatarUrl;

            // Set values
            hiddenInput.value = memberId;
            searchInput.value = displayName || memberName.split('(')[0].trim(); // Show display name only

            // Show selected member avatar
            showSelectedMemberAvatar(avatarUrl, displayName || memberName);

            // Hide dropdown properly
            dropdown.classList.remove('show');
            dropdown.style.display = 'none'; // Force hide

            // Add visual feedback
            searchInput.classList.add('is-valid');
            setTimeout(() => {
                searchInput.classList.remove('is-valid');
            }, 2000);

            console.log('Selected member:', memberName, 'ID:', memberId);
        });
    });

    // Keyboard navigation
    let currentFocus = -1;
    searchInput.addEventListener('keydown', function(e) {
        const visibleOptions = Array.from(memberOptions).filter(option =>
            option.style.display !== 'none'
        );

        if (e.key === 'ArrowDown') {
            e.preventDefault();
            currentFocus++;
            if (currentFocus >= visibleOptions.length) currentFocus = 0;
            setActive(visibleOptions);
        } else if (e.key === 'ArrowUp') {
            e.preventDefault();
            currentFocus--;
            if (currentFocus < 0) currentFocus = visibleOptions.length - 1;
            setActive(visibleOptions);
        } else if (e.key === 'Enter') {
            e.preventDefault();
            if (currentFocus > -1 && visibleOptions[currentFocus]) {
                visibleOptions[currentFocus].click();
            }
        } else if (e.key === 'Escape') {
            dropdown.classList.remove('show');
            dropdown.style.display = 'none'; // Force hide
            currentFocus = -1;
        }
    });

    function setActive(options) {
        // Remove active class from all
        options.forEach(option => option.classList.remove('active'));

        // Add active class to current
        if (currentFocus > -1 && options[currentFocus]) {
            options[currentFocus].classList.add('active');
            options[currentFocus].scrollIntoView({ block: 'nearest' });
        }
    }

    function filterMembers(searchTerm) {
        let visibleCount = 0;

        // If no search term, show all members
        if (!searchTerm.trim()) {
            memberOptions.forEach(option => {
                option.style.display = 'block';
                visibleCount++;
            });
            console.log(`Showing all ${visibleCount} members`);
            return;
        }

        memberOptions.forEach(option => {
            const memberName = (option.dataset.memberName || '').toLowerCase();
            const displayName = (option.dataset.displayName || '').toLowerCase();
            const username = (option.dataset.username || '').toLowerCase();

            // Also get text content as fallback
            const displayNameText = option.querySelector('.member-display-name');
            const usernameText = option.querySelector('.member-username');
            const displayNameFallback = displayNameText ? displayNameText.textContent.toLowerCase() : '';
            const usernameFallback = usernameText ? usernameText.textContent.toLowerCase() : '';

            // Check if search term matches any field
            const matches = memberName.includes(searchTerm) ||
                           displayName.includes(searchTerm) ||
                           username.includes(searchTerm) ||
                           displayNameFallback.includes(searchTerm) ||
                           usernameFallback.includes(searchTerm);

            if (matches) {
                option.style.display = 'block';
                visibleCount++;
            } else {
                option.style.display = 'none';
            }
        });

        // Log results and ensure dropdown is visible
        if (visibleCount === 0) {
            console.log(`No matches found for "${searchTerm}"`);
        } else {
            console.log(`Found ${visibleCount} matches for "${searchTerm}"`);
        }

        // Ensure dropdown is visible when we have results
        if (visibleCount > 0) {
            dropdown.classList.add('show');
            dropdown.style.display = 'block'; // Force display with inline style
            console.log('Dropdown should be visible with', visibleCount, 'results');
        }

        // Show "no results" message if no members match
        const noResultsMsg = dropdown.querySelector('.no-results');
        if (visibleCount === 0 && searchTerm) {
            if (!noResultsMsg) {
                const noResults = document.createElement('div');
                noResults.className = 'dropdown-item-text text-muted no-results';
                noResults.innerHTML = '<i class="fas fa-search me-2"></i>No members found matching "' + searchTerm + '"';
                dropdown.appendChild(noResults);
            }
        } else if (noResultsMsg) {
            noResultsMsg.remove();
        }

        currentFocus = -1; // Reset focus when filtering
    }

    // Function to show selected member avatar
    function showSelectedMemberAvatar(avatarUrl, memberName) {
        const avatarContainer = document.getElementById('selectedMemberAvatar');
        const avatarImg = document.getElementById('selectedAvatarImg');
        const avatarPlaceholder = document.getElementById('selectedAvatarPlaceholder');

        if (avatarUrl && avatarUrl.trim()) {
            // Show real avatar
            avatarImg.src = avatarUrl;
            avatarImg.style.display = 'block';
            avatarPlaceholder.style.display = 'none';

            // Handle image load error
            avatarImg.onerror = function() {
                this.style.display = 'none';
                avatarPlaceholder.style.display = 'flex';
                // Set placeholder initials
                const nameParts = memberName.split(' ');
                const initials = nameParts.length > 1 ?
                    nameParts[0][0].toUpperCase() + nameParts[nameParts.length - 1][0].toUpperCase() :
                    nameParts[0][0].toUpperCase();
                avatarPlaceholder.textContent = initials;
            };
        } else {
            // Show placeholder
            avatarImg.style.display = 'none';
            avatarPlaceholder.style.display = 'flex';
            // Set placeholder initials
            const nameParts = memberName.split(' ');
            const initials = nameParts.length > 1 ?
                nameParts[0][0].toUpperCase() + nameParts[nameParts.length - 1][0].toUpperCase() :
                nameParts[0][0].toUpperCase();
            avatarPlaceholder.textContent = initials;
        }

        // Show the avatar container
        avatarContainer.style.display = 'block';
    }

    // Function to hide selected member avatar
    function hideSelectedMemberAvatar() {
        const avatarContainer = document.getElementById('selectedMemberAvatar');
        avatarContainer.style.display = 'none';
    }

    // Make hideSelectedMemberAvatar globally accessible
    window.hideSelectedMemberAvatar = hideSelectedMemberAvatar;

    // Helper function to find members by username
    window.findMembersByUsername = function(searchTerm) {
        const matches = [];
        memberOptions.forEach(option => {
            const memberName = (option.dataset.memberName || '').toLowerCase();
            const displayName = (option.dataset.displayName || '').toLowerCase();
            const username = (option.dataset.username || '').toLowerCase();

            if (memberName.includes(searchTerm.toLowerCase()) ||
                displayName.includes(searchTerm.toLowerCase()) ||
                username.includes(searchTerm.toLowerCase())) {
                matches.push({
                    id: option.dataset.memberId,
                    name: option.dataset.memberName,
                    displayName: option.dataset.displayName,
                    username: option.dataset.username
                });
            }
        });
        console.log(`Found ${matches.length} members matching "${searchTerm}":`, matches);
        return matches;
    };

    // Helper to show all members (for debugging)
    window.showAllMembers = function() {
        console.log('All available members:');
        memberOptions.forEach((option, index) => {
            console.log(`${index + 1}. ${option.dataset.memberName} (ID: ${option.dataset.memberId})`);
        });
    };

    // Helper to manually show dropdown (for debugging)
    window.showDropdown = function() {
        dropdown.classList.add('show');
        dropdown.style.display = 'block'; // Force display
        memberOptions.forEach(option => option.style.display = 'block');
        console.log('Manually showing dropdown with all members');
        console.log('Dropdown classes:', dropdown.className);
        console.log('Dropdown computed display:', window.getComputedStyle(dropdown).display);
    };

    // Helper to test search
    window.testSearch = function(term) {
        searchInput.value = term;
        filterMembers(term.toLowerCase());
        dropdown.classList.add('show');
        dropdown.style.display = 'block'; // Force display
        console.log('Test search completed for:', term);
    };
});
</script>
{% endblock %}
