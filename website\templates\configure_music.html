{% extends "base.html" %}

{% block title %}Configure Music System - {{ server_info.name }}{% endblock %}

{% block styles %}
<style>
    .config-header {
        background: linear-gradient(135deg, rgba(147, 51, 234, 0.1) 0%, rgba(168, 85, 247, 0.1) 100%);
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(20px);
        border: 1px solid var(--border-color);
    }

    .config-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #9333ea, #a855f7);
    }

    .config-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .config-subtitle {
        color: var(--text-secondary);
        font-size: 1.1rem;
        margin-bottom: 0;
        opacity: 0.9;
    }

    .feature-card {
        background: rgba(255, 255, 255, 0.03);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        backdrop-filter: blur(20px);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        margin-bottom: 1.5rem;
    }

    .feature-card:hover {
        border-color: rgba(147, 51, 234, 0.3);
        box-shadow: 0 8px 32px rgba(147, 51, 234, 0.1);
        transform: translateY(-2px);
    }

    .card-header {
        background: rgba(147, 51, 234, 0.05);
        border-bottom: 1px solid var(--border-color);
        padding: 1.5rem;
        border-radius: var(--border-radius) var(--border-radius) 0 0;
    }

    .card-title {
        color: var(--text-primary);
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .card-subtitle {
        color: var(--text-secondary);
        font-size: 0.95rem;
        margin-bottom: 0;
        opacity: 0.8;
    }

    .card-body {
        padding: 1.5rem;
    }

    .feature-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .feature-list li {
        padding: 0.5rem 0;
        color: var(--text-secondary);
        font-size: 0.95rem;
    }

    .config-item {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid var(--border-color);
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .config-label {
        font-weight: 600;
        color: var(--text-secondary);
        font-size: 0.875rem;
        margin-bottom: 0.5rem;
    }

    .config-value {
        color: var(--text-primary);
    }

    .form-select, .form-control {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid var(--border-color);
        color: var(--text-primary);
        border-radius: 8px;
    }

    .form-select:focus, .form-control:focus {
        background: rgba(255, 255, 255, 0.08);
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(147, 51, 234, 0.25);
        color: var(--text-primary);
    }

    .form-select option {
        background: var(--bg-secondary);
        color: var(--text-primary);
    }

    .btn-primary {
        background: linear-gradient(135deg, #9333ea, #a855f7);
        border: none;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(147, 51, 234, 0.3);
    }

    .btn-outline-secondary {
        border-color: var(--border-color);
        color: var(--text-secondary);
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-outline-secondary:hover {
        background: rgba(255, 255, 255, 0.05);
        border-color: var(--primary-color);
        color: var(--text-primary);
        transform: translateY(-2px);
    }

    .text-purple {
        color: #a855f7 !important;
    }

    .form-control {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid var(--border-color);
        color: var(--text-primary);
        border-radius: 8px;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        background: rgba(255, 255, 255, 0.08);
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(var(--primary-color-rgb), 0.25);
        color: var(--text-primary);
    }

    .form-control option {
        background: var(--bg-secondary);
        color: var(--text-primary);
    }

    .btn-primary {
        background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(var(--primary-color-rgb), 0.3);
    }

    .btn-danger {
        background: linear-gradient(135deg, #dc3545, #c82333);
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-danger:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
    }

    .config-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }

    .config-item {
        background: rgba(255, 255, 255, 0.05);
        padding: 1rem;
        border-radius: 8px;
        border: 1px solid var(--border-color);
    }

    .config-item-label {
        font-weight: 600;
        color: var(--text-secondary);
        font-size: 0.875rem;
        margin-bottom: 0.5rem;
    }

    .config-item-value {
        color: var(--text-primary);
        font-family: 'Monaco', 'Menlo', monospace;
        background: rgba(255, 255, 255, 0.1);
        padding: 0.5rem;
        border-radius: 6px;
        font-size: 0.875rem;
    }

    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 60px;
        height: 34px;
    }

    .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 34px;
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 26px;
        width: 26px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }

    input:checked + .slider {
        background-color: var(--primary-color);
    }

    input:checked + .slider:before {
        transform: translateX(26px);
    }

    .feature-info {
        background: rgba(var(--primary-color-rgb), 0.1);
        border: 1px solid rgba(var(--primary-color-rgb), 0.2);
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1.5rem;
    }

    .feature-info h6 {
        color: var(--primary-color);
        margin-bottom: 0.5rem;
        font-weight: 600;
    }

    .feature-info ul {
        margin-bottom: 0;
        padding-left: 1.5rem;
    }

    .feature-info li {
        margin-bottom: 0.25rem;
        color: var(--text-secondary);
    }

    @media (max-width: 768px) {
        .config-header {
            padding: 1.5rem;
        }

        .config-title {
            font-size: 1.5rem;
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .form-section {
            padding: 1.5rem;
        }

        .config-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="row mt-3">
    <!-- Sidebar -->
    <div class="col-md-3">
        {% include 'sidebar.html' %}
    </div>

    <!-- Main Content -->
    <div class="col-md-9">
        <div class="config-header">
            <h1 class="config-title">
                <i class="fas fa-music text-purple"></i>
                Music System Configuration
            </h1>
            <p class="config-subtitle">Configure music playback and DJ permissions for your server</p>
        </div>

        <!-- Feature Information -->
        <div class="feature-card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-info-circle text-info"></i>
                    Music System Features
                </h5>
                <p class="card-subtitle">Powerful music bot with YouTube integration and advanced controls</p>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <ul class="feature-list">
                            <li><i class="fas fa-play text-success me-2"></i>Play music from YouTube with search</li>
                            <li><i class="fas fa-list text-info me-2"></i>Queue management with shuffle and loop</li>
                            <li><i class="fas fa-crown text-warning me-2"></i>DJ role permissions for advanced controls</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="feature-list">
                            <li><i class="fas fa-volume-off text-danger me-2"></i>Auto-disconnect when no users present</li>
                            <li><i class="fas fa-microphone text-purple me-2"></i>Temp voice channel owner permissions</li>
                            <li><i class="fas fa-terminal text-secondary me-2"></i>Full slash command integration</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>



        <!-- DJ Role Configuration -->
        <div class="feature-card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-crown text-warning"></i>
                    DJ Role Configuration
                </h5>
                <p class="card-subtitle">Set which role can control music playback (optional)</p>
            </div>
            <div class="card-body">
                <form method="POST" id="djRoleForm">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <input type="hidden" name="action" value="update_dj_role">

                    <div class="mb-3">
                        <label for="dj_role_id" class="form-label">
                            <i class="fas fa-users-crown me-2 text-warning"></i>DJ Role (Optional)
                        </label>
                        <select class="form-select" id="dj_role_id" name="dj_role_id">
                            <option value="">No DJ Role - Users with Manage Channels permission can control music</option>
                        </select>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            Users with this role, Manage Channels permission, or temp voice channel owners can use skip, pause, shuffle, and other advanced controls
                        </div>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update DJ Role
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Current Configuration -->
        {% if music_settings %}
        <div class="feature-card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-cog text-secondary"></i>
                    Current Configuration
                </h5>
                <p class="card-subtitle">Overview of your music system settings</p>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="config-item">
                            <div class="config-label">System Status</div>
                            <div class="config-value">
                                {% if music_settings.enabled %}
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>Enabled
                                    </span>
                                {% else %}
                                    <span class="badge bg-danger">
                                        <i class="fas fa-times me-1"></i>Disabled
                                    </span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="config-item">
                            <div class="config-label">DJ Role</div>
                            <div class="config-value">
                                <span class="badge bg-primary" id="currentDjRole">
                                    {% if music_settings.dj_role_id %}
                                        {{ music_settings.dj_role_id }}
                                    {% else %}
                                        Manage Channels Permission
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Back Button -->
        <div class="text-center mt-4">
            <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    loadRoles();
});

function loadRoles() {
    fetch('{{ url_for('api_roles') }}')
        .then(response => response.json())
        .then(roles => {
            const roleSelect = document.getElementById('dj_role_id');
            if (!roleSelect) return;

            // Clear existing options except the first one
            roleSelect.innerHTML = '<option value="">No DJ Role - Users with Manage Channels permission can control music</option>';

            if (Array.isArray(roles)) {
                roles.forEach(role => {
                    // Skip @everyone and managed roles
                    if (role.name === '@everyone' || role.managed) return;

                    const option = document.createElement('option');
                    option.value = role.id;
                    option.textContent = role.name;

                    // Select current role if editing
                    {% if music_settings and music_settings.dj_role_id %}
                    if (role.id === '{{ music_settings.dj_role_id }}') {
                        option.selected = true;
                    }
                    {% endif %}

                    roleSelect.appendChild(option);
                });

                // Update current DJ role display
                updateCurrentDjRoleDisplay(roles);
            }
        })
        .catch(error => {
            console.error('Error loading roles:', error);
            const roleSelect = document.getElementById('dj_role_id');
            if (roleSelect) {
                roleSelect.innerHTML = '<option value="">Error loading roles - Please enter role ID manually</option>';
            }
        });
}

function updateCurrentDjRoleDisplay(roles) {
    const currentDjRoleElement = document.getElementById('currentDjRole');
    if (!currentDjRoleElement) return;

    {% if music_settings and music_settings.dj_role_id %}
    const currentRoleId = '{{ music_settings.dj_role_id }}';
    const role = roles.find(r => r.id === currentRoleId);
    if (role) {
        currentDjRoleElement.textContent = role.name;
        currentDjRoleElement.style.color = role.color || '#ffffff';
    }
    {% endif %}
}


</script>
{% endblock %}
