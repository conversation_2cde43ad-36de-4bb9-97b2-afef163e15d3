{% extends "admin/base.html" %}

{% block title %}Pricing Management{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-dollar-sign me-2"></i>
        Pricing Management
    </h1>
    <div>
        <button class="btn btn-admin" onclick="previewShopPage()" target="_blank">
            <i class="fas fa-external-link-alt me-2"></i>
            Preview Shop Page
        </button>
    </div>
</div>

<div class="alert alert-info alert-admin">
    <i class="fas fa-info-circle me-2"></i>
    <strong>Note:</strong> Changes to pricing will be reflected immediately on the shop page. 
    Make sure to update corresponding Stripe price IDs if needed.
</div>

<!-- Current Pricing Overview -->
<div class="row mb-4">
    {% for tier, config in pricing_config.tiers.items() %}
    <div class="col-md-3 col-sm-6">
        <div class="stat-card {{ 'success' if tier == 'monthly' else 'warning' if tier == 'yearly' else 'danger' if tier == 'lifetime' else '' }}">
            <div class="stat-number">${{ "%.2f"|format(config.price) }}</div>
            <div class="stat-label">
                <i class="fas fa-{{ 'crown' if tier == 'lifetime' else 'calendar' }} me-1"></i>
                {{ tier.title() }}
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Pricing Configuration -->
<div class="card admin-card">
    <div class="card-header admin-card-header">
        <h5 class="mb-0">
            <i class="fas fa-edit me-2"></i>
            Edit Pricing Tiers
        </h5>
    </div>
    <div class="card-body">
        <form id="pricingForm">
            <div class="row">
                <!-- Weekly Tier -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-calendar-week me-2"></i>
                                Weekly Subscription
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="weekly_price" class="form-label">Price (USD)</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" id="weekly_price" name="weekly_price" 
                                           step="0.01" min="0" value="{{ pricing_config.tiers.weekly.price if pricing_config.tiers.weekly else '2.99' }}" required>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="weekly_stripe_id" class="form-label">Stripe Price ID</label>
                                <input type="text" class="form-control" id="weekly_stripe_id" name="weekly_stripe_id" 
                                       value="{{ pricing_config.tiers.weekly.stripe_price_id if pricing_config.tiers.weekly else '' }}" 
                                       placeholder="price_xxxxxxxxx">
                            </div>
                            <div class="mb-3">
                                <label for="weekly_description" class="form-label">Description</label>
                                <textarea class="form-control" id="weekly_description" name="weekly_description" rows="2" 
                                          placeholder="Perfect for trying out premium features">{{ pricing_config.tiers.weekly.description if pricing_config.tiers.weekly else '' }}</textarea>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="weekly_enabled" name="weekly_enabled" 
                                       {{ 'checked' if pricing_config.tiers.weekly.enabled != false else '' }}>
                                <label class="form-check-label" for="weekly_enabled">
                                    Enable Weekly Tier
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Monthly Tier -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-calendar-alt me-2"></i>
                                Monthly Subscription
                                <span class="badge bg-light text-dark ms-2">Popular</span>
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="monthly_price" class="form-label">Price (USD)</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" id="monthly_price" name="monthly_price" 
                                           step="0.01" min="0" value="{{ pricing_config.tiers.monthly.price if pricing_config.tiers.monthly else '9.99' }}" required>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="monthly_stripe_id" class="form-label">Stripe Price ID</label>
                                <input type="text" class="form-control" id="monthly_stripe_id" name="monthly_stripe_id" 
                                       value="{{ pricing_config.tiers.monthly.stripe_price_id if pricing_config.tiers.monthly else '' }}" 
                                       placeholder="price_xxxxxxxxx">
                            </div>
                            <div class="mb-3">
                                <label for="monthly_description" class="form-label">Description</label>
                                <textarea class="form-control" id="monthly_description" name="monthly_description" rows="2" 
                                          placeholder="Most popular choice for regular users">{{ pricing_config.tiers.monthly.description if pricing_config.tiers.monthly else '' }}</textarea>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="monthly_enabled" name="monthly_enabled" 
                                       {{ 'checked' if pricing_config.tiers.monthly.enabled != false else '' }}>
                                <label class="form-check-label" for="monthly_enabled">
                                    Enable Monthly Tier
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Yearly Tier -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="mb-0">
                                <i class="fas fa-calendar me-2"></i>
                                Yearly Subscription
                                <span class="badge bg-dark text-light ms-2">Best Value</span>
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="yearly_price" class="form-label">Price (USD)</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" id="yearly_price" name="yearly_price" 
                                           step="0.01" min="0" value="{{ pricing_config.tiers.yearly.price if pricing_config.tiers.yearly else '99.99' }}" required>
                                </div>
                                <div class="form-text">
                                    Monthly equivalent: $<span id="yearly_monthly_equiv">{{ "%.2f"|format((pricing_config.tiers.yearly.price / 12) if pricing_config.tiers.yearly else 8.33) }}</span>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="yearly_stripe_id" class="form-label">Stripe Price ID</label>
                                <input type="text" class="form-control" id="yearly_stripe_id" name="yearly_stripe_id" 
                                       value="{{ pricing_config.tiers.yearly.stripe_price_id if pricing_config.tiers.yearly else '' }}" 
                                       placeholder="price_xxxxxxxxx">
                            </div>
                            <div class="mb-3">
                                <label for="yearly_description" class="form-label">Description</label>
                                <textarea class="form-control" id="yearly_description" name="yearly_description" rows="2" 
                                          placeholder="Best value for long-term users">{{ pricing_config.tiers.yearly.description if pricing_config.tiers.yearly else '' }}</textarea>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="yearly_enabled" name="yearly_enabled" 
                                       {{ 'checked' if pricing_config.tiers.yearly.enabled != false else '' }}>
                                <label class="form-check-label" for="yearly_enabled">
                                    Enable Yearly Tier
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Lifetime Tier -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-danger text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-crown me-2"></i>
                                Lifetime Access
                                <span class="badge bg-light text-dark ms-2">Premium</span>
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="lifetime_price" class="form-label">Price (USD)</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" id="lifetime_price" name="lifetime_price" 
                                           step="0.01" min="0" value="{{ pricing_config.tiers.lifetime.price if pricing_config.tiers.lifetime else '299.99' }}" required>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="lifetime_stripe_id" class="form-label">Stripe Price ID</label>
                                <input type="text" class="form-control" id="lifetime_stripe_id" name="lifetime_stripe_id" 
                                       value="{{ pricing_config.tiers.lifetime.stripe_price_id if pricing_config.tiers.lifetime else '' }}" 
                                       placeholder="price_xxxxxxxxx">
                            </div>
                            <div class="mb-3">
                                <label for="lifetime_description" class="form-label">Description</label>
                                <textarea class="form-control" id="lifetime_description" name="lifetime_description" rows="2" 
                                          placeholder="One-time payment for lifetime access">{{ pricing_config.tiers.lifetime.description if pricing_config.tiers.lifetime else '' }}</textarea>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="lifetime_enabled" name="lifetime_enabled" 
                                       {{ 'checked' if pricing_config.tiers.lifetime.enabled != false else '' }}>
                                <label class="form-check-label" for="lifetime_enabled">
                                    Enable Lifetime Tier
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Settings -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        Additional Settings
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="currency" class="form-label">Currency</label>
                                <select class="form-select" id="currency" name="currency">
                                    <option value="USD" {{ 'selected' if pricing_config.currency == 'USD' else '' }}>USD ($)</option>
                                    <option value="EUR" {{ 'selected' if pricing_config.currency == 'EUR' else '' }}>EUR (€)</option>
                                    <option value="GBP" {{ 'selected' if pricing_config.currency == 'GBP' else '' }}>GBP (£)</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="trial_days" class="form-label">Free Trial Days</label>
                                <input type="number" class="form-control" id="trial_days" name="trial_days" 
                                       min="0" max="30" value="{{ pricing_config.trial_days or 0 }}">
                                <div class="form-text">Set to 0 to disable free trials</div>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="shop_header" class="form-label">Shop Page Header</label>
                        <input type="text" class="form-control" id="shop_header" name="shop_header" 
                               value="{{ pricing_config.shop_header or 'Choose Your Plan' }}" 
                               placeholder="Choose Your Plan">
                    </div>
                    <div class="mb-3">
                        <label for="shop_description" class="form-label">Shop Page Description</label>
                        <textarea class="form-control" id="shop_description" name="shop_description" rows="3" 
                                  placeholder="Unlock premium features with our subscription plans">{{ pricing_config.shop_description or '' }}</textarea>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="show_discounts" name="show_discounts" 
                               {{ 'checked' if pricing_config.show_discounts != false else '' }}>
                        <label class="form-check-label" for="show_discounts">
                            Show discount percentages on yearly plan
                        </label>
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-end mt-4">
                <button type="button" class="btn btn-outline-secondary me-2" onclick="resetPricing()">
                    <i class="fas fa-undo me-2"></i>
                    Reset to Defaults
                </button>
                <button type="button" class="btn btn-admin" onclick="savePricing()">
                    <i class="fas fa-save me-2"></i>
                    Save Pricing Changes
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Calculate yearly monthly equivalent in real-time
document.getElementById('yearly_price').addEventListener('input', function() {
    const yearlyPrice = parseFloat(this.value) || 0;
    const monthlyEquiv = (yearlyPrice / 12).toFixed(2);
    document.getElementById('yearly_monthly_equiv').textContent = monthlyEquiv;
});

function savePricing() {
    const form = document.getElementById('pricingForm');
    const formData = new FormData(form);
    
    // Build pricing object
    const pricing = {
        weekly: {
            price: parseFloat(formData.get('weekly_price')) || 0,
            stripe_price_id: formData.get('weekly_stripe_id') || '',
            description: formData.get('weekly_description') || '',
            enabled: formData.get('weekly_enabled') === 'on'
        },
        monthly: {
            price: parseFloat(formData.get('monthly_price')) || 0,
            stripe_price_id: formData.get('monthly_stripe_id') || '',
            description: formData.get('monthly_description') || '',
            enabled: formData.get('monthly_enabled') === 'on'
        },
        yearly: {
            price: parseFloat(formData.get('yearly_price')) || 0,
            stripe_price_id: formData.get('yearly_stripe_id') || '',
            description: formData.get('yearly_description') || '',
            enabled: formData.get('yearly_enabled') === 'on'
        },
        lifetime: {
            price: parseFloat(formData.get('lifetime_price')) || 0,
            stripe_price_id: formData.get('lifetime_stripe_id') || '',
            description: formData.get('lifetime_description') || '',
            enabled: formData.get('lifetime_enabled') === 'on'
        }
    };
    
    const settings = {
        currency: formData.get('currency') || 'USD',
        trial_days: parseInt(formData.get('trial_days')) || 0,
        shop_header: formData.get('shop_header') || 'Choose Your Plan',
        shop_description: formData.get('shop_description') || '',
        show_discounts: formData.get('show_discounts') === 'on'
    };
    
    // Validate required fields
    const enabledTiers = Object.keys(pricing).filter(tier => pricing[tier].enabled);
    if (enabledTiers.length === 0) {
        alert('At least one pricing tier must be enabled');
        return;
    }
    
    for (const tier of enabledTiers) {
        if (!pricing[tier].price || pricing[tier].price <= 0) {
            alert(`${tier.charAt(0).toUpperCase() + tier.slice(1)} tier must have a valid price`);
            return;
        }
    }
    
    fetch('/api/admin/pricing', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        },
        body: JSON.stringify({
            pricing: {
                tiers: pricing,
                ...settings
            }
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Pricing updated successfully!');
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while saving pricing');
    });
}

function resetPricing() {
    if (!confirm('Are you sure you want to reset pricing to default values? This will overwrite all current settings.')) {
        return;
    }
    
    // Reset to default values
    document.getElementById('weekly_price').value = '2.99';
    document.getElementById('monthly_price').value = '9.99';
    document.getElementById('yearly_price').value = '99.99';
    document.getElementById('lifetime_price').value = '299.99';
    
    // Clear Stripe IDs
    document.getElementById('weekly_stripe_id').value = '';
    document.getElementById('monthly_stripe_id').value = '';
    document.getElementById('yearly_stripe_id').value = '';
    document.getElementById('lifetime_stripe_id').value = '';
    
    // Clear descriptions
    document.getElementById('weekly_description').value = 'Perfect for trying out premium features';
    document.getElementById('monthly_description').value = 'Most popular choice for regular users';
    document.getElementById('yearly_description').value = 'Best value for long-term users';
    document.getElementById('lifetime_description').value = 'One-time payment for lifetime access';
    
    // Enable all tiers
    document.getElementById('weekly_enabled').checked = true;
    document.getElementById('monthly_enabled').checked = true;
    document.getElementById('yearly_enabled').checked = true;
    document.getElementById('lifetime_enabled').checked = true;
    
    // Reset settings
    document.getElementById('currency').value = 'USD';
    document.getElementById('trial_days').value = '0';
    document.getElementById('shop_header').value = 'Choose Your Plan';
    document.getElementById('shop_description').value = '';
    document.getElementById('show_discounts').checked = true;
    
    // Recalculate yearly equivalent
    document.getElementById('yearly_monthly_equiv').textContent = '8.33';
}

function previewShopPage() {
    window.open('/shop', '_blank');
}
</script>
{% endblock %}