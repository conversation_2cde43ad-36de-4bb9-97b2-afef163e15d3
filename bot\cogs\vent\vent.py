"""
Vent System Cog for Discord Bot
Handles anonymous venting functionality
"""

import discord
from discord.ext import commands
from discord import app_commands
import logging
from datetime import datetime, timezone
# Utils functionality moved to core utilities cog

logger = logging.getLogger(__name__)

class VentSystem(commands.Cog):
    """Cog for handling the vent system functionality"""
    
    def __init__(self, bot):
        self.bot = bot
        self.db = bot.db
        
    async def cog_load(self):
        """Called when the cog is loaded"""
        logger.info("Loading Vent System cog...")
        logger.info("Vent System cog loaded successfully")
        
    async def cog_unload(self):
        """Called when the cog is unloaded"""
        logger.info("Unloading Vent System cog...")
        logger.info("Vent System cog unloaded")

    @app_commands.command(name="vent", description="Send an anonymous message to the vent channel")
    @app_commands.describe(
        message="Your anonymous message (keep it appropriate)"
    )
    async def vent(self, interaction: discord.Interaction, message: str):
        """Send an anonymous vent message"""
        # Check if server has a license
        if not self.db.is_server_licensed(interaction.guild.id):
            await interaction.response.send_message("❌ This server does not have a valid license.", ephemeral=True)
            return
            
        # Check if vent system is enabled
        if not self.db.is_system_enabled(interaction.guild.id, "vent"):
            await interaction.response.send_message("❌ The vent system is currently disabled on this server.", ephemeral=True)
            return

        # Check message length
        if len(message.strip()) == 0:
            await interaction.response.send_message("❌ Your vent message cannot be empty.", ephemeral=True)
            return

        if len(message) > 2000:
            await interaction.response.send_message("❌ Your vent message is too long. Please keep it under 2000 characters.", ephemeral=True)
            return

        # Check if vent logging is enabled (no longer requires Discord log channel)
        logging_config = self.db.get_logging_config(interaction.guild.id)
        if not (logging_config.get('dashboard_enabled', True) or logging_config.get('discord_enabled', False)):
            await interaction.response.send_message("❌ The vent system requires logging to be enabled. Ask an administrator to configure logging in the dashboard.", ephemeral=True)
            return

        # Get vent settings
        vent_settings = self.db.get_vent_settings(interaction.guild.id)
        if not vent_settings:
            await interaction.response.send_message("❌ The vent system has not been set up on this server. Ask an administrator to configure it in the dashboard.", ephemeral=True)
            return

        # Get vent channel
        vent_channel = interaction.guild.get_channel(vent_settings['vent_channel_id'])
        if not vent_channel:
            await interaction.response.send_message("❌ The configured vent channel no longer exists. Please ask an administrator to reconfigure the system.", ephemeral=True)
            return

        # Send anonymous message to vent channel
        try:
            vent_embed = discord.Embed(
                title="💭 Anonymous Vent",
                description=message,
                color=discord.Color.purple(),
                timestamp=datetime.now(timezone.utc)
            )
            vent_embed.set_footer(text="This message was sent anonymously | use /vent <message>")

            await vent_channel.send(embed=vent_embed)

            # Log the vent message for moderation
            self.db.log_vent_message(
                interaction.guild.id,
                interaction.user.id,
                f"{interaction.user.name}#{interaction.user.discriminator}",
                message
            )

            # Log to dashboard
            self.db.log_bot_activity(
                interaction.guild.id,
                interaction.user.id,
                f"{interaction.user.name}#{interaction.user.discriminator}",
                "Anonymous vent message sent",
                f"Message length: {len(message)} characters",
                "vent",
                vent_channel.id
            )

            # Also log to Discord if enabled
            try:
                logging_cog = self.bot.get_cog('LoggingSystem')
                if logging_cog:
                    await logging_cog.log_bot_activity_to_channel(
                        interaction.guild.id, "vent_logs", interaction.user.id,
                        f"{interaction.user.name}#{interaction.user.discriminator}",
                        "Anonymous vent message sent",
                        f"Message length: {len(message)} characters",
                        vent_channel.id
                    )
            except Exception as e:
                logger.error(f"Failed to log vent message to Discord: {e}")

            # Send log to Discord log channel if configured
            try:
                logging_config = self.db.get_logging_config(interaction.guild.id)
                if logging_config.get('discord_enabled') and logging_config.get('log_channel_id'):
                    log_channel = interaction.guild.get_channel(logging_config['log_channel_id'])
                    if log_channel:
                        log_embed = discord.Embed(
                            title="💭 Vent Message Sent",
                            description="An anonymous vent message was sent",
                            color=discord.Color.purple(),
                            timestamp=datetime.now(timezone.utc)
                        )
                        log_embed.add_field(name="User", value=f"{interaction.user.mention} ({interaction.user.id})", inline=True)
                        log_embed.add_field(name="Channel", value=vent_channel.mention, inline=True)
                        log_embed.add_field(name="Message", value=message[:1000] + ("..." if len(message) > 1000 else ""), inline=False)

                        try:
                            await log_channel.send(embed=log_embed)
                        except Exception as e:
                            logger.error(f"Failed to send vent log: {e}")
            except Exception as e:
                logger.error(f"Error sending vent log to Discord: {e}")

            await interaction.response.send_message("✅ Your anonymous message has been sent to the vent channel.", ephemeral=True)

        except Exception as e:
            logger.error(f"Error sending vent message: {e}")
            await interaction.response.send_message("❌ Failed to send your vent message. Please try again later.", ephemeral=True)


async def setup(bot):
    """Setup function for the cog"""
    await bot.add_cog(VentSystem(bot))
