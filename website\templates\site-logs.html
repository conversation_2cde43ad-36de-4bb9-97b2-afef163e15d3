{% extends "base.html" %}

{% block title %}Logs - {{ server_info.name }}{% endblock %}

{% block styles %}
<style>
    .logs-header {
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 197, 253, 0.1) 100%);
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(20px);
        border: 1px solid var(--border-color);
    }

    .logs-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #3b82f6, #93c5fd);
    }

    .logs-title {
        font-size: 2rem;
        font-weight: 700;
        background: linear-gradient(135deg, var(--text-primary), #3b82f6);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .logs-subtitle {
        color: var(--text-secondary);
        font-size: 1.1rem;
        margin: 0;
    }

    .filter-section {
        background: rgba(255, 255, 255, 0.03);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        padding: 1.5rem;
        margin-bottom: 2rem;
        backdrop-filter: blur(20px);
    }

    .filter-controls {
        display: grid;
        grid-template-columns: 1fr 1fr auto;
        gap: 1rem;
        align-items: end;
    }

    .filter-buttons {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .btn-group-custom {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    @media (max-width: 768px) {
        .logs-header {
            padding: 1.5rem;
        }

        .logs-title {
            font-size: 1.5rem;
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .filter-controls {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .filter-buttons {
            justify-content: stretch;
        }

        .btn-group-custom .btn {
            flex: 1;
            min-width: 0;
        }
    }

    /* Log row interactions */
    .log-row:hover {
        background-color: rgba(255, 255, 255, 0.1) !important;
    }

    .log-row .fa-chevron-down {
        transition: transform 0.3s ease;
    }

    .log-row[aria-expanded="true"] .fa-chevron-down {
        transform: rotate(180deg);
    }

    .collapse.show {
        border-left: 3px solid #007bff;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mt-3">
    <!-- Sidebar -->
    <div class="col-md-3">
        {% include 'sidebar.html' %}
    </div>

    <!-- Main Content -->
    <div class="col-md-9">
        <div class="logs-header">
            <div class="d-flex justify-content-between align-items-start flex-wrap gap-3">
                <div>
                    <h1 class="logs-title">
                        <i class="fas fa-file-alt text-info"></i>
                        <span>Bot Activity</span>
                    </h1>
                    <p class="logs-subtitle">Monitor and manage your server's bot activity logs</p>
                </div>
                <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                </a>
            </div>
        </div>
        <!-- Statistics Cards -->
        <div class="row g-3 mb-4">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body text-center">
                        <h4 class="text-primary">{{ stats.total_logs }}</h4>
                        <small class="text-muted">Total Logs ({{ stats.period_description }})</small>
                    </div>
                </div>
            </div>
            {% for category_stat in stats.category_stats[:3] %}
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body text-center">
                        <h4 class="text-success">{{ category_stat.count }}</h4>
                        <small class="text-muted">{{ category_stat._id.title() }}</small>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Filters -->
        <div class="filter-section">
            <h3 class="section-title mb-3">
                <i class="fas fa-filter text-primary"></i>
                Filters & Actions
            </h3>

            <form method="GET">
                <div class="filter-controls">
                    <div>
                        <label for="category" class="form-label">
                            <i class="fas fa-tags me-2 text-info"></i>Category
                        </label>
                        <select class="form-select" id="category" name="category">
                            <option value="">All Categories</option>
                            <option value="repping" {% if current_category == 'repping' %}selected{% endif %}>Repping System</option>
                            <option value="vent" {% if current_category == 'vent' %}selected{% endif %}>Vent System</option>
                            <option value="tempvoice" {% if current_category == 'tempvoice' %}selected{% endif %}>Temp Voice</option>
                            <option value="sticky" {% if current_category == 'sticky' %}selected{% endif %}>Sticky Messages</option>
                            <option value="dm_support" {% if current_category == 'dm_support' %}selected{% endif %}>DM Support</option>
                            <option value="gender_verification" {% if current_category == 'gender_verification' %}selected{% endif %}>Gender Verification</option>
                            <option value="config" {% if current_category == 'config' %}selected{% endif %}>Configuration</option>
                            <option value="general" {% if current_category == 'general' %}selected{% endif %}>General</option>
                        </select>
                    </div>

                    <div>
                        <label for="limit" class="form-label">
                            <i class="fas fa-list-ol me-2 text-warning"></i>Limit
                        </label>
                        <select class="form-select" id="limit" name="limit">
                            <option value="50" {% if current_limit == 50 %}selected{% endif %}>50 logs</option>
                            <option value="100" {% if current_limit == 100 %}selected{% endif %}>100 logs</option>
                            <option value="250" {% if current_limit == 250 %}selected{% endif %}>250 logs</option>
                            <option value="500" {% if current_limit == 500 %}selected{% endif %}>500 logs</option>
                        </select>
                    </div>

                    <div class="btn-group-custom">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>Filter
                        </button>
                        <button type="button" class="btn btn-success" onclick="refreshLogs()">
                            <i class="fas fa-sync-alt me-1"></i>Refresh
                        </button>
                        <button type="button" class="btn btn-warning" onclick="cleanupLogs()">
                            <i class="fas fa-broom me-1"></i>Clean Up
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Logs Table -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">Activity Logs</h6>
                <span class="badge bg-info">{{ logs|length }} logs shown</span>
            </div>
            <div class="card-body p-0">
                {% if logs %}
                <div class="table-responsive">
                    <table class="table table-dark table-striped mb-0">
                        <thead>
                            <tr>
                                <th>Time</th>
                                <th>Category</th>
                                <th>User</th>
                                <th>Action</th>
                                <th>Details</th>
                            </tr>
                        </thead>
                        <tbody id="logs-tbody">
                            {% for log in logs %}
                            <tr class="log-row" style="cursor: pointer;" data-bs-toggle="collapse" data-bs-target="#log-details-{{ loop.index }}" aria-expanded="false">
                                <td>
                                    <small class="text-muted">
                                        {{ log.timestamp.strftime('%m/%d %H:%M:%S') }}
                                    </small>
                                </td>
                                <td>
                                    {% set category_colors = {
                                        'repping': 'warning',
                                        'vent': 'danger',
                                        'tempvoice': 'info',
                                        'sticky': 'success',
                                        'dm_support': 'primary',
                                        'gender_verification': 'secondary',
                                        'config': 'dark',
                                        'general': 'light'
                                    } %}
                                    <span class="badge bg-{{ category_colors.get(log.category, 'secondary') }}">
                                        {{ log.category.title() }}
                                    </span>
                                </td>
                                <td>
                                    <small>
                                        {{ log.username }}<br>
                                        <span class="text-muted">{{ log.user_id }}</span>
                                    </small>
                                </td>
                                <td>{{ log.action }}</td>
                                <td>
                                    {% if log.details %}
                                    <small class="text-muted">{{ log.details[:100] }}{% if log.details|length > 100 %}...{% endif %}</small>
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                    <i class="fas fa-chevron-down ms-2 text-muted"></i>
                                </td>
                            </tr>
                            <!-- Expandable details row -->
                            <tr class="collapse" id="log-details-{{ loop.index }}">
                                <td colspan="5" class="bg-dark">
                                    <div class="p-3">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <h6 class="text-info mb-2">📋 Log Details</h6>
                                                <p><strong>Full Timestamp:</strong> {{ log.timestamp.strftime('%Y-%m-%d %H:%M:%S UTC') }}</p>
                                                <p><strong>Category:</strong> {{ log.category }}</p>
                                                <p><strong>Log Type:</strong> {{ log.get('log_type', 'N/A') }}</p>
                                                {% if log.channel_id %}
                                                <p><strong>Channel ID:</strong> {{ log.channel_id }}</p>
                                                {% endif %}
                                            </div>
                                            <div class="col-md-6">
                                                <h6 class="text-info mb-2">👤 User Information</h6>
                                                <p><strong>Username:</strong> {{ log.username }}</p>
                                                <p><strong>User ID:</strong> {{ log.user_id }}</p>
                                                <p><strong>Action:</strong> {{ log.action }}</p>
                                            </div>
                                        </div>
                                        {% if log.details %}
                                        <div class="mt-3">
                                            <h6 class="text-info mb-2">📝 Full Details</h6>
                                            <div class="bg-secondary p-2 rounded">
                                                <code class="text-light">{{ log.details }}</code>
                                            </div>
                                        </div>
                                        {% endif %}
                                        {% if log.get('_id') %}
                                        <div class="mt-3">
                                            <h6 class="text-info mb-2">🔍 Technical Information</h6>
                                            <p><strong>Database ID:</strong> <code>{{ log._id }}</code></p>
                                            <p><strong>Server ID:</strong> {{ log.server_id }}</p>
                                        </div>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No logs found</h5>
                    <p class="text-muted">No activity logs match your current filters.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}



{% block scripts %}
<script>
// Handle log row expansion
document.addEventListener('DOMContentLoaded', function() {
    // Add click handlers for log rows
    document.querySelectorAll('.log-row').forEach(function(row) {
        row.addEventListener('click', function() {
            const chevron = this.querySelector('.fa-chevron-down');
            const isExpanded = this.getAttribute('aria-expanded') === 'true';

            // Update aria-expanded attribute
            this.setAttribute('aria-expanded', !isExpanded);

            // Rotate chevron
            if (chevron) {
                if (!isExpanded) {
                    chevron.style.transform = 'rotate(180deg)';
                } else {
                    chevron.style.transform = 'rotate(0deg)';
                }
            }
        });
    });

    // Handle collapse events to reset chevron
    document.querySelectorAll('.collapse').forEach(function(collapse) {
        collapse.addEventListener('hidden.bs.collapse', function() {
            const targetRow = document.querySelector(`[data-bs-target="#${this.id}"]`);
            if (targetRow) {
                const chevron = targetRow.querySelector('.fa-chevron-down');
                if (chevron) {
                    chevron.style.transform = 'rotate(0deg)';
                }
                targetRow.setAttribute('aria-expanded', 'false');
            }
        });

        collapse.addEventListener('shown.bs.collapse', function() {
            const targetRow = document.querySelector(`[data-bs-target="#${this.id}"]`);
            if (targetRow) {
                const chevron = targetRow.querySelector('.fa-chevron-down');
                if (chevron) {
                    chevron.style.transform = 'rotate(180deg)';
                }
                targetRow.setAttribute('aria-expanded', 'true');
            }
        });
    });
});

function refreshLogs() {
    // Get current filter values
    const category = document.getElementById('category').value;
    const limit = document.getElementById('limit').value;

    // Build query string
    const params = new URLSearchParams();
    if (category) params.append('category', category);
    if (limit) params.append('limit', limit);

    // Reload page with filters
    window.location.href = '{{ url_for("site_logs") }}?' + params.toString();
}

function cleanupLogs() {
    if (!confirm('This will delete old log entries, keeping only the most recent 100 logs. Continue?')) {
        return;
    }

    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Cleaning...';
    button.disabled = true;

    fetch('{{ url_for("api_cleanup_logs") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(`Successfully cleaned up ${data.deleted_count} old log entries.`);
            refreshLogs();
        } else {
            alert('Error cleaning up logs: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error cleaning up logs. Please try again.');
    })
    .finally(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

// Auto-refresh every 30 seconds
setInterval(function() {
    // Only refresh if we're still on the logs page
    if (window.location.pathname === '{{ url_for("site_logs") }}') {
        refreshLogs();
    }
}, 30000);
</script>
{% endblock %}
