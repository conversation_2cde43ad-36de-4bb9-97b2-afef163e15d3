{% extends "base.html" %}

{% block title %}Page Not Found - <PERSON><PERSON><PERSON><PERSON>{% endblock %}

{% block styles %}
<style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
    
    :root {
        --primary-color: #5865f2;
        --secondary-color: #7289da;
        --accent-color: #00d4ff;
        --bg-primary: #0a0a0f;
        --bg-secondary: #101218;
        --bg-tertiary: #1a1d29;
        --text-primary: #ffffff;
        --text-secondary: #b9bbbe;
        --text-muted: #72767d;
        --border-color: rgba(255, 255, 255, 0.1);
        --glow-primary: rgba(88, 101, 242, 0.5);
        --glow-accent: rgba(0, 212, 255, 0.3);
        --danger-color: #ef4444;
    }

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        background: var(--bg-primary);
        color: var(--text-primary);
        overflow-x: hidden;
        line-height: 1.6;
        min-height: 100vh;
        -webkit-text-size-adjust: 100%;
        -webkit-tap-highlight-color: transparent;
        touch-action: manipulation;
    }

    /* Animated Background */
    .animated-bg {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        background: linear-gradient(45deg, #0a0a0f 25%, transparent 25%),
                    linear-gradient(-45deg, #0a0a0f 25%, transparent 25%),
                    linear-gradient(45deg, transparent 75%, #0a0a0f 75%),
                    linear-gradient(-45deg, transparent 75%, #0a0a0f 75%);
        background-size: 60px 60px;
        background-position: 0 0, 0 30px, 30px -30px, -30px 0px;
        animation: backgroundMove 20s linear infinite;
        opacity: 0.03;
    }

    @keyframes backgroundMove {
        0% { background-position: 0 0, 0 30px, 30px -30px, -30px 0px; }
        100% { background-position: 60px 60px, 60px 90px, 90px 30px, 30px 60px; }
    }

    /* Main Content */
    .main-content {
        padding-top: 120px;
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .error-container {
        text-align: center;
        max-width: 600px;
        padding: 2rem;
    }

    .error-code {
        font-size: 8rem;
        font-weight: 900;
        background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        line-height: 1;
        margin-bottom: 1rem;
    }

    .error-title {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 1rem;
    }

    .error-description {
        font-size: 1.2rem;
        color: var(--text-secondary);
        margin-bottom: 2rem;
        line-height: 1.6;
    }

    .error-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }

    .btn-primary {
        padding: 12px 24px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: var(--text-primary);
        text-decoration: none;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 4px 15px rgba(88, 101, 242, 0.3);
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(88, 101, 242, 0.5);
        color: var(--text-primary);
        text-decoration: none;
    }

    .btn-secondary {
        padding: 12px 24px;
        background: rgba(255, 255, 255, 0.05);
        color: var(--text-secondary);
        text-decoration: none;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border: 1px solid var(--border-color);
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-secondary:hover {
        background: rgba(255, 255, 255, 0.1);
        color: var(--text-primary);
        text-decoration: none;
        transform: translateY(-2px);
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
        .error-code {
            font-size: 6rem;
        }

        .error-title {
            font-size: 2rem;
        }

        .error-description {
            font-size: 1rem;
        }

        .error-actions {
            flex-direction: column;
            align-items: center;
        }

        .btn-primary,
        .btn-secondary {
            width: 100%;
            max-width: 250px;
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Animated Background -->
<div class="animated-bg"></div>

<!-- Include Navbar -->
{% include 'navbar.html' %}

<div class="main-content">
    <div class="error-container">
        <div class="error-code">404</div>
        <h1 class="error-title">Page Not Found</h1>
        <p class="error-description">
            The page you're looking for doesn't exist or has been moved. 
            Don't worry, you can find what you're looking for from our homepage.
        </p>
        <div class="error-actions">
            <a href="{{ url_for('index') }}" class="btn-primary">
                <i class="fas fa-home"></i>
                Go Home
            </a>
            {% if 'discord_user' in session %}
            <a href="{{ url_for('select_server') }}" class="btn-secondary">
                <i class="fas fa-tachometer-alt"></i>
                Dashboard
            </a>
            {% else %}
            <a href="{{ url_for('login') }}" class="btn-secondary">
                <i class="fab fa-discord"></i>
                Login
            </a>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
