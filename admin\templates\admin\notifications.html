{% extends "admin/base.html" %}

{% block title %}Admin - Send Notifications{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Send Notifications</h5>
                </div>
                <div class="card-body">
                    <form id="notificationForm" class="needs-validation" novalidate>
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        
                        <div class="mb-3">
                            <label for="notificationTitle" class="form-label">Notification Title</label>
                            <input type="text" class="form-control" id="notificationTitle" required>
                            <div class="invalid-feedback">
                                Please provide a notification title.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="notificationMessage" class="form-label">Message</label>
                            <textarea class="form-control" id="notificationMessage" rows="3" required></textarea>
                            <div class="invalid-feedback">
                                Please provide a notification message.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="notificationType" class="form-label">Type</label>
                            <select class="form-select" id="notificationType">
                                <option value="info">Info</option>
                                <option value="success">Success</option>
                                <option value="warning">Warning</option>
                                <option value="error">Error</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="userId" class="form-label">User ID (Optional)</label>
                            <input type="text" class="form-control" id="userId" placeholder="Leave empty to send to all users">
                            <small class="text-muted">Enter a Discord User ID to send to a specific user, or leave empty to send to all users.</small>
                        </div>

                        <button type="submit" class="btn btn-primary">Send Notification</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('notificationForm');

    form.addEventListener('submit', async function(e) {
        e.preventDefault();

        if (!form.checkValidity()) {
            e.stopPropagation();
            form.classList.add('was-validated');
            return;
        }

        const data = {
            title: document.getElementById('notificationTitle').value,
            message: document.getElementById('notificationMessage').value,
            type: document.getElementById('notificationType').value,
            user_id: document.getElementById('userId').value || null
        };

        try {
            const response = await fetch('/api/send_notification', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': document.querySelector('input[name="csrf_token"]').value
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.success) {
                alert('Notification sent successfully!');
                form.reset();
                form.classList.remove('was-validated');
            } else {
                alert('Error sending notification: ' + (result.error || 'Unknown error'));
            }
        } catch (error) {
            console.error('Error:', error);
            alert('Error sending notification. Please try again.');
        }
    });
});
</script>
{% endblock %}
