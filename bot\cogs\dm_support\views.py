"""
Views for DM Support System
Contains Discord UI components for DM support
"""

import discord
from discord import ui, ButtonStyle
import logging
from datetime import datetime, timezone

logger = logging.getLogger(__name__)




class DMSupportConfirmView(ui.View):
    """View for confirming DM support ticket creation"""
    
    def __init__(self, guild_id: int, initial_message: str):
        super().__init__(timeout=300)  # 5 minute timeout
        self.guild_id = guild_id
        self.initial_message = initial_message

    @ui.button(emoji="✅", style=ButtonStyle.success, custom_id="dm_support_confirm")
    async def confirm_ticket(self, interaction: discord.Interaction, button: ui.Button):
        """Confirm ticket creation"""
        try:
            from main import bot, db
            
            # Get guild for name
            guild = bot.get_guild(self.guild_id)
            guild_name = guild.name if guild else "Unknown Server"

            # Create the ticket
            ticket_id = db.create_dm_support_ticket(
                self.guild_id,
                interaction.user.id,
                self.initial_message,
                guild_name
            )

            if ticket_id == "existing":
                await interaction.response.send_message("❌ You already have an open support ticket.", ephemeral=True)
                return

            # Get guild and settings
            guild = bot.get_guild(self.guild_id)
            if not guild:
                await interaction.response.send_message("❌ Server not found.", ephemeral=True)
                return

            dm_settings = db.get_dm_support_settings(self.guild_id)
            if not dm_settings:
                await interaction.response.send_message("❌ DM support is not configured for this server.", ephemeral=True)
                return

            # Create support channel
            await self.create_support_channel(interaction, guild, dm_settings, ticket_id)
            
        except Exception as e:
            logger.error(f"Error confirming DM support ticket: {e}")
            await interaction.response.send_message("❌ An error occurred while creating the ticket.", ephemeral=True)

    @ui.button(emoji="❌", style=ButtonStyle.danger, custom_id="dm_support_cancel")
    async def cancel_ticket(self, interaction: discord.Interaction, button: ui.Button):
        """Cancel ticket creation"""
        embed = discord.Embed(
            title="❌ Ticket Cancelled",
            description="Support ticket creation has been cancelled.",
            color=discord.Color.red()
        )
        await interaction.response.edit_message(embed=embed, view=None)

    async def create_support_channel(self, interaction, guild, dm_settings, ticket_id):
        """Create the support channel"""
        try:
            from main import db
            
            # Get category and support role
            category = guild.get_channel(dm_settings['category_id']) if dm_settings.get('category_id') else None
            support_role = guild.get_role(dm_settings['support_role_id']) if dm_settings.get('support_role_id') else None

            if not support_role:
                await interaction.response.send_message("❌ Support role not configured.", ephemeral=True)
                return

            # Create channel overwrites
            overwrites = {
                guild.default_role: discord.PermissionOverwrite(read_messages=False),
                support_role: discord.PermissionOverwrite(read_messages=True, send_messages=True),
                guild.me: discord.PermissionOverwrite(read_messages=True, send_messages=True)
            }

            channel_name = f"dm-{interaction.user.name}"
            channel = await guild.create_text_channel(
                name=channel_name[:100],
                category=category,
                overwrites=overwrites,
                topic=f"DM Support ticket for {interaction.user} (ID: {interaction.user.id})"
            )

            # Update ticket with channel ID
            db.update_dm_ticket_channel(ticket_id, channel.id)

            # Send initial message
            embed = discord.Embed(
                title="🎫 New DM Support Ticket",
                description=f"**User:** {interaction.user.mention} ({interaction.user})\n"
                           f"**User ID:** {interaction.user.id}\n\n"
                           f"**Initial Message:**\n{db.get_dm_ticket_transcript(ticket_id)['initial_message']}",
                color=discord.Color.green(),
                timestamp=datetime.now(timezone.utc)
            )
            embed.set_author(
                name=str(interaction.user),
                icon_url=interaction.user.display_avatar.url
            )
            embed.set_footer(text="Type =close <reason> to close this ticket")

            await channel.send(f"{support_role.mention}", embed=embed)

            # Log to dashboard
            db.log_bot_activity(
                guild.id,
                interaction.user.id,
                f"{interaction.user.name}#{interaction.user.discriminator}",
                "DM support ticket created",
                f"Channel: {channel.name}, Category: {category.name if category else 'None'}",
                "dm_support",
                channel.id
            )

            # Confirm to user
            embed = discord.Embed(
                title="✅ Support Ticket Created",
                description=f"Your support ticket has been created in **{guild.name}**.\n"
                           "The support team will respond to you here via DM.",
                color=discord.Color.green()
            )
            await interaction.response.edit_message(embed=embed, view=None)

        except Exception as e:
            logger.error(f"Error creating support channel: {e}")
            await interaction.response.send_message("❌ Failed to create support channel.", ephemeral=True)


class ServerSelectionView(ui.View):
    """View for selecting server for DM support"""
    
    def __init__(self, user_servers, initial_message):
        super().__init__(timeout=300)  # 5 minute timeout
        self.user_servers = user_servers[:5]  # Limit to 5 servers
        self.initial_message = initial_message

        # Add numbered buttons for each server
        for i, server in enumerate(self.user_servers, 1):
            button = ui.Button(
                label=str(i),
                style=ButtonStyle.primary,
                custom_id=f"server_select_{i}"
            )
            button.callback = self.create_server_callback(i-1)  # 0-based index
            self.add_item(button)

        # Add cancel button
        cancel_button = ui.Button(
            emoji="❌",
            style=ButtonStyle.danger,
            custom_id="server_select_cancel"
        )
        cancel_button.callback = self.cancel_callback
        self.add_item(cancel_button)

    def create_server_callback(self, server_index):
        """Create callback for server selection button"""
        async def server_callback(interaction):
            try:
                selected_server = self.user_servers[server_index]
                
                embed = discord.Embed(
                    title="🎫 Create Support Ticket",
                    description=f"Would you like to create a support ticket for **{selected_server.name}**?\n\n"
                               f"**Your message:**\n{self.initial_message}",
                    color=discord.Color.blue()
                )
                view = DMSupportConfirmView(selected_server.id, self.initial_message)
                await interaction.response.edit_message(embed=embed, view=view)
                
            except Exception as e:
                logger.error(f"Error in server selection callback: {e}")
                await interaction.response.send_message("❌ An error occurred.", ephemeral=True)
        
        return server_callback

    async def cancel_callback(self, interaction):
        """Cancel server selection"""
        embed = discord.Embed(
            title="❌ Cancelled",
            description="Server selection has been cancelled.",
            color=discord.Color.red()
        )
        await interaction.response.edit_message(embed=embed, view=None)
