"""
Shard Communication Service
Handles communication between the website and Discord bot shards
"""

import asyncio
import aiohttp
import logging
import os
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

class ShardCommunicationService:
    def __init__(self):
        # Load shard configuration from JSON file
        self.config_file = os.path.join(os.path.dirname(__file__), 'shard_config.json')
        self.config = self._load_shard_config()
        self.shard_urls = {}
        self.shard_api_keys = {}
        self.total_shards = self.config.get('total_shards', 1)
        self.timeout = self.config.get('request_timeout', 10)
        self.fallback_to_discord_api = self.config.get('fallback_to_discord_api', True)

        # Build shard mappings from config
        self._build_shard_mappings()

    def _load_shard_config(self) -> Dict:
        """Load shard configuration from JSON file"""
        try:
            with open(self.config_file, 'r') as f:
                config = json.load(f)
            logger.info(f"Loaded shard configuration from {self.config_file}")
            return config
        except FileNotFoundError:
            logger.error(f"Shard config file not found: {self.config_file}")
            return {"shards": [], "total_shards": 1, "request_timeout": 10}
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in shard config file: {e}")
            return {"shards": [], "total_shards": 1, "request_timeout": 10}

    def _build_shard_mappings(self):
        """Build shard URL and API key mappings from config"""
        for shard_config in self.config.get('shards', []):
            if shard_config.get('enabled', True):
                shard_id = shard_config['shard_id']
                self.shard_urls[shard_id] = shard_config['url']

                # Get API key from environment variable
                api_key_env = shard_config.get('api_key_env', f'SHARD_{shard_id}_API_KEY')
                api_key = os.getenv(api_key_env)

                if api_key:
                    self.shard_api_keys[shard_id] = api_key
                else:
                    logger.error(f"API key not found in environment variable: {api_key_env}")
                    continue  # Skip this shard if no API key

        logger.info(f"Loaded {len(self.shard_urls)} active shards: {list(self.shard_urls.keys())}")
        if len(self.shard_api_keys) != len(self.shard_urls):
            logger.warning(f"API keys missing for some shards. URLs: {len(self.shard_urls)}, Keys: {len(self.shard_api_keys)}")

    def reload_config(self):
        """Reload shard configuration from JSON file"""
        self.config = self._load_shard_config()
        self.shard_urls.clear()
        self.shard_api_keys.clear()
        self.total_shards = self.config.get('total_shards', 1)
        self.timeout = self.config.get('request_timeout', 10)
        self.fallback_to_discord_api = self.config.get('fallback_to_discord_api', True)
        self._build_shard_mappings()
        logger.info("Shard configuration reloaded")

    def get_active_shards(self) -> List[int]:
        """Get list of active shard IDs"""
        return list(self.shard_urls.keys())

    def get_shard_config(self, shard_id: int) -> Optional[Dict]:
        """Get configuration for a specific shard"""
        for shard_config in self.config.get('shards', []):
            if shard_config['shard_id'] == shard_id:
                return shard_config
        return None
    
    async def _make_shard_request(self, shard_id: int, endpoint: str, method: str = 'GET', data: Dict = None) -> Optional[Dict]:
        """Make a request to a specific shard"""
        if shard_id not in self.shard_urls:
            logger.error(f"No URL configured for shard {shard_id}")
            return None
        
        if shard_id not in self.shard_api_keys:
            logger.error(f"No API key configured for shard {shard_id}")
            return None
        
        url = f"{self.shard_urls[shard_id]}/api/{endpoint}"
        headers = {
            'Authorization': f'Bearer {self.shard_api_keys[shard_id]}',
            'Content-Type': 'application/json'
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                if method.upper() == 'GET':
                    async with session.get(url, headers=headers, timeout=aiohttp.ClientTimeout(total=self.timeout)) as response:
                        if response.status == 200:
                            return await response.json()
                        else:
                            logger.warning(f"Shard {shard_id} request failed: {response.status}")
                            return None
                elif method.upper() == 'POST':
                    async with session.post(url, headers=headers, json=data, timeout=aiohttp.ClientTimeout(total=self.timeout)) as response:
                        if response.status == 200:
                            return await response.json()
                        else:
                            logger.warning(f"Shard {shard_id} request failed: {response.status}")
                            return None
        except Exception as e:
            logger.error(f"Error making request to shard {shard_id}: {e}")
            return None
    
    def find_server_shard(self, server_id: str) -> Optional[int]:
        """Find which shard should contain a specific server using Discord's sharding formula"""
        try:
            server_id_int = int(server_id)

            # Use the configured total_shards, not just active shards
            if self.total_shards == 0:
                logger.error("Total shards is 0, cannot determine shard")
                return None

            # Discord's sharding formula: (guild_id >> 22) % total_shards
            shard_id = (server_id_int >> 22) % self.total_shards

            # Check if this shard is actually active/configured
            if shard_id in self.shard_urls:
                return shard_id
            else:
                logger.warning(f"Server {server_id} maps to shard {shard_id} but that shard is not active")
                return None

        except Exception as e:
            logger.error(f"Error finding server shard for {server_id}: {e}")
            return None
    
    async def get_server_info(self, server_id: str) -> Optional[Dict]:
        """Get server information from the appropriate shard"""
        shard_id = self.find_server_shard(server_id)
        if shard_id is None:
            logger.warning(f"Could not determine shard for server {server_id}")
            return None

        result = await self._make_shard_request(shard_id, f'server/{server_id}')
        if result is None and self.fallback_to_discord_api:
            logger.info(f"Shard {shard_id} unavailable for server {server_id}, falling back to Discord API")
            # Could implement Discord API fallback here if needed

        return result
    
    async def get_server_channels(self, server_id: str) -> Optional[List[Dict]]:
        """Get server channels from the appropriate shard"""
        shard_id = self.find_server_shard(server_id)
        if shard_id is None:
            return None
        
        result = await self._make_shard_request(shard_id, f'server/{server_id}/channels')
        return result.get('channels') if result else None
    
    async def get_server_members(self, server_id: str) -> Optional[List[Dict]]:
        """Get server members from the appropriate shard"""
        shard_id = self.find_server_shard(server_id)
        if shard_id is None:
            return None
        
        result = await self._make_shard_request(shard_id, f'server/{server_id}/members')
        return result.get('members') if result else None
    
    async def get_server_roles(self, server_id: str) -> Optional[List[Dict]]:
        """Get server roles from the appropriate shard"""
        shard_id = self.find_server_shard(server_id)
        if shard_id is None:
            return None
        
        result = await self._make_shard_request(shard_id, f'server/{server_id}/roles')
        return result.get('roles') if result else None
    
    async def check_bot_in_server(self, server_id: str) -> bool:
        """Check if bot is in the specified server"""
        shard_id = self.find_server_shard(server_id)
        if shard_id is None:
            return False
        
        result = await self._make_shard_request(shard_id, f'server/{server_id}/bot-status')
        return result.get('bot_in_server', False) if result else False
    
    async def send_message_to_channel(self, server_id: str, channel_id: str, content: str = None, embed: Dict = None) -> Optional[Dict]:
        """Send a message to a channel via the appropriate shard"""
        shard_id = self.find_server_shard(server_id)
        if shard_id is None:
            return None
        
        data = {
            'channel_id': channel_id,
            'content': content,
            'embed': embed
        }
        
        return await self._make_shard_request(shard_id, f'server/{server_id}/send-message', 'POST', data)
    
    async def create_giveaway_message(self, server_id: str, channel_id: str, giveaway_data: Dict) -> Optional[Dict]:
        """Create a giveaway message via the appropriate shard"""
        shard_id = self.find_server_shard(server_id)
        if shard_id is None:
            return None

        data = {
            'channel_id': channel_id,
            'giveaway_data': giveaway_data
        }

        return await self._make_shard_request(shard_id, f'server/{server_id}/create-giveaway', 'POST', data)

    async def delete_giveaway_message(self, server_id: str, channel_id: str, message_id: str) -> Optional[Dict]:
        """Delete a giveaway message via the appropriate shard"""
        shard_id = self.find_server_shard(server_id)
        if shard_id is None:
            return None

        data = {
            'channel_id': channel_id,
            'message_id': message_id
        }

        return await self._make_shard_request(shard_id, f'server/{server_id}/delete-giveaway-message', 'POST', data)

    async def end_giveaway_early(self, server_id: str, giveaway_id: str) -> Optional[Dict]:
        """End a giveaway early via the appropriate shard"""
        shard_id = self.find_server_shard(server_id)
        if shard_id is None:
            return None

        data = {
            'giveaway_id': giveaway_id
        }

        return await self._make_shard_request(shard_id, f'server/{server_id}/end-giveaway-early', 'POST', data)
    
    async def reroll_giveaway(self, server_id: str, giveaway_id: str) -> Optional[Dict]:
        """Reroll a giveaway via the appropriate shard"""
        shard_id = self.find_server_shard(server_id)
        if shard_id is None:
            return None
        
        data = {'giveaway_id': giveaway_id}
        return await self._make_shard_request(shard_id, f'server/{server_id}/reroll-giveaway', 'POST', data)
    
    async def get_current_repping_users(self, server_id: str) -> Optional[List[Dict]]:
        """Get current repping users from the appropriate shard"""
        shard_id = self.find_server_shard(server_id)
        if shard_id is None:
            return None
        
        result = await self._make_shard_request(shard_id, f'server/{server_id}/repping-users')
        return result.get('users') if result else None
    
    async def get_server_statistics(self, server_id: str) -> Optional[Dict]:
        """Get server statistics from the appropriate shard"""
        shard_id = self.find_server_shard(server_id)
        if shard_id is None:
            return None

        return await self._make_shard_request(shard_id, f'server/{server_id}/statistics')

    async def get_all_servers_from_shard(self, shard_id: int) -> Optional[List[Dict]]:
        """Get all servers from a specific shard"""
        result = await self._make_shard_request(shard_id, 'servers')
        return result.get('servers') if result else None

    async def get_all_servers_from_all_shards(self) -> List[Dict]:
        """Get all servers from all active shards"""
        all_servers = []

        for shard_id in self.get_active_shards():
            try:
                servers = await self.get_all_servers_from_shard(shard_id)
                if servers:
                    all_servers.extend(servers)
                    logger.debug(f"Got {len(servers)} servers from shard {shard_id}")
                else:
                    logger.warning(f"No servers returned from shard {shard_id}")
            except Exception as e:
                logger.error(f"Error getting servers from shard {shard_id}: {e}")
                continue

        logger.info(f"Total servers collected from all shards: {len(all_servers)}")
        return all_servers
    
    async def get_all_shard_statistics(self) -> Dict[int, Dict]:
        """Get statistics from all active shards simultaneously"""
        active_shards = self.get_active_shards()
        if not active_shards:
            logger.warning("No active shards found for statistics collection")
            return {}

        # Create tasks for all shards
        tasks = []
        for shard_id in active_shards:
            task = self._make_shard_request(shard_id, 'statistics')
            tasks.append((shard_id, task))

        # Execute all requests concurrently
        results = {}
        if tasks:
            # Wait for all tasks with timeout
            completed_tasks = await asyncio.gather(
                *[task for _, task in tasks],
                return_exceptions=True
            )

            # Process results
            for i, (shard_id, _) in enumerate(tasks):
                result = completed_tasks[i]
                if isinstance(result, Exception):
                    logger.error(f"Error getting statistics from shard {shard_id}: {result}")
                    results[shard_id] = None
                else:
                    results[shard_id] = result
                    if result:
                        logger.debug(f"Got statistics from shard {shard_id}: {result}")

        return results

    async def aggregate_global_statistics(self) -> Dict[str, Any]:
        """Aggregate statistics from all shards into global stats"""
        shard_stats = await self.get_all_shard_statistics()

        # Initialize aggregated stats
        aggregated = {
            'total_shards': len(self.get_active_shards()),
            'online_shards': 0,
            'total_servers': 0,
            'total_members': 0,
            'total_channels': 0,
            'total_roles': 0,
            'total_emojis': 0,
            'average_latency': 0,
            'last_updated': datetime.now(timezone.utc).isoformat(),
            'shard_details': {}
        }

        # Aggregate data from each shard
        latencies = []
        for shard_id, stats in shard_stats.items():
            if stats:
                aggregated['online_shards'] += 1
                aggregated['total_servers'] += stats.get('member_count', 0)  # This should be guild_count
                aggregated['total_members'] += stats.get('member_count', 0)
                aggregated['total_channels'] += stats.get('channel_count', 0)
                aggregated['total_roles'] += stats.get('role_count', 0)
                aggregated['total_emojis'] += stats.get('emoji_count', 0)

                # Collect latency for averaging
                if 'latency' in stats and stats['latency'] > 0:
                    latencies.append(stats['latency'])

                # Store individual shard details (convert shard_id to string for MongoDB)
                aggregated['shard_details'][str(shard_id)] = {
                    'status': 'online',
                    'servers': stats.get('guild_count', stats.get('member_count', 0)),
                    'members': stats.get('member_count', 0),
                    'latency': stats.get('latency', 0),
                    'last_seen': datetime.now(timezone.utc).isoformat()
                }
            else:
                # Shard is offline (convert shard_id to string for MongoDB)
                aggregated['shard_details'][str(shard_id)] = {
                    'status': 'offline',
                    'servers': 0,
                    'members': 0,
                    'latency': 0,
                    'last_seen': None
                }

        # Calculate average latency
        if latencies:
            aggregated['average_latency'] = round(sum(latencies) / len(latencies))

        logger.info(f"Aggregated statistics: {aggregated['online_shards']}/{aggregated['total_shards']} shards online, "
                   f"{aggregated['total_servers']} servers, {aggregated['total_members']} members")

        return aggregated

    def run_async(self, coro):
        """Helper to run async functions in sync context"""
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        return loop.run_until_complete(coro)

    def get_aggregated_stats(self) -> Dict:
        """Get aggregated statistics from all shards (synchronous wrapper for homepage)"""
        try:
            # Import here to avoid circular imports
            from statistics_aggregator import get_aggregator

            # Get cached statistics from the aggregator
            cached_stats = get_aggregator().get_cached_statistics()

            # Get shard data from shard data manager
            from shard_data import shard_data_manager
            shards = shard_data_manager.get_all_shards()

            # Build aggregated response similar to /api/shards endpoint
            total_servers = cached_stats.get('total_servers', 0)
            total_members = cached_stats.get('total_members', 0)

            # Extract server data from shards for homepage display
            all_servers = []
            for shard in shards:
                if shard.get('status') == 'operational' and shard.get('servers'):
                    all_servers.extend(shard.get('servers', []))

            return {
                'success': True,
                'total_shards': len(self.get_active_shards()),
                'operational_shards': len([s for s in shards if s.get('status') == 'operational']),
                'total_servers': total_servers,
                'total_members': total_members,
                'servers': total_servers,  # For compatibility
                'members': total_members,  # For compatibility
                'shards': shards,
                'all_servers': all_servers,
                'last_updated': cached_stats.get('last_updated')
            }

        except Exception as e:
            logger.error(f"Error getting aggregated stats: {e}")
            return {
                'success': False,
                'total_shards': 0,
                'operational_shards': 0,
                'total_servers': 0,
                'total_members': 0,
                'servers': 0,
                'members': 0,
                'shards': [],
                'all_servers': [],
                'error': str(e)
            }

# Global instance
shard_comm = ShardCommunicationService()
